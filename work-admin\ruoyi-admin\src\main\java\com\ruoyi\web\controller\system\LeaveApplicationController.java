package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.LeaveApplication;
import com.ruoyi.system.service.ILeaveApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 请假申请Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/leaveApplication")
public class LeaveApplicationController extends BaseController
{
    @Autowired
    private ILeaveApplicationService leaveApplicationService;

    /**
     * 查询请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeaveApplication leaveApplication)
    {
        startPage();
        List<LeaveApplication> list = leaveApplicationService.selectLeaveApplicationList(leaveApplication);
        return getDataTable(list);
    }

    /**
     * 查询我的请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:list')")
    @GetMapping("/myList")
    public TableDataInfo myList(LeaveApplication leaveApplication)
    {
        startPage();
        leaveApplication.setUserId(getUserId());
        List<LeaveApplication> list = leaveApplicationService.selectLeaveApplicationList(leaveApplication);
        return getDataTable(list);
    }

    /**
     * 查询待审批的请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:approve')")
    @GetMapping("/pendingList")
    public TableDataInfo pendingList(LeaveApplication leaveApplication)
    {
        startPage();
        List<LeaveApplication> list = leaveApplicationService.selectPendingLeaveApplicationList(leaveApplication);
        return getDataTable(list);
    }

    /**
     * 导出请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:export')")
    @Log(title = "请假申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LeaveApplication leaveApplication)
    {
        List<LeaveApplication> list = leaveApplicationService.selectLeaveApplicationList(leaveApplication);
        ExcelUtil<LeaveApplication> util = new ExcelUtil<LeaveApplication>(LeaveApplication.class);
        util.exportExcel(response, list, "请假申请数据");
    }

    /**
     * 获取请假申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(leaveApplicationService.selectLeaveApplicationById(id));
    }

    /**
     * 新增请假申请
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:add')")
    @Log(title = "请假申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveApplication leaveApplication)
    {
        leaveApplication.setCreateBy(getUsername());
        leaveApplication.setUserId(getUserId());
        leaveApplication.setUserName(getLoginUser().getUser().getNickName());
        return toAjax(leaveApplicationService.insertLeaveApplication(leaveApplication));
    }

    /**
     * 修改请假申请
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:edit')")
    @Log(title = "请假申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveApplication leaveApplication)
    {
        leaveApplication.setUpdateBy(getUsername());
        return toAjax(leaveApplicationService.updateLeaveApplication(leaveApplication));
    }

    /**
     * 删除请假申请
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:remove')")
    @Log(title = "请假申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveApplicationService.deleteLeaveApplicationByIds(ids));
    }

    /**
     * 校验申请单号
     */
    @PostMapping("/checkAppNoUnique")
    public AjaxResult checkAppNoUnique(@RequestBody LeaveApplication leaveApplication)
    {
        return success(leaveApplicationService.checkAppNoUnique(leaveApplication));
    }

    /**
     * 生成申请单号
     */
    @GetMapping("/generateAppNo")
    public AjaxResult generateAppNo()
    {
        return success(leaveApplicationService.generateAppNo());
    }

    /**
     * 审批请假申请
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:approve')")
    @Log(title = "请假申请审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public AjaxResult approve(@PathVariable Long id, @RequestParam Integer status, @RequestParam(required = false) String remark)
    {
        return toAjax(leaveApplicationService.approveLeaveApplication(id, status, remark));
    }

    /**
     * 撤销请假申请
     */
    @PreAuthorize("@ss.hasPermi('system:leaveApplication:cancel')")
    @Log(title = "请假申请撤销", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable Long id)
    {
        return toAjax(leaveApplicationService.cancelLeaveApplication(id));
    }

    /**
     * 根据用户ID和时间范围查询请假申请
     */
    @GetMapping("/getByUserAndDate")
    public AjaxResult getByUserAndDate(@RequestParam Long userId, @RequestParam String startDate, @RequestParam String endDate)
    {
        List<LeaveApplication> list = leaveApplicationService.selectLeaveApplicationByUserAndDate(userId, startDate, endDate);
        return success(list);
    }

    /**
     * 检查是否可以删除
     */
    @PostMapping("/checkCanDelete")
    public AjaxResult checkCanDelete(@RequestBody LeaveApplication leaveApplication)
    {
        LeaveApplication dbRecord = leaveApplicationService.selectLeaveApplicationById(leaveApplication.getId());
        if (dbRecord == null)
        {
            return error("请假申请不存在");
        }
        return success(leaveApplicationService.checkCanDelete(dbRecord));
    }

    /**
     * 检查是否可以修改
     */
    @PostMapping("/checkCanEdit")
    public AjaxResult checkCanEdit(@RequestBody LeaveApplication leaveApplication)
    {
        LeaveApplication dbRecord = leaveApplicationService.selectLeaveApplicationById(leaveApplication.getId());
        if (dbRecord == null)
        {
            return error("请假申请不存在");
        }
        return success(leaveApplicationService.checkCanEdit(dbRecord));
    }
}
