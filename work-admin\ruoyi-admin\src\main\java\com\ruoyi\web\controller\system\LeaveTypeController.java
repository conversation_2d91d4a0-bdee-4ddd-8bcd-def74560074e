package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.LeaveType;
import com.ruoyi.system.service.ILeaveTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 假期类型Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/leaveType")
public class LeaveTypeController extends BaseController
{
    @Autowired
    private ILeaveTypeService leaveTypeService;

    /**
     * 查询假期类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:list')")
    @GetMapping("/list")
    public TableDataInfo list(LeaveType leaveType)
    {
        startPage();
        List<LeaveType> list = leaveTypeService.selectLeaveTypeList(leaveType);
        return getDataTable(list);
    }

    /**
     * 查询所有假期类型选项（下拉框使用）
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:list')")
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<LeaveType> list = leaveTypeService.selectLeaveTypeAll();
        return success(list);
    }

    /**
     * 导出假期类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:export')")
    @Log(title = "假期类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LeaveType leaveType)
    {
        List<LeaveType> list = leaveTypeService.selectLeaveTypeList(leaveType);
        ExcelUtil<LeaveType> util = new ExcelUtil<LeaveType>(LeaveType.class);
        util.exportExcel(response, list, "假期类型数据");
    }

    /**
     * 获取假期类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(leaveTypeService.selectLeaveTypeById(id));
    }

    /**
     * 新增假期类型
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:add')")
    @Log(title = "假期类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveType leaveType)
    {
        if (!leaveTypeService.checkTypeCodeUnique(leaveType))
        {
            return error("新增假期类型'" + leaveType.getTypeName() + "'失败，假期代码已存在");
        }
        leaveType.setCreateBy(getUsername());
        return toAjax(leaveTypeService.insertLeaveType(leaveType));
    }

    /**
     * 修改假期类型
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:edit')")
    @Log(title = "假期类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveType leaveType)
    {
        if (!leaveTypeService.checkTypeCodeUnique(leaveType))
        {
            return error("修改假期类型'" + leaveType.getTypeName() + "'失败，假期代码已存在");
        }
        leaveType.setUpdateBy(getUsername());
        return toAjax(leaveTypeService.updateLeaveType(leaveType));
    }

    /**
     * 删除假期类型
     */
    @PreAuthorize("@ss.hasPermi('system:leaveType:remove')")
    @Log(title = "假期类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveTypeService.deleteLeaveTypeByIds(ids));
    }

    /**
     * 校验假期代码
     */
    @PostMapping("/checkTypeCodeUnique")
    public AjaxResult checkTypeCodeUnique(@RequestBody LeaveType leaveType)
    {
        return success(leaveTypeService.checkTypeCodeUnique(leaveType));
    }
} 