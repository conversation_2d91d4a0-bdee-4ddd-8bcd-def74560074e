package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ProcurementApplication;
import com.ruoyi.system.service.IProcurementApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采购申请Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/procurement")
public class ProcurementApplicationController extends BaseController
{
    @Autowired
    private IProcurementApplicationService procurementApplicationService;

    /**
     * 查询采购申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcurementApplication procurementApplication)
    {
        startPage();
        List<ProcurementApplication> list = procurementApplicationService.selectProcurementApplicationList(procurementApplication);
        return getDataTable(list);
    }

    /**
     * 导出采购申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:export')")
    @Log(title = "采购申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcurementApplication procurementApplication)
    {
        List<ProcurementApplication> list = procurementApplicationService.selectProcurementApplicationList(procurementApplication);
        ExcelUtil<ProcurementApplication> util = new ExcelUtil<ProcurementApplication>(ProcurementApplication.class);
        util.exportExcel(response, list, "采购申请数据");
    }

    /**
     * 获取采购申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(procurementApplicationService.selectProcurementApplicationById(id));
    }

    /**
     * 新增采购申请
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:add')")
    @Log(title = "采购申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcurementApplication procurementApplication)
    {
        return toAjax(procurementApplicationService.insertProcurementApplication(procurementApplication));
    }

    /**
     * 修改采购申请
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:edit')")
    @Log(title = "采购申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcurementApplication procurementApplication)
    {
        return toAjax(procurementApplicationService.updateProcurementApplication(procurementApplication));
    }

    /**
     * 删除采购申请
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:remove')")
    @Log(title = "采购申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(procurementApplicationService.deleteProcurementApplicationByIds(ids));
    }

    /**
     * 生成申请单号
     */
    @GetMapping("/generateAppNo")
    public AjaxResult generateAppNo()
    {
        return success(procurementApplicationService.generateAppNo());
    }

    /**
     * 提交采购申请
     */
    @PreAuthorize("@ss.hasPermi('system:procurement:edit')")
    @Log(title = "提交采购申请", businessType = BusinessType.UPDATE)
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody ProcurementApplication procurementApplication)
    {
        return toAjax(procurementApplicationService.submitProcurementApplication(procurementApplication));
    }

    /**
     * 根据申请单号查询采购申请
     */
    @GetMapping("/getByAppNo/{appNo}")
    public AjaxResult getByAppNo(@PathVariable("appNo") String appNo)
    {
        return success(procurementApplicationService.selectProcurementApplicationByAppNo(appNo));
    }
}