package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ApprovalTemplate;
import com.ruoyi.system.service.IApprovalTemplateService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 审批模板Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/template")
public class SysApprovalTemplateController extends BaseController
{
    @Autowired
    private IApprovalTemplateService approvalTemplateService;

    /**
     * 查询审批模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApprovalTemplate approvalTemplate)
    {
        startPage();
        List<ApprovalTemplate> list = approvalTemplateService.selectApprovalTemplateList(approvalTemplate);
        return getDataTable(list);
    }

    /**
     * 导出审批模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:export')")
    @Log(title = "审批模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApprovalTemplate approvalTemplate)
    {
        List<ApprovalTemplate> list = approvalTemplateService.selectApprovalTemplateList(approvalTemplate);
        ExcelUtil<ApprovalTemplate> util = new ExcelUtil<ApprovalTemplate>(ApprovalTemplate.class);
        util.exportExcel(response, list, "审批模板数据");
    }

    /**
     * 获取审批模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(approvalTemplateService.selectApprovalTemplateById(id));
    }

    /**
     * 新增审批模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "审批模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApprovalTemplate approvalTemplate)
    {
        return toAjax(approvalTemplateService.insertApprovalTemplate(approvalTemplate));
    }

    /**
     * 修改审批模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "审批模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApprovalTemplate approvalTemplate)
    {
        return toAjax(approvalTemplateService.updateApprovalTemplate(approvalTemplate));
    }

    /**
     * 删除审批模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "审批模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(approvalTemplateService.deleteApprovalTemplateByIds(ids));
    }

    /**
     * 设置默认模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "设置默认模板", businessType = BusinessType.UPDATE)
    @PutMapping("/setDefault/{id}")
    public AjaxResult setDefault(@PathVariable Long id)
    {
        return toAjax(approvalTemplateService.setDefaultTemplate(id));
    }

    /**
     * 改变模板状态
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "改变模板状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestParam Long id, @RequestParam Boolean status)
    {
        return toAjax(approvalTemplateService.changeTemplateStatus(id, status));
    }

    /**
     * 根据条件获取审批模板
     */
    @GetMapping("/getByCondition")
    public AjaxResult getByCondition(@RequestParam String businessType,
                                   @RequestParam(required = false) String procurementType,
                                   @RequestParam(required = false) java.math.BigDecimal amount,
                                   @RequestParam(required = false) Integer urgencyLevel)
    {
        ApprovalTemplate template = approvalTemplateService.getTemplateByCondition(businessType, procurementType, amount, urgencyLevel);
        return AjaxResult.success(template);
    }
}