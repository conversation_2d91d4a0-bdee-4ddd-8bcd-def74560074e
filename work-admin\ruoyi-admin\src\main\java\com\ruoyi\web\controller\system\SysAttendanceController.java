package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysAttendance;
import com.ruoyi.system.service.ISysAttendanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 考勤记录Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/attendance")
public class SysAttendanceController extends BaseController
{
    @Autowired
    private ISysAttendanceService sysAttendanceService;

    /**
     * 查询考勤记录列表
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAttendance sysAttendance)
    {
        startPage();
        List<SysAttendance> list = sysAttendanceService.selectSysAttendanceList(sysAttendance);
        return getDataTable(list);
    }

    /**
     * 导出考勤记录列表
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:export')")
    @Log(title = "考勤记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAttendance sysAttendance)
    {
        List<SysAttendance> list = sysAttendanceService.selectSysAttendanceList(sysAttendance);
        ExcelUtil<SysAttendance> util = new ExcelUtil<SysAttendance>(SysAttendance.class);
        util.exportExcel(response, list, "考勤记录数据");
    }

    /**
     * 获取考勤记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:query')")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(sysAttendanceService.selectSysAttendanceByAttendanceId(attendanceId));
    }

    /**
     * 新增考勤记录
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:add')")
    @Log(title = "考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAttendance sysAttendance)
    {
        return toAjax(sysAttendanceService.insertSysAttendance(sysAttendance));
    }

    /**
     * 修改考勤记录
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:edit')")
    @Log(title = "考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAttendance sysAttendance)
    {
        return toAjax(sysAttendanceService.updateSysAttendance(sysAttendance));
    }

    /**
     * 删除考勤记录
     */
	@PreAuthorize("@ss.hasPermi('attendance:record:remove')")
    @Log(title = "考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(sysAttendanceService.deleteSysAttendanceByAttendanceIds(attendanceIds));
    }

    /**
     * 获取当前用户今日考勤记录
     */
    @GetMapping("/today")
    public AjaxResult getTodayAttendance()
    {
        Long userId = SecurityUtils.getUserId();
        SysAttendance attendance = sysAttendanceService.selectTodayAttendanceByUserId(userId);
        return AjaxResult.success(attendance);
    }

    /**
     * 签到
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:clock')")
    @Log(title = "考勤签到", businessType = BusinessType.UPDATE)
    @PostMapping("/checkin")
    public AjaxResult checkIn()
    {
        Long userId = SecurityUtils.getUserId();
        // 检查是否已经签到
        SysAttendance todayAttendance = sysAttendanceService.selectTodayAttendanceByUserId(userId);
        if (todayAttendance != null && todayAttendance.getCheckInTime() != null) {
            return AjaxResult.error("今日已签到，请勿重复签到");
        }
        
        int result = sysAttendanceService.checkIn(userId);
        if (result > 0) {
            return AjaxResult.success("签到成功");
        } else {
            return AjaxResult.error("签到失败");
        }
    }

    /**
     * 签退
     */
    @PreAuthorize("@ss.hasPermi('attendance:record:clock')")
    @Log(title = "考勤签退", businessType = BusinessType.UPDATE)
    @PostMapping("/checkout")
    public AjaxResult checkOut()
    {
        Long userId = SecurityUtils.getUserId();
        // 检查是否已经签退
        SysAttendance todayAttendance = sysAttendanceService.selectTodayAttendanceByUserId(userId);
        if (todayAttendance == null || todayAttendance.getCheckInTime() == null) {
            return AjaxResult.error("请先签到后再签退");
        }
        if (todayAttendance.getCheckOutTime() != null) {
            return AjaxResult.error("今日已签退，请勿重复签退");
        }
        
        int result = sysAttendanceService.checkOut(userId);
        if (result > 0) {
            return AjaxResult.success("签退成功");
        } else {
            return AjaxResult.error("签退失败");
        }
    }
} 