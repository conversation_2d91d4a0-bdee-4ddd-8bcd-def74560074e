package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysAttendanceRule;
import com.ruoyi.system.service.ISysAttendanceRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 考勤规则Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/attendanceRule")
public class SysAttendanceRuleController extends BaseController
{
    @Autowired
    private ISysAttendanceRuleService sysAttendanceRuleService;

    /**
     * 查询考勤规则列表
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAttendanceRule sysAttendanceRule)
    {
        startPage();
        List<SysAttendanceRule> list = sysAttendanceRuleService.selectSysAttendanceRuleList(sysAttendanceRule);
        return getDataTable(list);
    }

    /**
     * 导出考勤规则列表
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:export')")
    @Log(title = "考勤规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAttendanceRule sysAttendanceRule)
    {
        List<SysAttendanceRule> list = sysAttendanceRuleService.selectSysAttendanceRuleList(sysAttendanceRule);
        ExcelUtil<SysAttendanceRule> util = new ExcelUtil<SysAttendanceRule>(SysAttendanceRule.class);
        util.exportExcel(response, list, "考勤规则数据");
    }

    /**
     * 获取考勤规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:query')")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        return AjaxResult.success(sysAttendanceRuleService.selectSysAttendanceRuleByRuleId(ruleId));
    }

    /**
     * 新增考勤规则
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:add')")
    @Log(title = "考勤规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAttendanceRule sysAttendanceRule)
    {
        return toAjax(sysAttendanceRuleService.insertSysAttendanceRule(sysAttendanceRule));
    }

    /**
     * 修改考勤规则
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:edit')")
    @Log(title = "考勤规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAttendanceRule sysAttendanceRule)
    {
        return toAjax(sysAttendanceRuleService.updateSysAttendanceRule(sysAttendanceRule));
    }

    /**
     * 删除考勤规则
     */
    @PreAuthorize("@ss.hasPermi('attendance:rule:remove')")
    @Log(title = "考勤规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleIds}")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(sysAttendanceRuleService.deleteSysAttendanceRuleByRuleIds(ruleIds));
    }
} 