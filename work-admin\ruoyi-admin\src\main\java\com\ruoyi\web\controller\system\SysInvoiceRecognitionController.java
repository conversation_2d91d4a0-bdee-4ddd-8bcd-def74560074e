package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.InvoiceRecognition;
import com.ruoyi.system.service.IInvoiceRecognitionService;

/**
 * 发票识别Controller
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/system/invoice")
public class SysInvoiceRecognitionController extends BaseController
{
    @Autowired
    private IInvoiceRecognitionService invoiceRecognitionService;

    /**
     * 查询发票识别列表
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(InvoiceRecognition invoiceRecognition)
    {
        startPage();
        List<InvoiceRecognition> list = invoiceRecognitionService.selectInvoiceRecognitionList(invoiceRecognition);
        return getDataTable(list);
    }

    /**
     * 导出发票识别列表
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:export')")
    @Log(title = "发票识别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoiceRecognition invoiceRecognition)
    {
        List<InvoiceRecognition> list = invoiceRecognitionService.selectInvoiceRecognitionList(invoiceRecognition);
        ExcelUtil<InvoiceRecognition> util = new ExcelUtil<InvoiceRecognition>(InvoiceRecognition.class);
        util.exportExcel(response, list, "发票识别数据");
    }

    /**
     * 获取发票识别详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(invoiceRecognitionService.selectInvoiceRecognitionById(id));
    }

    /**
     * 新增发票识别
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:add')")
    @Log(title = "发票识别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoiceRecognition invoiceRecognition)
    {
        return toAjax(invoiceRecognitionService.insertInvoiceRecognition(invoiceRecognition));
    }

    /**
     * 修改发票识别
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:edit')")
    @Log(title = "发票识别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoiceRecognition invoiceRecognition)
    {
        return toAjax(invoiceRecognitionService.updateInvoiceRecognition(invoiceRecognition));
    }

    /**
     * 删除发票识别
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:remove')")
    @Log(title = "发票识别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(invoiceRecognitionService.deleteInvoiceRecognitionByIds(ids));
    }

    /**
     * 上传并识别OFD发票
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:upload')")
    @Log(title = "发票识别", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public AjaxResult uploadAndRecognize(@RequestParam("file") MultipartFile file)
    {
        if (file.isEmpty())
        {
            return error("上传文件不能为空");
        }

        try
        {
            InvoiceRecognition result = invoiceRecognitionService.uploadAndRecognizeOfdInvoice(file);
            
            if ("1".equals(result.getStatus()))
            {
                return success("发票识别成功", result);
            }
            else if ("0".equals(result.getStatus()))
            {
                return success("发票识别中，请稍后查看结果", result);
            }
            else
            {
                return error("发票识别失败: " + result.getErrorMessage());
            }
        }
        catch (Exception e)
        {
            logger.error("发票识别异常", e);
            return error("发票识别异常: " + e.getMessage());
        }
    }

    /**
     * 通过URL识别OFD发票
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:recognize')")
    @Log(title = "发票识别", businessType = BusinessType.INSERT)
    @PostMapping("/recognizeByUrl")
    public AjaxResult recognizeByUrl(@RequestParam("fileUrl") String fileUrl)
    {
        if (StringUtils.isEmpty(fileUrl))
        {
            return error("文件URL不能为空");
        }

        try
        {
            InvoiceRecognition result = invoiceRecognitionService.recognizeOfdInvoiceByUrl(fileUrl);
            
            if ("1".equals(result.getStatus()))
            {
                return success("发票识别成功", result);
            }
            else if ("0".equals(result.getStatus()))
            {
                return success("发票识别中，请稍后查看结果", result);
            }
            else
            {
                return error("发票识别失败: " + result.getErrorMessage());
            }
        }
        catch (Exception e)
        {
            logger.error("发票识别异常", e);
            return error("发票识别异常: " + e.getMessage());
        }
    }

    /**
     * 通过Base64识别OFD发票
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:recognize')")
    @Log(title = "发票识别", businessType = BusinessType.INSERT)
    @PostMapping("/recognizeByBase64")
    public AjaxResult recognizeByBase64(@RequestParam("fileBase64") String fileBase64, 
                                       @RequestParam(value = "fileName", required = false) String fileName)
    {
        if (StringUtils.isEmpty(fileBase64))
        {
            return error("文件Base64编码不能为空");
        }

        try
        {
            InvoiceRecognition result = invoiceRecognitionService.recognizeOfdInvoiceByBase64(fileBase64, fileName);
            
            if ("1".equals(result.getStatus()))
            {
                return success("发票识别成功", result);
            }
            else if ("0".equals(result.getStatus()))
            {
                return success("发票识别中，请稍后查看结果", result);
            }
            else
            {
                return error("发票识别失败: " + result.getErrorMessage());
            }
        }
        catch (Exception e)
        {
            logger.error("发票识别异常", e);
            return error("发票识别异常: " + e.getMessage());
        }
    }

    /**
     * 重新识别发票
     */
    @PreAuthorize("@ss.hasPermi('system:invoice:recognize')")
    @Log(title = "发票识别", businessType = BusinessType.UPDATE)
    @PostMapping("/reRecognize/{id}")
    public AjaxResult reRecognize(@PathVariable("id") Long id)
    {
        try
        {
            InvoiceRecognition invoice = invoiceRecognitionService.selectInvoiceRecognitionById(id);
            if (invoice == null)
            {
                return error("发票记录不存在");
            }

            InvoiceRecognition result;
            if (StringUtils.isNotEmpty(invoice.getFilePath()) && invoice.getFilePath().startsWith("http"))
            {
                // 通过URL重新识别
                result = invoiceRecognitionService.recognizeOfdInvoiceByUrl(invoice.getFilePath());
            }
            else
            {
                return error("无法重新识别，缺少有效的文件路径");
            }

            // 更新识别结果
            result.setId(id);
            invoiceRecognitionService.updateInvoiceRecognition(result);

            if ("1".equals(result.getStatus()))
            {
                return success("发票重新识别成功", result);
            }
            else
            {
                return error("发票重新识别失败: " + result.getErrorMessage());
            }
        }
        catch (Exception e)
        {
            logger.error("发票重新识别异常", e);
            return error("发票重新识别异常: " + e.getMessage());
        }
    }
}
