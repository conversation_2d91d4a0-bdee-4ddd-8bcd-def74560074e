package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysPostLevel;
import com.ruoyi.system.service.ISysPostLevelService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 岗位级别工资Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/system/post/level")
public class SysPostLevelController extends BaseController
{
    @Autowired
    private ISysPostLevelService sysPostLevelService;

    /**
     * 查询岗位级别工资列表
     */
    @PreAuthorize("@ss.hasPermi('system:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPostLevel sysPostLevel)
    {
        startPage();
        List<SysPostLevel> list = sysPostLevelService.selectSysPostLevelList(sysPostLevel);
        return getDataTable(list);
    }

    /**
     * 根据岗位ID查询级别工资列表
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping("/listByPostId/{postId}")
    public AjaxResult listByPostId(@PathVariable("postId") Long postId)
    {
        List<SysPostLevel> list = sysPostLevelService.selectSysPostLevelByPostId(postId);
        return AjaxResult.success(list);
    }

    /**
     * 导出岗位级别工资列表
     */
    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @Log(title = "岗位级别工资", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPostLevel sysPostLevel)
    {
        List<SysPostLevel> list = sysPostLevelService.selectSysPostLevelList(sysPostLevel);
        ExcelUtil<SysPostLevel> util = new ExcelUtil<SysPostLevel>(SysPostLevel.class);
        util.exportExcel(response, list, "岗位级别工资数据");
    }

    /**
     * 获取岗位级别工资详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping(value = "/{levelId}")
    public AjaxResult getInfo(@PathVariable("levelId") Long levelId)
    {
        return AjaxResult.success(sysPostLevelService.selectSysPostLevelByLevelId(levelId));
    }

    /**
     * 新增岗位级别工资
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "岗位级别工资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPostLevel sysPostLevel)
    {
        if ("1".equals(sysPostLevelService.checkLevelCodeUnique(sysPostLevel)))
        {
            return AjaxResult.error("新增岗位级别'" + sysPostLevel.getLevelName() + "'失败，级别编码已存在");
        }
        return toAjax(sysPostLevelService.insertSysPostLevel(sysPostLevel));
    }

    /**
     * 修改岗位级别工资
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "岗位级别工资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPostLevel sysPostLevel)
    {
        if ("1".equals(sysPostLevelService.checkLevelCodeUnique(sysPostLevel)))
        {
            return AjaxResult.error("修改岗位级别'" + sysPostLevel.getLevelName() + "'失败，级别编码已存在");
        }
        return toAjax(sysPostLevelService.updateSysPostLevel(sysPostLevel));
    }

    /**
     * 删除岗位级别工资
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "岗位级别工资", businessType = BusinessType.DELETE)
    @DeleteMapping("/{levelIds}")
    public AjaxResult remove(@PathVariable Long[] levelIds)
    {
        return toAjax(sysPostLevelService.deleteSysPostLevelByLevelIds(levelIds));
    }

    /**
     * 批量保存岗位级别工资
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "批量保存岗位级别工资", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSave/{postId}")
    public AjaxResult batchSave(@PathVariable("postId") Long postId, @RequestBody List<SysPostLevel> postLevelList)
    {
        return toAjax(sysPostLevelService.batchSavePostLevels(postId, postLevelList));
    }
}