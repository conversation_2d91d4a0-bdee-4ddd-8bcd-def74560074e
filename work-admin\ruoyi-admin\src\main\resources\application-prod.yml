# 项目相关配置
ruoyi:
    # 名称
    name: RuoYi
    # 版本
    version: 3.8.7
    # 版权年份
    copyrightYear: 2024
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: /usr/local/organizationalSystem/uploadPath
    # 获取ip地址开关
    addressEnabled: false
    # 验证码类型 math 数字计算 char 字符验证
    captchaType: math

# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *******************************************************************************************************************************************************
                username: root
                password: PrZ=sP(ub0e!
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    redis:
        host: 127.0.0.1
        password: Pum=sP(ub0e!
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 7
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms

#飞书授权
feishu:
    #  client-id: cli_a641cb203b78d013
    #  client-secret: avYImYuKZYnAZ2ff9VG3JhS0uLxDePPS
    client-id: cli_a67905c156fed00c
    client-secret: FqQdUUVmcO3LGO3egkVLSsDU828isTAk
    redirect-uri: http://45s7p8.natappfree.cc/oauth/feishu/callback
    app-token-key: feishuAppTokenKey

#统一身份认证参数
integrated:
    token-url: https://ua.sdicin.com/idp/oauth2/getToken
    userinfo-url: https://ua.sdicin.com/idp/oauth2/getUserInfo
    #  token-url: https://tauth.sdic.com.cn/idp/oauth2/getToken
    #  userinfo-url: https://tauth.sdic.com.cn/idp/oauth2/getUserInfo
    client-id: SDICPowerWT
    client-secret: 61b2f5f7ff144d1fb042834fde6b3d5d
#  client-secret: db73cbdb260e4b5e8d9b7c02b8ddeb27

# 因为web端和移动端访问上传文件的地址不同，需要进行转换
upload:
    app-url: https://fssdicpower-wt.sdic.com.cn/system-api/
    web-url: https://wt.sdicpower.com/system-api/
