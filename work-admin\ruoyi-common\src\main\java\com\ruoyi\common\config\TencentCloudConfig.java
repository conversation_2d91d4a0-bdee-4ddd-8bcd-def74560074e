package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 腾讯云配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "tencent.cloud")
public class TencentCloudConfig
{
    /** 腾讯云SecretId */
    private String secretId;

    /** 腾讯云SecretKey */
    private String secretKey;

    /** 腾讯云OCR服务地域 */
    private String region = "ap-beijing";

    /** OCR服务端点 */
    private String endpoint = "ocr.tencentcloudapi.com";

    public String getSecretId()
    {
        return secretId;
    }

    public void setSecretId(String secretId)
    {
        this.secretId = secretId;
    }

    public String getSecretKey()
    {
        return secretKey;
    }

    public void setSecretKey(String secretKey)
    {
        this.secretKey = secretKey;
    }

    public String getRegion()
    {
        return region;
    }

    public void setRegion(String region)
    {
        this.region = region;
    }

    public String getEndpoint()
    {
        return endpoint;
    }

    public void setEndpoint(String endpoint)
    {
        this.endpoint = endpoint;
    }
}
