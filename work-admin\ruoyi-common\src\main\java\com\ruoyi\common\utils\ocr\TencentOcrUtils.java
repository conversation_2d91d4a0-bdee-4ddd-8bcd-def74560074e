package com.ruoyi.common.utils.ocr;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ruoyi.common.config.TencentCloudConfig;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.InvoiceGoodsInfo;
import com.ruoyi.system.domain.InvoiceRecognition;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.VerifyOfdVatInvoiceOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.VerifyOfdVatInvoiceOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.VatInvoiceGoodsInfo;

/**
 * 腾讯云OCR工具类
 * 
 * <AUTHOR>
 */
@Component
public class TencentOcrUtils
{
    private static final Logger log = LoggerFactory.getLogger(TencentOcrUtils.class);

    @Autowired
    private TencentCloudConfig tencentCloudConfig;

    /**
     * OFD发票识别
     * 
     * @param ofdFileUrl OFD文件的URL地址
     * @return 发票识别结果
     */
    public InvoiceRecognition recognizeOfdInvoice(String ofdFileUrl)
    {
        return recognizeOfdInvoice(ofdFileUrl, null, 1);
    }

    /**
     * OFD发票识别
     * 
     * @param ofdFileUrl OFD文件的URL地址
     * @param ofdFileBase64 OFD文件的Base64编码
     * @param pageNumber 页码，默认为1
     * @return 发票识别结果
     */
    public InvoiceRecognition recognizeOfdInvoice(String ofdFileUrl, String ofdFileBase64, Integer pageNumber)
    {
        InvoiceRecognition result = new InvoiceRecognition();
        result.setStatus("0"); // 识别中
        
        try
        {
            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
            Credential cred = new Credential(tencentCloudConfig.getSecretId(), tencentCloudConfig.getSecretKey());
            
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(tencentCloudConfig.getEndpoint());
            
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            
            // 实例化要请求产品的client对象，clientProfile是可选的
            OcrClient client = new OcrClient(cred, tencentCloudConfig.getRegion(), clientProfile);
            
            // 实例化一个请求对象，每个接口都会对应一个request对象
            VerifyOfdVatInvoiceOCRRequest req = new VerifyOfdVatInvoiceOCRRequest();
            
            if (StringUtils.isNotEmpty(ofdFileUrl))
            {
                req.setOfdFileUrl(ofdFileUrl);
            }
            else if (StringUtils.isNotEmpty(ofdFileBase64))
            {
                req.setOfdFileBase64(ofdFileBase64);
            }
            else
            {
                result.setStatus("2"); // 识别失败
                result.setErrorMessage("OFD文件URL和Base64编码不能同时为空");
                return result;
            }
            
            if (pageNumber != null && pageNumber > 0)
            {
                req.setOfdPageNumber(pageNumber.longValue());
            }
            
            // 返回的resp是一个VerifyOfdVatInvoiceOCRResponse的实例，与请求对象对应
            VerifyOfdVatInvoiceOCRResponse resp = client.VerifyOfdVatInvoiceOCR(req);
            
            // 解析响应结果
            parseInvoiceResponse(resp, result);
            
            result.setStatus("1"); // 识别成功
            log.info("OFD发票识别成功: {}", result.getInvoiceNumber());
            
        }
        catch (TencentCloudSDKException e)
        {
            log.error("腾讯云OCR识别失败", e);
            result.setStatus("2"); // 识别失败
            result.setErrorMessage("识别失败: " + e.getMessage());
        }
        catch (Exception e)
        {
            log.error("发票识别异常", e);
            result.setStatus("2"); // 识别失败
            result.setErrorMessage("识别异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 解析发票识别响应结果
     * 
     * @param resp 腾讯云响应
     * @param result 发票识别结果对象
     */
    private void parseInvoiceResponse(VerifyOfdVatInvoiceOCRResponse resp, InvoiceRecognition result)
    {
        // 基本信息
        result.setInvoiceType(resp.getType());
        result.setInvoiceCode(resp.getInvoiceCode());
        result.setInvoiceNumber(resp.getInvoiceNumber());
        result.setCheckCode(resp.getInvoiceCheckCode());
        result.setMachineNumber(resp.getMachineNumber());
        result.setTaxControlCode(resp.getTaxControlCode());
        result.setInvoiceTitle(resp.getInvoiceTitle());
        result.setNote(resp.getNote());
        
        // 开票日期解析
        if (StringUtils.isNotEmpty(resp.getIssueDate()))
        {
            result.setIssueDate(parseDate(resp.getIssueDate()));
        }
        
        // 购买方信息
        if (resp.getBuyer() != null)
        {
            result.setBuyerName(resp.getBuyer().getName());
            result.setBuyerTaxId(resp.getBuyer().getTaxId());
            result.setBuyerAddrTel(resp.getBuyer().getAddrTel());
            result.setBuyerFinancialAccount(resp.getBuyer().getFinancialAccount());
        }
        
        // 销售方信息
        if (resp.getSeller() != null)
        {
            result.setSellerName(resp.getSeller().getName());
            result.setSellerTaxId(resp.getSeller().getTaxId());
            result.setSellerAddrTel(resp.getSeller().getAddrTel());
            result.setSellerFinancialAccount(resp.getSeller().getFinancialAccount());
        }
        
        // 金额信息
        result.setTotalAmount(parseBigDecimal(resp.getTaxInclusiveTotalAmount()));
        result.setTaxAmount(parseBigDecimal(resp.getTaxTotalAmount()));
        result.setAmountWithoutTax(parseBigDecimal(resp.getTaxExclusiveTotalAmount()));
        
        // 人员信息
        result.setInvoiceClerk(resp.getInvoiceClerk());
        result.setPayee(resp.getPayee());
        result.setChecker(resp.getChecker());
        
        // 货物或服务清单
        if (resp.getGoodsInfos() != null && resp.getGoodsInfos().length > 0)
        {
            List<InvoiceGoodsInfo> goodsList = new ArrayList<>();
            for (VatInvoiceGoodsInfo goodsInfo : resp.getGoodsInfos())
            {
                InvoiceGoodsInfo goods = new InvoiceGoodsInfo();
                goods.setItem(goodsInfo.getItem());
                goods.setSpecification(goodsInfo.getSpecification());
                goods.setMeasurementDimension(goodsInfo.getMeasurementDimension());
                goods.setQuantity(goodsInfo.getQuantity());
                goods.setPrice(goodsInfo.getPrice());
                goods.setAmount(goodsInfo.getAmount());
                goods.setTaxScheme(goodsInfo.getTaxScheme());
                goods.setTaxAmount(goodsInfo.getTaxAmount());
                goodsList.add(goods);
            }
            result.setGoodsInfoList(goodsList);
        }
    }

    /**
     * 解析日期字符串
     * 
     * @param dateStr 日期字符串
     * @return Date对象
     */
    private Date parseDate(String dateStr)
    {
        if (StringUtils.isEmpty(dateStr))
        {
            return null;
        }
        
        try
        {
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy年MM月dd日",
                "yyyy-MM-dd",
                "yyyy/MM/dd",
                "yyyyMMdd"
            };
            
            for (String pattern : patterns)
            {
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    return sdf.parse(dateStr);
                }
                catch (ParseException e)
                {
                    // 继续尝试下一种格式
                }
            }
        }
        catch (Exception e)
        {
            log.warn("日期解析失败: {}", dateStr, e);
        }
        
        return null;
    }

    /**
     * 解析BigDecimal
     * 
     * @param amountStr 金额字符串
     * @return BigDecimal对象
     */
    private BigDecimal parseBigDecimal(String amountStr)
    {
        if (StringUtils.isEmpty(amountStr))
        {
            return null;
        }
        
        try
        {
            // 移除可能的符号和空格
            String cleanAmount = amountStr.replaceAll("[^\\d.-]", "");
            return new BigDecimal(cleanAmount);
        }
        catch (Exception e)
        {
            log.warn("金额解析失败: {}", amountStr, e);
            return null;
        }
    }
}
