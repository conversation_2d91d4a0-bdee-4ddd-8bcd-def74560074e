package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 审批步骤模板对象 approval_step_template
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ApprovalStepTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 步骤ID */
    private Long id;

    /** 模板ID */
    @Excel(name = "模板ID")
    private Long templateId;

    /** 步骤序号 */
    @Excel(name = "步骤序号")
    private Integer stepNo;

    /** 步骤名称 */
    @Excel(name = "步骤名称")
    private String stepName;

    /** 步骤类型 */
    @Excel(name = "步骤类型")
    private String stepType;

    /** 审批人类型 */
    @Excel(name = "审批人类型")
    private String approverType;

    /** 审批人值 */
    @Excel(name = "审批人值")
    private String approverValue;

    /** 审批策略 */
    @Excel(name = "审批策略")
    private String approverPolicy;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTemplateId(Long templateId) 
    {
        this.templateId = templateId;
    }

    public Long getTemplateId() 
    {
        return templateId;
    }
    public void setStepNo(Integer stepNo) 
    {
        this.stepNo = stepNo;
    }

    public Integer getStepNo() 
    {
        return stepNo;
    }
    public void setStepName(String stepName) 
    {
        this.stepName = stepName;
    }

    public String getStepName() 
    {
        return stepName;
    }
    public void setStepType(String stepType) 
    {
        this.stepType = stepType;
    }

    public String getStepType() 
    {
        return stepType;
    }
    public void setApproverType(String approverType) 
    {
        this.approverType = approverType;
    }

    public String getApproverType() 
    {
        return approverType;
    }
    public void setApproverValue(String approverValue) 
    {
        this.approverValue = approverValue;
    }

    public String getApproverValue() 
    {
        return approverValue;
    }
    public void setApproverPolicy(String approverPolicy) 
    {
        this.approverPolicy = approverPolicy;
    }

    public String getApproverPolicy() 
    {
        return approverPolicy;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("templateId", getTemplateId())
            .append("stepNo", getStepNo())
            .append("stepName", getStepName())
            .append("stepType", getStepType())
            .append("approverType", getApproverType())
            .append("approverValue", getApproverValue())
            .append("approverPolicy", getApproverPolicy())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}