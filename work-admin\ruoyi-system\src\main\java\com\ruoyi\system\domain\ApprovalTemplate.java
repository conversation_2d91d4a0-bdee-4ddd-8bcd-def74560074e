package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 审批模板对象 approval_template
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ApprovalTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Long id;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String templateName;

    /** 模板代码 */
    @Excel(name = "模板代码")
    private String templateCode;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 采购类型 */
    @Excel(name = "采购类型")
    private String procurementType;

    /** 最小金额 */
    @Excel(name = "最小金额")
    private BigDecimal minAmount;

    /** 最大金额 */
    @Excel(name = "最大金额")
    private BigDecimal maxAmount;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevels;

    /** 是否默认 */
    @Excel(name = "是否默认")
    private Boolean isDefault;

    /** 是否激活 */
    @Excel(name = "是否激活")
    private Boolean isActive;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 模板描述 */
    @Excel(name = "模板描述")
    private String description;

    /** 审批步骤列表 */
    private List<ApprovalStepTemplate> approvalStepTemplateList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTemplateName(String templateName) 
    {
        this.templateName = templateName;
    }

    public String getTemplateName() 
    {
        return templateName;
    }
    public void setTemplateCode(String templateCode) 
    {
        this.templateCode = templateCode;
    }

    public String getTemplateCode() 
    {
        return templateCode;
    }
    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }
    public void setProcurementType(String procurementType) 
    {
        this.procurementType = procurementType;
    }

    public String getProcurementType() 
    {
        return procurementType;
    }
    public void setMinAmount(BigDecimal minAmount) 
    {
        this.minAmount = minAmount;
    }

    public BigDecimal getMinAmount() 
    {
        return minAmount;
    }
    public void setMaxAmount(BigDecimal maxAmount) 
    {
        this.maxAmount = maxAmount;
    }

    public BigDecimal getMaxAmount() 
    {
        return maxAmount;
    }
    public void setUrgencyLevels(String urgencyLevels) 
    {
        this.urgencyLevels = urgencyLevels;
    }

    public String getUrgencyLevels() 
    {
        return urgencyLevels;
    }
    public void setIsDefault(Boolean isDefault) 
    {
        this.isDefault = isDefault;
    }

    public Boolean getIsDefault() 
    {
        return isDefault;
    }
    public void setIsActive(Boolean isActive) 
    {
        this.isActive = isActive;
    }

    public Boolean getIsActive() 
    {
        return isActive;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public List<ApprovalStepTemplate> getApprovalStepTemplateList()
    {
        return approvalStepTemplateList;
    }

    public void setApprovalStepTemplateList(List<ApprovalStepTemplate> approvalStepTemplateList)
    {
        this.approvalStepTemplateList = approvalStepTemplateList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("templateName", getTemplateName())
            .append("templateCode", getTemplateCode())
            .append("businessType", getBusinessType())
            .append("procurementType", getProcurementType())
            .append("minAmount", getMinAmount())
            .append("maxAmount", getMaxAmount())
            .append("urgencyLevels", getUrgencyLevels())
            .append("isDefault", getIsDefault())
            .append("isActive", getIsActive())
            .append("sortOrder", getSortOrder())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("approvalStepTemplateList", getApprovalStepTemplateList())
            .toString();
    }
}