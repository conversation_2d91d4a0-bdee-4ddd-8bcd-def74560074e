package com.ruoyi.system.domain;

import java.math.BigDecimal;
import com.ruoyi.common.annotation.Excel;

/**
 * 发票商品信息对象
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public class InvoiceGoodsInfo
{
    /** 货物或应税劳务、服务名称 */
    @Excel(name = "货物或应税劳务、服务名称")
    private String item;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String measurementDimension;

    /** 数量 */
    @Excel(name = "数量")
    private String quantity;

    /** 单价 */
    @Excel(name = "单价")
    private String price;

    /** 金额 */
    @Excel(name = "金额")
    private String amount;

    /** 税率 */
    @Excel(name = "税率")
    private String taxScheme;

    /** 税额 */
    @Excel(name = "税额")
    private String taxAmount;

    public void setItem(String item) 
    {
        this.item = item;
    }

    public String getItem() 
    {
        return item;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setMeasurementDimension(String measurementDimension) 
    {
        this.measurementDimension = measurementDimension;
    }

    public String getMeasurementDimension() 
    {
        return measurementDimension;
    }

    public void setQuantity(String quantity) 
    {
        this.quantity = quantity;
    }

    public String getQuantity() 
    {
        return quantity;
    }

    public void setPrice(String price) 
    {
        this.price = price;
    }

    public String getPrice() 
    {
        return price;
    }

    public void setAmount(String amount) 
    {
        this.amount = amount;
    }

    public String getAmount() 
    {
        return amount;
    }

    public void setTaxScheme(String taxScheme) 
    {
        this.taxScheme = taxScheme;
    }

    public String getTaxScheme() 
    {
        return taxScheme;
    }

    public void setTaxAmount(String taxAmount) 
    {
        this.taxAmount = taxAmount;
    }

    public String getTaxAmount() 
    {
        return taxAmount;
    }

    @Override
    public String toString() {
        return "InvoiceGoodsInfo{" +
                "item='" + item + '\'' +
                ", specification='" + specification + '\'' +
                ", measurementDimension='" + measurementDimension + '\'' +
                ", quantity='" + quantity + '\'' +
                ", price='" + price + '\'' +
                ", amount='" + amount + '\'' +
                ", taxScheme='" + taxScheme + '\'' +
                ", taxAmount='" + taxAmount + '\'' +
                '}';
    }
}
