package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 发票识别对象 invoice_recognition
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public class InvoiceRecognition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 发票类型 */
    @Excel(name = "发票类型")
    private String invoiceType;

    /** 发票代码 */
    @Excel(name = "发票代码")
    private String invoiceCode;

    /** 发票号码 */
    @Excel(name = "发票号码")
    private String invoiceNumber;

    /** 开票日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开票日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueDate;

    /** 验证码 */
    @Excel(name = "验证码")
    private String checkCode;

    /** 机器编号 */
    @Excel(name = "机器编号")
    private String machineNumber;

    /** 密码区 */
    @Excel(name = "密码区")
    private String taxControlCode;

    /** 购买方名称 */
    @Excel(name = "购买方名称")
    private String buyerName;

    /** 购买方纳税人识别号 */
    @Excel(name = "购买方纳税人识别号")
    private String buyerTaxId;

    /** 购买方地址电话 */
    @Excel(name = "购买方地址电话")
    private String buyerAddrTel;

    /** 购买方开户行及账号 */
    @Excel(name = "购买方开户行及账号")
    private String buyerFinancialAccount;

    /** 销售方名称 */
    @Excel(name = "销售方名称")
    private String sellerName;

    /** 销售方纳税人识别号 */
    @Excel(name = "销售方纳税人识别号")
    private String sellerTaxId;

    /** 销售方地址电话 */
    @Excel(name = "销售方地址电话")
    private String sellerAddrTel;

    /** 销售方开户行及账号 */
    @Excel(name = "销售方开户行及账号")
    private String sellerFinancialAccount;

    /** 价税合计 */
    @Excel(name = "价税合计")
    private BigDecimal totalAmount;

    /** 税额 */
    @Excel(name = "税额")
    private BigDecimal taxAmount;

    /** 不含税金额 */
    @Excel(name = "不含税金额")
    private BigDecimal amountWithoutTax;

    /** 开票人 */
    @Excel(name = "开票人")
    private String invoiceClerk;

    /** 收款人 */
    @Excel(name = "收款人")
    private String payee;

    /** 复核人 */
    @Excel(name = "复核人")
    private String checker;

    /** 备注 */
    @Excel(name = "备注")
    private String note;

    /** 发票标题 */
    @Excel(name = "发票标题")
    private String invoiceTitle;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String originalFileName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 识别状态 0-识别中 1-识别成功 2-识别失败 */
    @Excel(name = "识别状态", readConverterExp = "0=识别中,1=识别成功,2=识别失败")
    private String status;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 货物或服务清单JSON */
    private String goodsInfoJson;

    /** 货物或服务清单 */
    private List<InvoiceGoodsInfo> goodsInfoList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInvoiceType(String invoiceType) 
    {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceType() 
    {
        return invoiceType;
    }

    public void setInvoiceCode(String invoiceCode) 
    {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceCode() 
    {
        return invoiceCode;
    }

    public void setInvoiceNumber(String invoiceNumber) 
    {
        this.invoiceNumber = invoiceNumber;
    }

    public String getInvoiceNumber() 
    {
        return invoiceNumber;
    }

    public void setIssueDate(Date issueDate) 
    {
        this.issueDate = issueDate;
    }

    public Date getIssueDate() 
    {
        return issueDate;
    }

    public void setCheckCode(String checkCode) 
    {
        this.checkCode = checkCode;
    }

    public String getCheckCode() 
    {
        return checkCode;
    }

    public void setMachineNumber(String machineNumber) 
    {
        this.machineNumber = machineNumber;
    }

    public String getMachineNumber() 
    {
        return machineNumber;
    }

    public void setTaxControlCode(String taxControlCode) 
    {
        this.taxControlCode = taxControlCode;
    }

    public String getTaxControlCode() 
    {
        return taxControlCode;
    }

    public void setBuyerName(String buyerName) 
    {
        this.buyerName = buyerName;
    }

    public String getBuyerName() 
    {
        return buyerName;
    }

    public void setBuyerTaxId(String buyerTaxId) 
    {
        this.buyerTaxId = buyerTaxId;
    }

    public String getBuyerTaxId() 
    {
        return buyerTaxId;
    }

    public void setBuyerAddrTel(String buyerAddrTel) 
    {
        this.buyerAddrTel = buyerAddrTel;
    }

    public String getBuyerAddrTel() 
    {
        return buyerAddrTel;
    }

    public void setBuyerFinancialAccount(String buyerFinancialAccount) 
    {
        this.buyerFinancialAccount = buyerFinancialAccount;
    }

    public String getBuyerFinancialAccount() 
    {
        return buyerFinancialAccount;
    }

    public void setSellerName(String sellerName) 
    {
        this.sellerName = sellerName;
    }

    public String getSellerName() 
    {
        return sellerName;
    }

    public void setSellerTaxId(String sellerTaxId) 
    {
        this.sellerTaxId = sellerTaxId;
    }

    public String getSellerTaxId() 
    {
        return sellerTaxId;
    }

    public void setSellerAddrTel(String sellerAddrTel) 
    {
        this.sellerAddrTel = sellerAddrTel;
    }

    public String getSellerAddrTel() 
    {
        return sellerAddrTel;
    }

    public void setSellerFinancialAccount(String sellerFinancialAccount) 
    {
        this.sellerFinancialAccount = sellerFinancialAccount;
    }

    public String getSellerFinancialAccount() 
    {
        return sellerFinancialAccount;
    }

    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) 
    {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getTaxAmount() 
    {
        return taxAmount;
    }

    public void setAmountWithoutTax(BigDecimal amountWithoutTax) 
    {
        this.amountWithoutTax = amountWithoutTax;
    }

    public BigDecimal getAmountWithoutTax() 
    {
        return amountWithoutTax;
    }

    public void setInvoiceClerk(String invoiceClerk) 
    {
        this.invoiceClerk = invoiceClerk;
    }

    public String getInvoiceClerk() 
    {
        return invoiceClerk;
    }

    public void setPayee(String payee) 
    {
        this.payee = payee;
    }

    public String getPayee() 
    {
        return payee;
    }

    public void setChecker(String checker) 
    {
        this.checker = checker;
    }

    public String getChecker() 
    {
        return checker;
    }

    public void setNote(String note) 
    {
        this.note = note;
    }

    public String getNote() 
    {
        return note;
    }

    public void setInvoiceTitle(String invoiceTitle) 
    {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceTitle() 
    {
        return invoiceTitle;
    }

    public void setOriginalFileName(String originalFileName) 
    {
        this.originalFileName = originalFileName;
    }

    public String getOriginalFileName() 
    {
        return originalFileName;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setGoodsInfoJson(String goodsInfoJson) 
    {
        this.goodsInfoJson = goodsInfoJson;
    }

    public String getGoodsInfoJson() 
    {
        return goodsInfoJson;
    }

    public void setGoodsInfoList(List<InvoiceGoodsInfo> goodsInfoList) 
    {
        this.goodsInfoList = goodsInfoList;
    }

    public List<InvoiceGoodsInfo> getGoodsInfoList() 
    {
        return goodsInfoList;
    }

    @Override
    public String toString() {
        return "InvoiceRecognition{" +
                "id=" + id +
                ", invoiceType='" + invoiceType + '\'' +
                ", invoiceCode='" + invoiceCode + '\'' +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", issueDate=" + issueDate +
                ", checkCode='" + checkCode + '\'' +
                ", machineNumber='" + machineNumber + '\'' +
                ", taxControlCode='" + taxControlCode + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", buyerTaxId='" + buyerTaxId + '\'' +
                ", buyerAddrTel='" + buyerAddrTel + '\'' +
                ", buyerFinancialAccount='" + buyerFinancialAccount + '\'' +
                ", sellerName='" + sellerName + '\'' +
                ", sellerTaxId='" + sellerTaxId + '\'' +
                ", sellerAddrTel='" + sellerAddrTel + '\'' +
                ", sellerFinancialAccount='" + sellerFinancialAccount + '\'' +
                ", totalAmount=" + totalAmount +
                ", taxAmount=" + taxAmount +
                ", amountWithoutTax=" + amountWithoutTax +
                ", invoiceClerk='" + invoiceClerk + '\'' +
                ", payee='" + payee + '\'' +
                ", checker='" + checker + '\'' +
                ", note='" + note + '\'' +
                ", invoiceTitle='" + invoiceTitle + '\'' +
                ", originalFileName='" + originalFileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", status='" + status + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", goodsInfoJson='" + goodsInfoJson + '\'' +
                '}';
    }
}
