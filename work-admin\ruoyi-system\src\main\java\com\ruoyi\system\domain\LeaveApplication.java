package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.config.DateDeserializer;

/**
 * 请假申请对象 leave_application
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class LeaveApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 请假申请ID */
    @Excel(name = "请假申请ID", cellType = ColumnType.NUMERIC)
    private Long id;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private String appNo;

    /** 申请人ID */
    @Excel(name = "申请人ID", cellType = ColumnType.NUMERIC)
    private Long userId;

    /** 申请人姓名 */
    @Excel(name = "申请人")
    private String userName;

    /** 部门名称 */
    @Excel(name = "部门")
    private String deptName;

    /** 假期类型ID */
    @Excel(name = "假期类型ID", cellType = ColumnType.NUMERIC)
    private Long leaveTypeId;

    /** 假期类型名称 */
    @Excel(name = "假期类型")
    private String leaveTypeName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 请假天数 */
    @Excel(name = "请假天数")
    private BigDecimal leaveDays;

    /** 请假原因 */
    @Excel(name = "请假原因")
    private String reason;

    /** 证明附件 */
    private String proofFile;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=待审批,2=已通过,3=已拒绝,4=已撤销")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAppNo(String appNo) 
    {
        this.appNo = appNo;
    }

    @NotBlank(message = "申请单号不能为空")
    @Size(min = 0, max = 50, message = "申请单号长度不能超过50个字符")
    public String getAppNo() 
    {
        return appNo;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    @NotNull(message = "申请人不能为空")
    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setLeaveTypeId(Long leaveTypeId) 
    {
        this.leaveTypeId = leaveTypeId;
    }

    @NotNull(message = "假期类型不能为空")
    public Long getLeaveTypeId() 
    {
        return leaveTypeId;
    }

    public void setLeaveTypeName(String leaveTypeName) 
    {
        this.leaveTypeName = leaveTypeName;
    }

    public String getLeaveTypeName() 
    {
        return leaveTypeName;
    }

    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    @NotNull(message = "开始时间不能为空")
    public Date getStartDate() 
    {
        return startDate;
    }

    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    @NotNull(message = "结束时间不能为空")
    public Date getEndDate() 
    {
        return endDate;
    }

    public void setLeaveDays(BigDecimal leaveDays) 
    {
        this.leaveDays = leaveDays;
    }

    @NotNull(message = "请假天数不能为空")
    public BigDecimal getLeaveDays() 
    {
        return leaveDays;
    }

    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    @NotBlank(message = "请假原因不能为空")
    @Size(min = 0, max = 500, message = "请假原因长度不能超过500个字符")
    public String getReason() 
    {
        return reason;
    }

    public void setProofFile(String proofFile) 
    {
        this.proofFile = proofFile;
    }

    public String getProofFile() 
    {
        return proofFile;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appNo", getAppNo())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("deptName", getDeptName())
            .append("leaveTypeId", getLeaveTypeId())
            .append("leaveTypeName", getLeaveTypeName())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("leaveDays", getLeaveDays())
            .append("reason", getReason())
            .append("proofFile", getProofFile())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 