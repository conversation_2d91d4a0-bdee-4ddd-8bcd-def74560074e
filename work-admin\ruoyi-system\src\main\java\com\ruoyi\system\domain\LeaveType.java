package com.ruoyi.system.domain;

import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 假期类型对象 leave_type
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class LeaveType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 假期类型ID */
    @Excel(name = "假期类型ID", cellType = ColumnType.NUMERIC)
    private Long id;

    /** 假期名称 */
    @Excel(name = "假期名称")
    private String typeName;

    /** 假期代码 */
    @Excel(name = "假期代码")
    private String typeCode;

    /** 最大天数 */
    @Excel(name = "最大天数")
    private BigDecimal maxDays;

    /** 是否带薪 */
    @Excel(name = "是否带薪", readConverterExp = "1=是,0=否")
    private Integer isPaid;

    /** 是否需要证明 */
    @Excel(name = "是否需要证明", readConverterExp = "1=是,0=否")
    private Integer needProof;

    /** 需提前申请天数 */
    @Excel(name = "需提前申请天数")
    private Integer advanceDays;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=正常,0=停用")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTypeName(String typeName) 
    {
        this.typeName = typeName;
    }

    @NotBlank(message = "假期名称不能为空")
    @Size(min = 0, max = 50, message = "假期名称长度不能超过50个字符")
    public String getTypeName() 
    {
        return typeName;
    }

    public void setTypeCode(String typeCode) 
    {
        this.typeCode = typeCode;
    }

    @NotBlank(message = "假期代码不能为空")
    @Size(min = 0, max = 20, message = "假期代码长度不能超过20个字符")
    public String getTypeCode() 
    {
        return typeCode;
    }

    public void setMaxDays(BigDecimal maxDays) 
    {
        this.maxDays = maxDays;
    }

    public BigDecimal getMaxDays() 
    {
        return maxDays;
    }

    public void setIsPaid(Integer isPaid) 
    {
        this.isPaid = isPaid;
    }

    public Integer getIsPaid() 
    {
        return isPaid;
    }

    public void setNeedProof(Integer needProof) 
    {
        this.needProof = needProof;
    }

    public Integer getNeedProof() 
    {
        return needProof;
    }

    public void setAdvanceDays(Integer advanceDays) 
    {
        this.advanceDays = advanceDays;
    }

    public Integer getAdvanceDays() 
    {
        return advanceDays;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("typeName", getTypeName())
            .append("typeCode", getTypeCode())
            .append("maxDays", getMaxDays())
            .append("isPaid", getIsPaid())
            .append("needProof", getNeedProof())
            .append("advanceDays", getAdvanceDays())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 