package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购申请对象 procurement_application
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProcurementApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private String appNo;

    /** 申请标题 */
    @Excel(name = "申请标题")
    private String title;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private Long applicantId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String applicantName;

    /** 申请部门ID */
    @Excel(name = "申请部门ID")
    private Long deptId;

    /** 申请部门名称 */
    @Excel(name = "申请部门名称")
    private String deptName;

    /** 采购类型 */
    @Excel(name = "采购类型", readConverterExp = "OFFICE=办公用品,IT=IT设备,MATERIAL=原材料,SERVICE=服务")
    private String procurementType;

    /** 申请总金额 */
    @Excel(name = "申请总金额")
    private BigDecimal totalAmount;

    /** 紧急程度 */
    @Excel(name = "紧急程度", readConverterExp = "1=普通,2=紧急,3=特急")
    private Integer urgencyLevel;

    /** 期望到货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望到货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expectedDate;

    /** 申请理由 */
    @Excel(name = "申请理由")
    private String reason;

    /** 附件URLs */
    private String attachmentUrls;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=草稿,2=待审批,3=审批中,4=已通过,5=已拒绝,6=已采购,7=已入库,8=已报销,9=已关闭")
    private Integer status;

    /** 当前审批步骤 */
    @Excel(name = "当前审批步骤")
    private Integer currentStep;

    /** 流程实例ID */
    private String processInstanceId;

    /** 删除标志 */
    private String delFlag;

    /** 采购明细列表 */
    private List<ProcurementItem> procurementItemList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAppNo(String appNo) 
    {
        this.appNo = appNo;
    }

    public String getAppNo() 
    {
        return appNo;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setApplicantId(Long applicantId) 
    {
        this.applicantId = applicantId;
    }

    public Long getApplicantId() 
    {
        return applicantId;
    }
    public void setApplicantName(String applicantName) 
    {
        this.applicantName = applicantName;
    }

    public String getApplicantName() 
    {
        return applicantName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setProcurementType(String procurementType) 
    {
        this.procurementType = procurementType;
    }

    public String getProcurementType() 
    {
        return procurementType;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setUrgencyLevel(Integer urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public Integer getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setExpectedDate(Date expectedDate) 
    {
        this.expectedDate = expectedDate;
    }

    public Date getExpectedDate() 
    {
        return expectedDate;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setAttachmentUrls(String attachmentUrls) 
    {
        this.attachmentUrls = attachmentUrls;
    }

    public String getAttachmentUrls() 
    {
        return attachmentUrls;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setCurrentStep(Integer currentStep) 
    {
        this.currentStep = currentStep;
    }

    public Integer getCurrentStep() 
    {
        return currentStep;
    }
    public void setProcessInstanceId(String processInstanceId) 
    {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessInstanceId() 
    {
        return processInstanceId;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public List<ProcurementItem> getProcurementItemList()
    {
        return procurementItemList;
    }

    public void setProcurementItemList(List<ProcurementItem> procurementItemList)
    {
        this.procurementItemList = procurementItemList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appNo", getAppNo())
            .append("title", getTitle())
            .append("applicantId", getApplicantId())
            .append("applicantName", getApplicantName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("procurementType", getProcurementType())
            .append("totalAmount", getTotalAmount())
            .append("urgencyLevel", getUrgencyLevel())
            .append("expectedDate", getExpectedDate())
            .append("reason", getReason())
            .append("attachmentUrls", getAttachmentUrls())
            .append("status", getStatus())
            .append("currentStep", getCurrentStep())
            .append("processInstanceId", getProcessInstanceId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .append("procurementItemList", getProcurementItemList())
            .toString();
    }
}