package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购明细对象 procurement_item
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProcurementItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 申请ID */
    @Excel(name = "申请ID")
    private Long appId;

    /** 物品分类 */
    @Excel(name = "物品分类")
    private String itemCategory;

    /** 物品名称 */
    @Excel(name = "物品名称")
    private String itemName;

    /** 物品编码 */
    @Excel(name = "物品编码")
    private String itemCode;

    /** 品牌 */
    @Excel(name = "品牌")
    private String brand;

    /** 型号 */
    @Excel(name = "型号")
    private String model;

    /** 规格描述 */
    @Excel(name = "规格描述")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal quantity;

    /** 预估单价 */
    @Excel(name = "预估单价")
    private BigDecimal unitPrice;

    /** 预估总价 */
    @Excel(name = "预估总价")
    private BigDecimal totalPrice;

    /** 实际单价 */
    @Excel(name = "实际单价")
    private BigDecimal actualUnitPrice;

    /** 实际总价 */
    @Excel(name = "实际总价")
    private BigDecimal actualTotalPrice;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 采购链接 */
    @Excel(name = "采购链接")
    private String purchaseUrl;

    /** 截图附件 */
    @Excel(name = "截图附件")
    private String screenshotUrl;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=待采购,2=已采购,3=已入库")
    private Integer status;

    /** 采购日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "采购日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date purchaseDate;

    /** 收货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiptDate;

    /** 删除标志 */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAppId(Long appId) 
    {
        this.appId = appId;
    }

    public Long getAppId() 
    {
        return appId;
    }
    public void setItemCategory(String itemCategory) 
    {
        this.itemCategory = itemCategory;
    }

    public String getItemCategory() 
    {
        return itemCategory;
    }
    public void setItemName(String itemName) 
    {
        this.itemName = itemName;
    }

    public String getItemName() 
    {
        return itemName;
    }
    public void setItemCode(String itemCode) 
    {
        this.itemCode = itemCode;
    }

    public String getItemCode() 
    {
        return itemCode;
    }
    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }
    public void setModel(String model) 
    {
        this.model = model;
    }

    public String getModel() 
    {
        return model;
    }
    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }
    public void setTotalPrice(BigDecimal totalPrice) 
    {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getTotalPrice() 
    {
        return totalPrice;
    }
    public void setActualUnitPrice(BigDecimal actualUnitPrice) 
    {
        this.actualUnitPrice = actualUnitPrice;
    }

    public BigDecimal getActualUnitPrice() 
    {
        return actualUnitPrice;
    }
    public void setActualTotalPrice(BigDecimal actualTotalPrice) 
    {
        this.actualTotalPrice = actualTotalPrice;
    }

    public BigDecimal getActualTotalPrice() 
    {
        return actualTotalPrice;
    }
    public void setSupplierName(String supplierName) 
    {
        this.supplierName = supplierName;
    }

    public String getSupplierName() 
    {
        return supplierName;
    }
    public void setPurchaseUrl(String purchaseUrl) 
    {
        this.purchaseUrl = purchaseUrl;
    }

    public String getPurchaseUrl() 
    {
        return purchaseUrl;
    }
    public void setScreenshotUrl(String screenshotUrl) 
    {
        this.screenshotUrl = screenshotUrl;
    }

    public String getScreenshotUrl() 
    {
        return screenshotUrl;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setPurchaseDate(Date purchaseDate) 
    {
        this.purchaseDate = purchaseDate;
    }

    public Date getPurchaseDate() 
    {
        return purchaseDate;
    }
    public void setReceiptDate(Date receiptDate) 
    {
        this.receiptDate = receiptDate;
    }

    public Date getReceiptDate() 
    {
        return receiptDate;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appId", getAppId())
            .append("itemCategory", getItemCategory())
            .append("itemName", getItemName())
            .append("itemCode", getItemCode())
            .append("brand", getBrand())
            .append("model", getModel())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("quantity", getQuantity())
            .append("unitPrice", getUnitPrice())
            .append("totalPrice", getTotalPrice())
            .append("actualUnitPrice", getActualUnitPrice())
            .append("actualTotalPrice", getActualTotalPrice())
            .append("supplierName", getSupplierName())
            .append("purchaseUrl", getPurchaseUrl())
            .append("screenshotUrl", getScreenshotUrl())
            .append("remark", getRemark())
            .append("status", getStatus())
            .append("purchaseDate", getPurchaseDate())
            .append("receiptDate", getReceiptDate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .toString();
    }
}