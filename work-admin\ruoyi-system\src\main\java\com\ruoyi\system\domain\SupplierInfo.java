package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SupplierInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 供应商ID */
    private Long id;

    /** 供应商编码 */
    @Excel(name = "供应商编码")
    private String supplierCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 供应商简称 */
    @Excel(name = "供应商简称")
    private String supplierShortName;

    /** 供应商类型 */
    @Excel(name = "供应商类型", readConverterExp = "MANUFACTURER=生产商,DISTRIBUTOR=经销商,SERVICE=服务商,TRADER=贸易商")
    private String supplierType;

    /** 供应商等级 */
    @Excel(name = "供应商等级", readConverterExp = "A=A级,B=B级,C=C级,D=D级")
    private String supplierLevel;

    /** 主营业务 */
    @Excel(name = "主营业务")
    private String mainBusiness;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobilePhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 传真号码 */
    @Excel(name = "传真号码")
    private String faxNumber;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 区县 */
    @Excel(name = "区县")
    private String district;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    private String postalCode;

    /** 营业执照号 */
    @Excel(name = "营业执照号")
    private String businessLicense;

    /** 税号 */
    @Excel(name = "税号")
    private String taxNumber;

    /** 银行账号 */
    @Excel(name = "银行账号")
    private String bankAccount;

    /** 开户银行 */
    @Excel(name = "开户银行")
    private String bankName;

    /** 信用等级 */
    @Excel(name = "信用等级", readConverterExp = "AAA=AAA级,AA=AA级,A=A级,BBB=BBB级,BB=BB级,B=B级,CCC=CCC级,CC=CC级,C=C级,D=D级")
    private String creditRating;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private BigDecimal registeredCapital;

    /** 成立日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "成立日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date establishDate;

    /** 合作开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合作开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cooperationStartDate;

    /** 合作年限 */
    @Excel(name = "合作年限")
    private Integer cooperationYears;

    /** 累计采购金额 */
    @Excel(name = "累计采购金额")
    private BigDecimal totalAmount;

    /** 年度采购金额 */
    @Excel(name = "年度采购金额")
    private BigDecimal yearlyAmount;

    /** 付款方式 */
    @Excel(name = "付款方式", readConverterExp = "CASH=现金,TRANSFER=转账,CHECK=支票,CREDIT=赊账")
    private String paymentMethod;

    /** 付款周期（天） */
    @Excel(name = "付款周期")
    private Integer paymentCycle;

    /** 发票类型 */
    @Excel(name = "发票类型", readConverterExp = "NORMAL=普通发票,SPECIAL=专用发票,ELECTRONIC=电子发票")
    private String invoiceType;

    /** 质量认证 */
    @Excel(name = "质量认证")
    private String qualityCertification;

    /** 环保认证 */
    @Excel(name = "环保认证")
    private String environmentCertification;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "1=正常,2=暂停,3=黑名单,4=待审核")
    private Integer status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setSupplierCode(String supplierCode)
    {
        this.supplierCode = supplierCode;
    }

    @NotBlank(message = "供应商编码不能为空")
    @Size(min = 0, max = 50, message = "供应商编码长度不能超过50个字符")
    public String getSupplierCode()
    {
        return supplierCode;
    }

    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }

    @NotBlank(message = "供应商名称不能为空")
    @Size(min = 0, max = 200, message = "供应商名称长度不能超过200个字符")
    public String getSupplierName()
    {
        return supplierName;
    }

    public void setSupplierShortName(String supplierShortName)
    {
        this.supplierShortName = supplierShortName;
    }

    @Size(min = 0, max = 100, message = "供应商简称长度不能超过100个字符")
    public String getSupplierShortName()
    {
        return supplierShortName;
    }

    public void setSupplierType(String supplierType)
    {
        this.supplierType = supplierType;
    }

    @NotBlank(message = "供应商类型不能为空")
    public String getSupplierType()
    {
        return supplierType;
    }

    public void setSupplierLevel(String supplierLevel)
    {
        this.supplierLevel = supplierLevel;
    }

    public String getSupplierLevel()
    {
        return supplierLevel;
    }

    public void setMainBusiness(String mainBusiness)
    {
        this.mainBusiness = mainBusiness;
    }

    @Size(min = 0, max = 500, message = "主营业务长度不能超过500个字符")
    public String getMainBusiness()
    {
        return mainBusiness;
    }

    public void setContactPerson(String contactPerson)
    {
        this.contactPerson = contactPerson;
    }

    @Size(min = 0, max = 50, message = "联系人长度不能超过50个字符")
    public String getContactPerson()
    {
        return contactPerson;
    }

    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    @Pattern(regexp = "^[0-9-()（）\\s+]*$", message = "联系电话格式不正确")
    public String getContactPhone()
    {
        return contactPhone;
    }

    public void setMobilePhone(String mobilePhone)
    {
        this.mobilePhone = mobilePhone;
    }

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    public String getMobilePhone()
    {
        return mobilePhone;
    }

    public void setContactEmail(String contactEmail)
    {
        this.contactEmail = contactEmail;
    }

    @Email(message = "邮箱格式不正确")
    public String getContactEmail()
    {
        return contactEmail;
    }

    public void setFaxNumber(String faxNumber)
    {
        this.faxNumber = faxNumber;
    }

    public String getFaxNumber()
    {
        return faxNumber;
    }

    public void setAddress(String address)
    {
        this.address = address;
    }

    @Size(min = 0, max = 500, message = "详细地址长度不能超过500个字符")
    public String getAddress()
    {
        return address;
    }

    public void setProvince(String province)
    {
        this.province = province;
    }

    public String getProvince()
    {
        return province;
    }

    public void setCity(String city)
    {
        this.city = city;
    }

    public String getCity()
    {
        return city;
    }

    public void setDistrict(String district)
    {
        this.district = district;
    }

    public String getDistrict()
    {
        return district;
    }

    public void setPostalCode(String postalCode)
    {
        this.postalCode = postalCode;
    }

    @Pattern(regexp = "^\\d{6}$", message = "邮政编码格式不正确")
    public String getPostalCode()
    {
        return postalCode;
    }

    public void setBusinessLicense(String businessLicense)
    {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense()
    {
        return businessLicense;
    }

    public void setTaxNumber(String taxNumber)
    {
        this.taxNumber = taxNumber;
    }

    public String getTaxNumber()
    {
        return taxNumber;
    }

    public void setBankAccount(String bankAccount)
    {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount()
    {
        return bankAccount;
    }

    public void setBankName(String bankName)
    {
        this.bankName = bankName;
    }

    public String getBankName()
    {
        return bankName;
    }

    public void setCreditRating(String creditRating)
    {
        this.creditRating = creditRating;
    }

    public String getCreditRating()
    {
        return creditRating;
    }

    public void setRegisteredCapital(BigDecimal registeredCapital)
    {
        this.registeredCapital = registeredCapital;
    }

    public BigDecimal getRegisteredCapital()
    {
        return registeredCapital;
    }

    public void setEstablishDate(Date establishDate)
    {
        this.establishDate = establishDate;
    }

    public Date getEstablishDate()
    {
        return establishDate;
    }

    public void setCooperationStartDate(Date cooperationStartDate)
    {
        this.cooperationStartDate = cooperationStartDate;
    }

    public Date getCooperationStartDate()
    {
        return cooperationStartDate;
    }

    public void setCooperationYears(Integer cooperationYears)
    {
        this.cooperationYears = cooperationYears;
    }

    public Integer getCooperationYears()
    {
        return cooperationYears;
    }

    public void setTotalAmount(BigDecimal totalAmount)
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount()
    {
        return totalAmount;
    }

    public void setYearlyAmount(BigDecimal yearlyAmount)
    {
        this.yearlyAmount = yearlyAmount;
    }

    public BigDecimal getYearlyAmount()
    {
        return yearlyAmount;
    }

    public void setPaymentMethod(String paymentMethod)
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod()
    {
        return paymentMethod;
    }

    public void setPaymentCycle(Integer paymentCycle)
    {
        this.paymentCycle = paymentCycle;
    }

    public Integer getPaymentCycle()
    {
        return paymentCycle;
    }

    public void setInvoiceType(String invoiceType)
    {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceType()
    {
        return invoiceType;
    }

    public void setQualityCertification(String qualityCertification)
    {
        this.qualityCertification = qualityCertification;
    }

    public String getQualityCertification()
    {
        return qualityCertification;
    }

    public void setEnvironmentCertification(String environmentCertification)
    {
        this.environmentCertification = environmentCertification;
    }

    public String getEnvironmentCertification()
    {
        return environmentCertification;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("supplierCode", getSupplierCode())
            .append("supplierName", getSupplierName())
            .append("supplierShortName", getSupplierShortName())
            .append("supplierType", getSupplierType())
            .append("supplierLevel", getSupplierLevel())
            .append("mainBusiness", getMainBusiness())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("mobilePhone", getMobilePhone())
            .append("contactEmail", getContactEmail())
            .append("faxNumber", getFaxNumber())
            .append("address", getAddress())
            .append("province", getProvince())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("postalCode", getPostalCode())
            .append("businessLicense", getBusinessLicense())
            .append("taxNumber", getTaxNumber())
            .append("bankAccount", getBankAccount())
            .append("bankName", getBankName())
            .append("creditRating", getCreditRating())
            .append("registeredCapital", getRegisteredCapital())
            .append("establishDate", getEstablishDate())
            .append("cooperationStartDate", getCooperationStartDate())
            .append("cooperationYears", getCooperationYears())
            .append("totalAmount", getTotalAmount())
            .append("yearlyAmount", getYearlyAmount())
            .append("paymentMethod", getPaymentMethod())
            .append("paymentCycle", getPaymentCycle())
            .append("invoiceType", getInvoiceType())
            .append("qualityCertification", getQualityCertification())
            .append("environmentCertification", getEnvironmentCertification())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}
