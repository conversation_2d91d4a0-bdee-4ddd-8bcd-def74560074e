package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.config.DateDeserializer;

/**
 * 考勤记录对象 sys_attendance
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SysAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 考勤ID */
    private Long attendanceId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 部门名称 */
    @Excel(name = "部门")
    private String deptName;

    /** 考勤日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "考勤日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date attendanceDate;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkInTime;

    /** 签退时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "签退时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkOutTime;

    /** 工作时长(小时) */
    @Excel(name = "工作时长(小时)")
    private BigDecimal workHours;

    /** 考勤状态（0正常 1迟到 2早退 3旷工 4请假） */
    @Excel(name = "考勤状态", readConverterExp = "0=正常,1=迟到,2=早退,3=旷工,4=请假")
    private String attendanceStatus;

    /** 迟到分钟数 */
    @Excel(name = "迟到分钟数")
    private Integer lateMinutes;

    /** 早退分钟数 */
    @Excel(name = "早退分钟数")
    private Integer earlyMinutes;

    public void setAttendanceId(Long attendanceId) 
    {
        this.attendanceId = attendanceId;
    }

    public Long getAttendanceId() 
    {
        return attendanceId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setAttendanceDate(Date attendanceDate) 
    {
        this.attendanceDate = attendanceDate;
    }

    public Date getAttendanceDate() 
    {
        return attendanceDate;
    }
    public void setCheckInTime(Date checkInTime) 
    {
        this.checkInTime = checkInTime;
    }

    public Date getCheckInTime() 
    {
        return checkInTime;
    }
    public void setCheckOutTime(Date checkOutTime) 
    {
        this.checkOutTime = checkOutTime;
    }

    public Date getCheckOutTime() 
    {
        return checkOutTime;
    }
    public void setWorkHours(BigDecimal workHours) 
    {
        this.workHours = workHours;
    }

    public BigDecimal getWorkHours() 
    {
        return workHours;
    }
    public void setAttendanceStatus(String attendanceStatus) 
    {
        this.attendanceStatus = attendanceStatus;
    }

    public String getAttendanceStatus() 
    {
        return attendanceStatus;
    }
    public void setLateMinutes(Integer lateMinutes) 
    {
        this.lateMinutes = lateMinutes;
    }

    public Integer getLateMinutes() 
    {
        return lateMinutes;
    }
    public void setEarlyMinutes(Integer earlyMinutes) 
    {
        this.earlyMinutes = earlyMinutes;
    }

    public Integer getEarlyMinutes() 
    {
        return earlyMinutes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attendanceId", getAttendanceId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("deptName", getDeptName())
            .append("attendanceDate", getAttendanceDate())
            .append("checkInTime", getCheckInTime())
            .append("checkOutTime", getCheckOutTime())
            .append("workHours", getWorkHours())
            .append("attendanceStatus", getAttendanceStatus())
            .append("lateMinutes", getLateMinutes())
            .append("earlyMinutes", getEarlyMinutes())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 