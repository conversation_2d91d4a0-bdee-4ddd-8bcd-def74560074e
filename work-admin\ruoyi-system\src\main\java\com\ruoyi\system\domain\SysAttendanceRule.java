package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.config.DateDeserializer;

/**
 * 考勤规则对象 sys_attendance_rule
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SysAttendanceRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long ruleId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 上班时间 */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "上班时间", width = 30, dateFormat = "HH:mm:ss")
    private Date workStartTime;

    /** 下班时间 */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = DateDeserializer.class)
    @Excel(name = "下班时间", width = 30, dateFormat = "HH:mm:ss")
    private Date workEndTime;

    /** 迟到阈值(分钟) */
    @Excel(name = "迟到阈值(分钟)")
    private Integer lateThreshold;

    /** 早退阈值(分钟) */
    @Excel(name = "早退阈值(分钟)")
    private Integer earlyThreshold;

    /** 工作日 */
    @Excel(name = "工作日")
    private String workDays;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setRuleName(String ruleName) 
    {
        this.ruleName = ruleName;
    }

    public String getRuleName() 
    {
        return ruleName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setWorkStartTime(Date workStartTime) 
    {
        this.workStartTime = workStartTime;
    }

    public Date getWorkStartTime() 
    {
        return workStartTime;
    }
    public void setWorkEndTime(Date workEndTime) 
    {
        this.workEndTime = workEndTime;
    }

    public Date getWorkEndTime() 
    {
        return workEndTime;
    }
    public void setLateThreshold(Integer lateThreshold) 
    {
        this.lateThreshold = lateThreshold;
    }

    public Integer getLateThreshold() 
    {
        return lateThreshold;
    }
    public void setEarlyThreshold(Integer earlyThreshold) 
    {
        this.earlyThreshold = earlyThreshold;
    }

    public Integer getEarlyThreshold() 
    {
        return earlyThreshold;
    }
    public void setWorkDays(String workDays) 
    {
        this.workDays = workDays;
    }

    public String getWorkDays() 
    {
        return workDays;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("ruleName", getRuleName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("workStartTime", getWorkStartTime())
            .append("workEndTime", getWorkEndTime())
            .append("lateThreshold", getLateThreshold())
            .append("earlyThreshold", getEarlyThreshold())
            .append("workDays", getWorkDays())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 