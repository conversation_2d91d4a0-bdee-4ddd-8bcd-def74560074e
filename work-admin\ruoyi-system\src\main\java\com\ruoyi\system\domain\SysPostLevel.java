package com.ruoyi.system.domain;

import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 岗位级别工资表 sys_post_level
 * 
 * <AUTHOR>
 */
public class SysPostLevel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 级别ID */
    private Long levelId;

    /** 岗位ID */
    @Excel(name = "岗位ID")
    private Long postId;

    /** 级别编码 */
    @Excel(name = "级别编码")
    private String levelCode;

    /** 级别名称 */
    @Excel(name = "级别名称")
    private String levelName;

    /** 级别排序 */
    @Excel(name = "级别排序")
    private Integer levelOrder;

    /** 基础工资 */
    @Excel(name = "基础工资")
    private BigDecimal baseSalary;

    /** 绩效工资 */
    @Excel(name = "绩效工资")
    private BigDecimal performanceSalary;

    /** 津贴补助 */
    @Excel(name = "津贴补助")
    private BigDecimal allowance;

    /** 总工资 */
    @Excel(name = "总工资")
    private BigDecimal totalSalary;

    /** 最低工作经验要求(月) */
    @Excel(name = "最低经验要求")
    private Integer minExperience;

    /** 最高工作经验要求(月) */
    @Excel(name = "最高经验要求")
    private Integer maxExperience;

    /** 技能要求 */
    @Excel(name = "技能要求")
    private String skillRequirements;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 岗位名称（关联查询用） */
    private String postName;

    public Long getLevelId()
    {
        return levelId;
    }

    public void setLevelId(Long levelId)
    {
        this.levelId = levelId;
    }

    @NotNull(message = "岗位ID不能为空")
    public Long getPostId()
    {
        return postId;
    }

    public void setPostId(Long postId)
    {
        this.postId = postId;
    }

    @NotBlank(message = "级别编码不能为空")
    @Size(min = 0, max = 50, message = "级别编码长度不能超过50个字符")
    public String getLevelCode()
    {
        return levelCode;
    }

    public void setLevelCode(String levelCode)
    {
        this.levelCode = levelCode;
    }

    @NotBlank(message = "级别名称不能为空")
    @Size(min = 0, max = 100, message = "级别名称长度不能超过100个字符")
    public String getLevelName()
    {
        return levelName;
    }

    public void setLevelName(String levelName)
    {
        this.levelName = levelName;
    }

    @NotNull(message = "级别排序不能为空")
    public Integer getLevelOrder()
    {
        return levelOrder;
    }

    public void setLevelOrder(Integer levelOrder)
    {
        this.levelOrder = levelOrder;
    }

    @NotNull(message = "基础工资不能为空")
    public BigDecimal getBaseSalary()
    {
        return baseSalary;
    }

    public void setBaseSalary(BigDecimal baseSalary)
    {
        this.baseSalary = baseSalary;
    }

    public BigDecimal getPerformanceSalary()
    {
        return performanceSalary;
    }

    public void setPerformanceSalary(BigDecimal performanceSalary)
    {
        this.performanceSalary = performanceSalary;
    }

    public BigDecimal getAllowance()
    {
        return allowance;
    }

    public void setAllowance(BigDecimal allowance)
    {
        this.allowance = allowance;
    }

    @NotNull(message = "总工资不能为空")
    public BigDecimal getTotalSalary()
    {
        return totalSalary;
    }

    public void setTotalSalary(BigDecimal totalSalary)
    {
        this.totalSalary = totalSalary;
    }

    public Integer getMinExperience()
    {
        return minExperience;
    }

    public void setMinExperience(Integer minExperience)
    {
        this.minExperience = minExperience;
    }

    public Integer getMaxExperience()
    {
        return maxExperience;
    }

    public void setMaxExperience(Integer maxExperience)
    {
        this.maxExperience = maxExperience;
    }

    public String getSkillRequirements()
    {
        return skillRequirements;
    }

    public void setSkillRequirements(String skillRequirements)
    {
        this.skillRequirements = skillRequirements;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getPostName()
    {
        return postName;
    }

    public void setPostName(String postName)
    {
        this.postName = postName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("levelId", getLevelId())
            .append("postId", getPostId())
            .append("levelCode", getLevelCode())
            .append("levelName", getLevelName())
            .append("levelOrder", getLevelOrder())
            .append("baseSalary", getBaseSalary())
            .append("performanceSalary", getPerformanceSalary())
            .append("allowance", getAllowance())
            .append("totalSalary", getTotalSalary())
            .append("minExperience", getMinExperience())
            .append("maxExperience", getMaxExperience())
            .append("skillRequirements", getSkillRequirements())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}