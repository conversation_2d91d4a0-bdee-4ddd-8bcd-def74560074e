package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ApprovalTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * 审批模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ApprovalTemplateMapper 
{
    /**
     * 查询审批模板
     * 
     * @param id 审批模板主键
     * @return 审批模板
     */
    public ApprovalTemplate selectApprovalTemplateById(Long id);

    /**
     * 查询审批模板列表
     * 
     * @param approvalTemplate 审批模板
     * @return 审批模板集合
     */
    public List<ApprovalTemplate> selectApprovalTemplateList(ApprovalTemplate approvalTemplate);

    /**
     * 新增审批模板
     * 
     * @param approvalTemplate 审批模板
     * @return 结果
     */
    public int insertApprovalTemplate(ApprovalTemplate approvalTemplate);

    /**
     * 修改审批模板
     * 
     * @param approvalTemplate 审批模板
     * @return 结果
     */
    public int updateApprovalTemplate(ApprovalTemplate approvalTemplate);

    /**
     * 删除审批模板
     * 
     * @param id 审批模板主键
     * @return 结果
     */
    public int deleteApprovalTemplateById(Long id);

    /**
     * 批量删除审批模板
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApprovalTemplateByIds(Long[] ids);

    /**
     * 清除所有默认模板
     * 
     * @return 结果
     */
    public int clearDefaultTemplate();

    /**
     * 设置默认模板
     * 
     * @param id 模板ID
     * @return 结果
     */
    public int setDefaultTemplate(Long id);

    /**
     * 改变模板状态
     * 
     * @param id 模板ID
     * @param status 状态
     * @return 结果
     */
    public int changeTemplateStatus(@Param("id") Long id, @Param("status") Boolean status);

    /**
     * 根据条件获取审批模板
     * 
     * @param businessType 业务类型
     * @param procurementType 采购类型
     * @param amount 金额
     * @param urgencyLevel 紧急程度
     * @return 审批模板
     */
    public ApprovalTemplate getTemplateByCondition(@Param("businessType") String businessType, 
                                                  @Param("procurementType") String procurementType,
                                                  @Param("amount") java.math.BigDecimal amount, 
                                                  @Param("urgencyLevel") Integer urgencyLevel);
}