package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.InvoiceRecognition;

/**
 * 发票识别Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public interface InvoiceRecognitionMapper 
{
    /**
     * 查询发票识别
     * 
     * @param id 发票识别主键
     * @return 发票识别
     */
    public InvoiceRecognition selectInvoiceRecognitionById(Long id);

    /**
     * 查询发票识别列表
     * 
     * @param invoiceRecognition 发票识别
     * @return 发票识别集合
     */
    public List<InvoiceRecognition> selectInvoiceRecognitionList(InvoiceRecognition invoiceRecognition);

    /**
     * 新增发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    public int insertInvoiceRecognition(InvoiceRecognition invoiceRecognition);

    /**
     * 修改发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    public int updateInvoiceRecognition(InvoiceRecognition invoiceRecognition);

    /**
     * 删除发票识别
     * 
     * @param id 发票识别主键
     * @return 结果
     */
    public int deleteInvoiceRecognitionById(Long id);

    /**
     * 批量删除发票识别
     * 
     * @param ids 需要删除的发票识别主键集合
     * @return 结果
     */
    public int deleteInvoiceRecognitionByIds(Long[] ids);
}
