package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LeaveApplication;
import org.apache.ibatis.annotations.Param;

/**
 * 请假申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface LeaveApplicationMapper 
{
    /**
     * 查询请假申请
     * 
     * @param id 请假申请主键
     * @return 请假申请
     */
    public LeaveApplication selectLeaveApplicationById(Long id);

    /**
     * 查询请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationList(LeaveApplication leaveApplication);

    /**
     * 根据申请单号查询请假申请
     * 
     * @param appNo 申请单号
     * @return 请假申请
     */
    public LeaveApplication selectLeaveApplicationByAppNo(String appNo);

    /**
     * 校验申请单号是否唯一
     * 
     * @param appNo 申请单号
     * @return 请假申请信息
     */
    public LeaveApplication checkAppNoUnique(String appNo);

    /**
     * 根据用户ID查询请假申请列表
     * 
     * @param userId 用户ID
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationByUserId(Long userId);

    /**
     * 查询待审批的请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectPendingLeaveApplicationList(LeaveApplication leaveApplication);

    /**
     * 根据用户ID和时间范围查询请假申请
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationByUserAndDate(@Param("userId") Long userId, 
                                                                      @Param("startDate") String startDate, 
                                                                      @Param("endDate") String endDate);

    /**
     * 新增请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public int insertLeaveApplication(LeaveApplication leaveApplication);

    /**
     * 修改请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public int updateLeaveApplication(LeaveApplication leaveApplication);

    /**
     * 删除请假申请
     * 
     * @param id 请假申请主键
     * @return 结果
     */
    public int deleteLeaveApplicationById(Long id);

    /**
     * 批量删除请假申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLeaveApplicationByIds(Long[] ids);
} 