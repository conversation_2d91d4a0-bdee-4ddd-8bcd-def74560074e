package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LeaveType;

/**
 * 假期类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface LeaveTypeMapper 
{
    /**
     * 查询假期类型
     * 
     * @param id 假期类型主键
     * @return 假期类型
     */
    public LeaveType selectLeaveTypeById(Long id);

    /**
     * 查询假期类型列表
     * 
     * @param leaveType 假期类型
     * @return 假期类型集合
     */
    public List<LeaveType> selectLeaveTypeList(LeaveType leaveType);

    /**
     * 查询所有正常状态的假期类型
     * 
     * @return 假期类型集合
     */
    public List<LeaveType> selectLeaveTypeAll();

    /**
     * 根据假期代码查询假期类型
     * 
     * @param typeCode 假期代码
     * @return 假期类型
     */
    public LeaveType selectLeaveTypeByCode(String typeCode);

    /**
     * 校验假期代码是否唯一
     * 
     * @param typeCode 假期代码
     * @return 假期类型信息
     */
    public LeaveType checkTypeCodeUnique(String typeCode);

    /**
     * 新增假期类型
     * 
     * @param leaveType 假期类型
     * @return 结果
     */
    public int insertLeaveType(LeaveType leaveType);

    /**
     * 修改假期类型
     * 
     * @param leaveType 假期类型
     * @return 结果
     */
    public int updateLeaveType(LeaveType leaveType);

    /**
     * 删除假期类型
     * 
     * @param id 假期类型主键
     * @return 结果
     */
    public int deleteLeaveTypeById(Long id);

    /**
     * 批量删除假期类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLeaveTypeByIds(Long[] ids);
} 