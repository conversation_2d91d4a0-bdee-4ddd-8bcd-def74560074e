package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ProcurementApplication;
import com.ruoyi.system.domain.ProcurementItem;

/**
 * 采购申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProcurementApplicationMapper 
{
    /**
     * 查询采购申请
     * 
     * @param id 采购申请主键
     * @return 采购申请
     */
    public ProcurementApplication selectProcurementApplicationById(Long id);

    /**
     * 查询采购申请列表
     * 
     * @param procurementApplication 采购申请
     * @return 采购申请集合
     */
    public List<ProcurementApplication> selectProcurementApplicationList(ProcurementApplication procurementApplication);

    /**
     * 新增采购申请
     * 
     * @param procurementApplication 采购申请
     * @return 结果
     */
    public int insertProcurementApplication(ProcurementApplication procurementApplication);

    /**
     * 修改采购申请
     * 
     * @param procurementApplication 采购申请
     * @return 结果
     */
    public int updateProcurementApplication(ProcurementApplication procurementApplication);

    /**
     * 删除采购申请
     * 
     * @param id 采购申请主键
     * @return 结果
     */
    public int deleteProcurementApplicationById(Long id);

    /**
     * 批量删除采购申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcurementApplicationByIds(Long[] ids);

    /**
     * 批量删除采购明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcurementItemByAppIds(Long[] ids);
    
    /**
     * 批量新增采购明细
     * 
     * @param procurementItemList 采购明细列表
     * @return 结果
     */
    public int batchProcurementItem(List<ProcurementItem> procurementItemList);
    

    /**
     * 通过采购申请主键删除采购明细信息
     * 
     * @param id 采购申请ID
     * @return 结果
     */
    public int deleteProcurementItemByAppId(Long id);

    /**
     * 根据申请单号查询采购申请
     * 
     * @param appNo 申请单号
     * @return 采购申请
     */
    public ProcurementApplication selectProcurementApplicationByAppNo(String appNo);

    /**
     * 生成申请单号
     * 
     * @return 申请单号
     */
    public String generateAppNo();
}