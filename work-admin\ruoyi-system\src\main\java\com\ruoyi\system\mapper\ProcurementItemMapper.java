package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ProcurementItem;

/**
 * 采购明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProcurementItemMapper 
{
    /**
     * 查询采购明细
     * 
     * @param id 采购明细主键
     * @return 采购明细
     */
    public ProcurementItem selectProcurementItemById(Long id);

    /**
     * 查询采购明细列表
     * 
     * @param procurementItem 采购明细
     * @return 采购明细集合
     */
    public List<ProcurementItem> selectProcurementItemList(ProcurementItem procurementItem);

    /**
     * 根据申请ID查询采购明细列表
     * 
     * @param appId 申请ID
     * @return 采购明细集合
     */
    public List<ProcurementItem> selectProcurementItemByAppId(Long appId);

    /**
     * 新增采购明细
     * 
     * @param procurementItem 采购明细
     * @return 结果
     */
    public int insertProcurementItem(ProcurementItem procurementItem);

    /**
     * 修改采购明细
     * 
     * @param procurementItem 采购明细
     * @return 结果
     */
    public int updateProcurementItem(ProcurementItem procurementItem);

    /**
     * 删除采购明细
     * 
     * @param id 采购明细主键
     * @return 结果
     */
    public int deleteProcurementItemById(Long id);

    /**
     * 批量删除采购明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcurementItemByIds(Long[] ids);
}