package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.SysAttendance;

/**
 * 考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface SysAttendanceMapper 
{
    /**
     * 查询考勤记录
     * 
     * @param attendanceId 考勤记录主键
     * @return 考勤记录
     */
    public SysAttendance selectSysAttendanceByAttendanceId(Long attendanceId);

    /**
     * 查询考勤记录列表
     * 
     * @param sysAttendance 考勤记录
     * @return 考勤记录集合
     */
    public List<SysAttendance> selectSysAttendanceList(SysAttendance sysAttendance);

    /**
     * 新增考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int insertSysAttendance(SysAttendance sysAttendance);

    /**
     * 修改考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int updateSysAttendance(SysAttendance sysAttendance);

    /**
     * 删除考勤记录
     * 
     * @param attendanceId 考勤记录主键
     * @return 结果
     */
    public int deleteSysAttendanceByAttendanceId(Long attendanceId);

    /**
     * 批量删除考勤记录
     * 
     * @param attendanceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAttendanceByAttendanceIds(Long[] attendanceIds);

    /**
     * 查询用户当日考勤记录
     * 
     * @param userId 用户ID
     * @param attendanceDate 考勤日期
     * @return 考勤记录
     */
    public SysAttendance selectTodayAttendanceByUserId(@Param("userId") Long userId, @Param("attendanceDate") String attendanceDate);

    /**
     * 签到
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int checkIn(SysAttendance sysAttendance);

    /**
     * 签退
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int checkOut(SysAttendance sysAttendance);
} 