package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysAttendanceRule;

/**
 * 考勤规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface SysAttendanceRuleMapper 
{
    /**
     * 查询考勤规则
     * 
     * @param ruleId 考勤规则主键
     * @return 考勤规则
     */
    public SysAttendanceRule selectSysAttendanceRuleByRuleId(Long ruleId);

    /**
     * 查询考勤规则列表
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 考勤规则集合
     */
    public List<SysAttendanceRule> selectSysAttendanceRuleList(SysAttendanceRule sysAttendanceRule);

    /**
     * 新增考勤规则
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 结果
     */
    public int insertSysAttendanceRule(SysAttendanceRule sysAttendanceRule);

    /**
     * 修改考勤规则
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 结果
     */
    public int updateSysAttendanceRule(SysAttendanceRule sysAttendanceRule);

    /**
     * 删除考勤规则
     * 
     * @param ruleId 考勤规则主键
     * @return 结果
     */
    public int deleteSysAttendanceRuleByRuleId(Long ruleId);

    /**
     * 批量删除考勤规则
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAttendanceRuleByRuleIds(Long[] ruleIds);
} 