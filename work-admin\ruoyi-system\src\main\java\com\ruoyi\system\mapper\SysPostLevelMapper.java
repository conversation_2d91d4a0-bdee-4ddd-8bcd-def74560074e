package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysPostLevel;

/**
 * 岗位级别工资Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface SysPostLevelMapper 
{
    /**
     * 查询岗位级别工资
     * 
     * @param levelId 岗位级别工资主键
     * @return 岗位级别工资
     */
    public SysPostLevel selectSysPostLevelByLevelId(Long levelId);

    /**
     * 查询岗位级别工资列表
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 岗位级别工资集合
     */
    public List<SysPostLevel> selectSysPostLevelList(SysPostLevel sysPostLevel);

    /**
     * 根据岗位ID查询级别工资列表
     * 
     * @param postId 岗位ID
     * @return 岗位级别工资集合
     */
    public List<SysPostLevel> selectSysPostLevelByPostId(Long postId);

    /**
     * 新增岗位级别工资
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 结果
     */
    public int insertSysPostLevel(SysPostLevel sysPostLevel);

    /**
     * 修改岗位级别工资
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 结果
     */
    public int updateSysPostLevel(SysPostLevel sysPostLevel);

    /**
     * 删除岗位级别工资
     * 
     * @param levelId 岗位级别工资主键
     * @return 结果
     */
    public int deleteSysPostLevelByLevelId(Long levelId);

    /**
     * 批量删除岗位级别工资
     * 
     * @param levelIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysPostLevelByLevelIds(Long[] levelIds);

    /**
     * 根据岗位ID删除级别工资
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    public int deleteSysPostLevelByPostId(Long postId);

    /**
     * 检查级别编码是否唯一
     * 
     * @param postId 岗位ID
     * @param levelCode 级别编码
     * @return 结果
     */
    public SysPostLevel checkLevelCodeUnique(Long postId, String levelCode);
}