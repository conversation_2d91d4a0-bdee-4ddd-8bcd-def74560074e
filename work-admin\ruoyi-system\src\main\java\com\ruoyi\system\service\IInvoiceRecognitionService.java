package com.ruoyi.system.service;

import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.domain.InvoiceRecognition;

/**
 * 发票识别Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
public interface IInvoiceRecognitionService 
{
    /**
     * 查询发票识别
     * 
     * @param id 发票识别主键
     * @return 发票识别
     */
    public InvoiceRecognition selectInvoiceRecognitionById(Long id);

    /**
     * 查询发票识别列表
     * 
     * @param invoiceRecognition 发票识别
     * @return 发票识别集合
     */
    public List<InvoiceRecognition> selectInvoiceRecognitionList(InvoiceRecognition invoiceRecognition);

    /**
     * 新增发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    public int insertInvoiceRecognition(InvoiceRecognition invoiceRecognition);

    /**
     * 修改发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    public int updateInvoiceRecognition(InvoiceRecognition invoiceRecognition);

    /**
     * 批量删除发票识别
     * 
     * @param ids 需要删除的发票识别主键集合
     * @return 结果
     */
    public int deleteInvoiceRecognitionByIds(Long[] ids);

    /**
     * 删除发票识别信息
     * 
     * @param id 发票识别主键
     * @return 结果
     */
    public int deleteInvoiceRecognitionById(Long id);

    /**
     * 上传并识别OFD发票
     * 
     * @param file OFD文件
     * @return 发票识别结果
     */
    public InvoiceRecognition uploadAndRecognizeOfdInvoice(MultipartFile file);

    /**
     * 通过URL识别OFD发票
     * 
     * @param fileUrl OFD文件URL
     * @return 发票识别结果
     */
    public InvoiceRecognition recognizeOfdInvoiceByUrl(String fileUrl);

    /**
     * 通过Base64识别OFD发票
     * 
     * @param fileBase64 OFD文件Base64编码
     * @param fileName 文件名
     * @return 发票识别结果
     */
    public InvoiceRecognition recognizeOfdInvoiceByBase64(String fileBase64, String fileName);
}
