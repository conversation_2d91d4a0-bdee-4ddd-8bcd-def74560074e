package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.LeaveApplication;

/**
 * 请假申请Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ILeaveApplicationService 
{
    /**
     * 查询请假申请
     * 
     * @param id 请假申请主键
     * @return 请假申请
     */
    public LeaveApplication selectLeaveApplicationById(Long id);

    /**
     * 查询请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationList(LeaveApplication leaveApplication);

    /**
     * 根据申请单号查询请假申请
     * 
     * @param appNo 申请单号
     * @return 请假申请
     */
    public LeaveApplication selectLeaveApplicationByAppNo(String appNo);

    /**
     * 校验申请单号是否唯一
     * 
     * @param appNo 申请单号
     * @return 结果
     */
    public boolean checkAppNoUnique(String appNo);

    /**
     * 校验申请单号是否唯一
     * 
     * @param leaveApplication 请假申请信息
     * @return 结果
     */
    public boolean checkAppNoUnique(LeaveApplication leaveApplication);

    /**
     * 根据用户ID查询请假申请列表
     * 
     * @param userId 用户ID
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationByUserId(Long userId);

    /**
     * 查询待审批的请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectPendingLeaveApplicationList(LeaveApplication leaveApplication);

    /**
     * 根据用户ID和时间范围查询请假申请
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 请假申请集合
     */
    public List<LeaveApplication> selectLeaveApplicationByUserAndDate(Long userId, String startDate, String endDate);

    /**
     * 生成申请单号
     * 
     * @return 申请单号
     */
    public String generateAppNo();

    /**
     * 新增请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public int insertLeaveApplication(LeaveApplication leaveApplication);

    /**
     * 修改请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public int updateLeaveApplication(LeaveApplication leaveApplication);

    /**
     * 批量删除请假申请
     * 
     * @param ids 需要删除的请假申请主键集合
     * @return 结果
     */
    public int deleteLeaveApplicationByIds(Long[] ids);

    /**
     * 删除请假申请信息
     * 
     * @param id 请假申请主键
     * @return 结果
     */
    public int deleteLeaveApplicationById(Long id);

    /**
     * 审批请假申请
     * 
     * @param id 请假申请ID
     * @param status 审批状态 2-通过 3-拒绝
     * @param remark 审批意见
     * @return 结果
     */
    public int approveLeaveApplication(Long id, Integer status, String remark);

    /**
     * 撤销请假申请
     * 
     * @param id 请假申请ID
     * @return 结果
     */
    public int cancelLeaveApplication(Long id);

    /**
     * 校验是否可以删除请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public boolean checkCanDelete(LeaveApplication leaveApplication);

    /**
     * 校验是否可以修改请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    public boolean checkCanEdit(LeaveApplication leaveApplication);
} 