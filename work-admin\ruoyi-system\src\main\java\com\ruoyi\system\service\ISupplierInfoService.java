package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SupplierInfo;

/**
 * 供应商信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ISupplierInfoService 
{
    /**
     * 查询供应商信息
     * 
     * @param id 供应商信息主键
     * @return 供应商信息
     */
    public SupplierInfo selectSupplierInfoById(Long id);

    /**
     * 查询供应商信息列表
     * 
     * @param supplierInfo 供应商信息
     * @return 供应商信息集合
     */
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo);

    /**
     * 新增供应商信息
     * 
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    public int insertSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 修改供应商信息
     * 
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    public int updateSupplierInfo(SupplierInfo supplierInfo);

    /**
     * 批量删除供应商信息
     * 
     * @param ids 需要删除的供应商信息主键集合
     * @return 结果
     */
    public int deleteSupplierInfoByIds(Long[] ids);

    /**
     * 删除供应商信息信息
     * 
     * @param id 供应商信息主键
     * @return 结果
     */
    public int deleteSupplierInfoById(Long id);

    /**
     * 校验供应商编码是否唯一
     * 
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    public String checkSupplierCodeUnique(SupplierInfo supplierInfo);

    /**
     * 生成供应商编码
     * 
     * @return 供应商编码
     */
    public String generateSupplierCode();

    /**
     * 查询供应商选项列表（用于下拉选择）
     * 
     * @return 供应商选项集合
     */
    public List<SupplierInfo> selectSupplierOptions();
}