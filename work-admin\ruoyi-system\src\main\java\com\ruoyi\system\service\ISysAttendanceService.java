package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysAttendance;

/**
 * 考勤记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ISysAttendanceService 
{
    /**
     * 查询考勤记录
     * 
     * @param attendanceId 考勤记录主键
     * @return 考勤记录
     */
    public SysAttendance selectSysAttendanceByAttendanceId(Long attendanceId);

    /**
     * 查询考勤记录列表
     * 
     * @param sysAttendance 考勤记录
     * @return 考勤记录集合
     */
    public List<SysAttendance> selectSysAttendanceList(SysAttendance sysAttendance);

    /**
     * 新增考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int insertSysAttendance(SysAttendance sysAttendance);

    /**
     * 修改考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    public int updateSysAttendance(SysAttendance sysAttendance);

    /**
     * 批量删除考勤记录
     * 
     * @param attendanceIds 需要删除的考勤记录主键集合
     * @return 结果
     */
    public int deleteSysAttendanceByAttendanceIds(Long[] attendanceIds);

    /**
     * 删除考勤记录信息
     * 
     * @param attendanceId 考勤记录主键
     * @return 结果
     */
    public int deleteSysAttendanceByAttendanceId(Long attendanceId);

    /**
     * 查询用户当日考勤记录
     * 
     * @param userId 用户ID
     * @return 考勤记录
     */
    public SysAttendance selectTodayAttendanceByUserId(Long userId);

    /**
     * 签到
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int checkIn(Long userId);

    /**
     * 签退
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int checkOut(Long userId);
} 