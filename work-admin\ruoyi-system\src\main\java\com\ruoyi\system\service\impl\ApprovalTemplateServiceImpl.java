package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ApprovalTemplateMapper;
import com.ruoyi.system.domain.ApprovalTemplate;
import com.ruoyi.system.service.IApprovalTemplateService;

/**
 * 审批模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ApprovalTemplateServiceImpl implements IApprovalTemplateService 
{
    @Autowired
    private ApprovalTemplateMapper approvalTemplateMapper;

    /**
     * 查询审批模板
     * 
     * @param id 审批模板主键
     * @return 审批模板
     */
    @Override
    public ApprovalTemplate selectApprovalTemplateById(Long id)
    {
        return approvalTemplateMapper.selectApprovalTemplateById(id);
    }

    /**
     * 查询审批模板列表
     * 
     * @param approvalTemplate 审批模板
     * @return 审批模板
     */
    @Override
    public List<ApprovalTemplate> selectApprovalTemplateList(ApprovalTemplate approvalTemplate)
    {
        return approvalTemplateMapper.selectApprovalTemplateList(approvalTemplate);
    }

    /**
     * 新增审批模板
     * 
     * @param approvalTemplate 审批模板
     * @return 结果
     */
    @Override
    public int insertApprovalTemplate(ApprovalTemplate approvalTemplate)
    {
        return approvalTemplateMapper.insertApprovalTemplate(approvalTemplate);
    }

    /**
     * 修改审批模板
     * 
     * @param approvalTemplate 审批模板
     * @return 结果
     */
    @Override
    public int updateApprovalTemplate(ApprovalTemplate approvalTemplate)
    {
        return approvalTemplateMapper.updateApprovalTemplate(approvalTemplate);
    }

    /**
     * 批量删除审批模板
     * 
     * @param ids 需要删除的审批模板主键
     * @return 结果
     */
    @Override
    public int deleteApprovalTemplateByIds(Long[] ids)
    {
        return approvalTemplateMapper.deleteApprovalTemplateByIds(ids);
    }

    /**
     * 删除审批模板信息
     * 
     * @param id 审批模板主键
     * @return 结果
     */
    @Override
    public int deleteApprovalTemplateById(Long id)
    {
        return approvalTemplateMapper.deleteApprovalTemplateById(id);
    }

    /**
     * 设置默认模板
     * 
     * @param id 模板ID
     * @return 结果
     */
    @Override
    public int setDefaultTemplate(Long id)
    {
        // 先清除所有默认模板
        approvalTemplateMapper.clearDefaultTemplate();
        // 再设置指定模板为默认
        return approvalTemplateMapper.setDefaultTemplate(id);
    }

    /**
     * 改变模板状态
     * 
     * @param id 模板ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int changeTemplateStatus(Long id, Boolean status)
    {
        return approvalTemplateMapper.changeTemplateStatus(id, status);
    }

    /**
     * 根据条件获取审批模板
     * 
     * @param businessType 业务类型
     * @param procurementType 采购类型
     * @param amount 金额
     * @param urgencyLevel 紧急程度
     * @return 审批模板
     */
    @Override
    public ApprovalTemplate getTemplateByCondition(String businessType, String procurementType, 
                                                  java.math.BigDecimal amount, Integer urgencyLevel)
    {
        return approvalTemplateMapper.getTemplateByCondition(businessType, procurementType, amount, urgencyLevel);
    }
}