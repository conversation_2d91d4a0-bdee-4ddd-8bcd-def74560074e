package com.ruoyi.system.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.ocr.TencentOcrUtils;
import com.ruoyi.system.domain.InvoiceRecognition;
import com.ruoyi.system.mapper.InvoiceRecognitionMapper;
import com.ruoyi.system.service.IInvoiceRecognitionService;

/**
 * 发票识别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@Service
public class InvoiceRecognitionServiceImpl implements IInvoiceRecognitionService 
{
    private static final Logger log = LoggerFactory.getLogger(InvoiceRecognitionServiceImpl.class);

    @Autowired
    private InvoiceRecognitionMapper invoiceRecognitionMapper;

    @Autowired
    private TencentOcrUtils tencentOcrUtils;

    /**
     * 查询发票识别
     * 
     * @param id 发票识别主键
     * @return 发票识别
     */
    @Override
    public InvoiceRecognition selectInvoiceRecognitionById(Long id)
    {
        InvoiceRecognition invoiceRecognition = invoiceRecognitionMapper.selectInvoiceRecognitionById(id);
        if (invoiceRecognition != null && StringUtils.isNotEmpty(invoiceRecognition.getGoodsInfoJson()))
        {
            // 解析商品信息JSON
            try
            {
                invoiceRecognition.setGoodsInfoList(
                    JSON.parseArray(invoiceRecognition.getGoodsInfoJson(), 
                    com.ruoyi.system.domain.InvoiceGoodsInfo.class)
                );
            }
            catch (Exception e)
            {
                log.warn("解析商品信息JSON失败: {}", invoiceRecognition.getGoodsInfoJson(), e);
            }
        }
        return invoiceRecognition;
    }

    /**
     * 查询发票识别列表
     * 
     * @param invoiceRecognition 发票识别
     * @return 发票识别
     */
    @Override
    public List<InvoiceRecognition> selectInvoiceRecognitionList(InvoiceRecognition invoiceRecognition)
    {
        return invoiceRecognitionMapper.selectInvoiceRecognitionList(invoiceRecognition);
    }

    /**
     * 新增发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    @Override
    public int insertInvoiceRecognition(InvoiceRecognition invoiceRecognition)
    {
        // 处理商品信息列表
        if (invoiceRecognition.getGoodsInfoList() != null && !invoiceRecognition.getGoodsInfoList().isEmpty())
        {
            invoiceRecognition.setGoodsInfoJson(JSON.toJSONString(invoiceRecognition.getGoodsInfoList()));
        }
        
        invoiceRecognition.setCreateTime(DateUtils.getNowDate());
        return invoiceRecognitionMapper.insertInvoiceRecognition(invoiceRecognition);
    }

    /**
     * 修改发票识别
     * 
     * @param invoiceRecognition 发票识别
     * @return 结果
     */
    @Override
    public int updateInvoiceRecognition(InvoiceRecognition invoiceRecognition)
    {
        // 处理商品信息列表
        if (invoiceRecognition.getGoodsInfoList() != null && !invoiceRecognition.getGoodsInfoList().isEmpty())
        {
            invoiceRecognition.setGoodsInfoJson(JSON.toJSONString(invoiceRecognition.getGoodsInfoList()));
        }
        
        invoiceRecognition.setUpdateTime(DateUtils.getNowDate());
        return invoiceRecognitionMapper.updateInvoiceRecognition(invoiceRecognition);
    }

    /**
     * 批量删除发票识别
     * 
     * @param ids 需要删除的发票识别主键
     * @return 结果
     */
    @Override
    public int deleteInvoiceRecognitionByIds(Long[] ids)
    {
        return invoiceRecognitionMapper.deleteInvoiceRecognitionByIds(ids);
    }

    /**
     * 删除发票识别信息
     * 
     * @param id 发票识别主键
     * @return 结果
     */
    @Override
    public int deleteInvoiceRecognitionById(Long id)
    {
        return invoiceRecognitionMapper.deleteInvoiceRecognitionById(id);
    }

    /**
     * 上传并识别OFD发票
     * 
     * @param file OFD文件
     * @return 发票识别结果
     */
    @Override
    public InvoiceRecognition uploadAndRecognizeOfdInvoice(MultipartFile file)
    {
        try
        {
            // 验证文件类型
            if (!isOfdFile(file))
            {
                InvoiceRecognition result = new InvoiceRecognition();
                result.setStatus("2");
                result.setErrorMessage("文件类型不正确，请上传OFD格式的发票文件");
                return result;
            }

            // 上传文件
            String fileName = FileUploadUtils.upload(RuoYiConfig.getUploadPath(), file);
            String filePath = RuoYiConfig.getUploadPath() + File.separator + fileName;
            
            // 将文件转换为Base64
            byte[] fileBytes = file.getBytes();
            String fileBase64 = Base64.getEncoder().encodeToString(fileBytes);
            
            // 调用识别服务
            InvoiceRecognition result = tencentOcrUtils.recognizeOfdInvoice(null, fileBase64, 1);
            
            // 设置文件信息
            result.setOriginalFileName(file.getOriginalFilename());
            result.setFilePath(fileName);
            result.setCreateTime(new Date());
            
            // 保存到数据库
            insertInvoiceRecognition(result);
            
            return result;
        }
        catch (IOException e)
        {
            log.error("文件上传失败", e);
            InvoiceRecognition result = new InvoiceRecognition();
            result.setStatus("2");
            result.setErrorMessage("文件上传失败: " + e.getMessage());
            return result;
        }
        catch (Exception e)
        {
            log.error("发票识别失败", e);
            InvoiceRecognition result = new InvoiceRecognition();
            result.setStatus("2");
            result.setErrorMessage("发票识别失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 通过URL识别OFD发票
     * 
     * @param fileUrl OFD文件URL
     * @return 发票识别结果
     */
    @Override
    public InvoiceRecognition recognizeOfdInvoiceByUrl(String fileUrl)
    {
        try
        {
            // 调用识别服务
            InvoiceRecognition result = tencentOcrUtils.recognizeOfdInvoice(fileUrl, null, 1);
            result.setFilePath(fileUrl);
            result.setCreateTime(new Date());
            
            // 保存到数据库
            insertInvoiceRecognition(result);
            
            return result;
        }
        catch (Exception e)
        {
            log.error("发票识别失败", e);
            InvoiceRecognition result = new InvoiceRecognition();
            result.setStatus("2");
            result.setErrorMessage("发票识别失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 通过Base64识别OFD发票
     * 
     * @param fileBase64 OFD文件Base64编码
     * @param fileName 文件名
     * @return 发票识别结果
     */
    @Override
    public InvoiceRecognition recognizeOfdInvoiceByBase64(String fileBase64, String fileName)
    {
        try
        {
            // 调用识别服务
            InvoiceRecognition result = tencentOcrUtils.recognizeOfdInvoice(null, fileBase64, 1);
            result.setOriginalFileName(fileName);
            result.setCreateTime(new Date());
            
            // 保存到数据库
            insertInvoiceRecognition(result);
            
            return result;
        }
        catch (Exception e)
        {
            log.error("发票识别失败", e);
            InvoiceRecognition result = new InvoiceRecognition();
            result.setStatus("2");
            result.setErrorMessage("发票识别失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 验证是否为OFD文件
     * 
     * @param file 文件
     * @return 是否为OFD文件
     */
    private boolean isOfdFile(MultipartFile file)
    {
        if (file == null || file.isEmpty())
        {
            return false;
        }
        
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName))
        {
            return false;
        }
        
        return fileName.toLowerCase().endsWith(".ofd");
    }
}
