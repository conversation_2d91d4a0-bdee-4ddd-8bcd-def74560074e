package com.ruoyi.system.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LeaveApplicationMapper;
import com.ruoyi.system.domain.LeaveApplication;
import com.ruoyi.system.service.ILeaveApplicationService;

/**
 * 请假申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class LeaveApplicationServiceImpl implements ILeaveApplicationService 
{
    @Autowired
    private LeaveApplicationMapper leaveApplicationMapper;
    
    @Autowired
    private com.ruoyi.system.service.ILeaveTypeService leaveTypeService;

    /**
     * 查询请假申请
     * 
     * @param id 请假申请主键
     * @return 请假申请
     */
    @Override
    public LeaveApplication selectLeaveApplicationById(Long id)
    {
        return leaveApplicationMapper.selectLeaveApplicationById(id);
    }

    /**
     * 查询请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<LeaveApplication> selectLeaveApplicationList(LeaveApplication leaveApplication)
    {
        return leaveApplicationMapper.selectLeaveApplicationList(leaveApplication);
    }

    /**
     * 根据申请单号查询请假申请
     * 
     * @param appNo 申请单号
     * @return 请假申请
     */
    @Override
    public LeaveApplication selectLeaveApplicationByAppNo(String appNo)
    {
        return leaveApplicationMapper.selectLeaveApplicationByAppNo(appNo);
    }

    /**
     * 校验申请单号是否唯一
     * 
     * @param appNo 申请单号
     * @return 结果
     */
    @Override
    public boolean checkAppNoUnique(String appNo)
    {
        LeaveApplication info = leaveApplicationMapper.checkAppNoUnique(appNo);
        return StringUtils.isNull(info);
    }

    /**
     * 校验申请单号是否唯一
     * 
     * @param leaveApplication 请假申请信息
     * @return 结果
     */
    @Override
    public boolean checkAppNoUnique(LeaveApplication leaveApplication)
    {
        Long id = StringUtils.isNull(leaveApplication.getId()) ? -1L : leaveApplication.getId();
        LeaveApplication info = leaveApplicationMapper.checkAppNoUnique(leaveApplication.getAppNo());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 根据用户ID查询请假申请列表
     * 
     * @param userId 用户ID
     * @return 请假申请集合
     */
    @Override
    public List<LeaveApplication> selectLeaveApplicationByUserId(Long userId)
    {
        return leaveApplicationMapper.selectLeaveApplicationByUserId(userId);
    }

    /**
     * 查询待审批的请假申请列表
     * 
     * @param leaveApplication 请假申请
     * @return 请假申请集合
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<LeaveApplication> selectPendingLeaveApplicationList(LeaveApplication leaveApplication)
    {
        return leaveApplicationMapper.selectPendingLeaveApplicationList(leaveApplication);
    }

    /**
     * 根据用户ID和时间范围查询请假申请
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 请假申请集合
     */
    @Override
    public List<LeaveApplication> selectLeaveApplicationByUserAndDate(Long userId, String startDate, String endDate)
    {
        return leaveApplicationMapper.selectLeaveApplicationByUserAndDate(userId, startDate, endDate);
    }

    /**
     * 生成申请单号
     * 
     * @return 申请单号
     */
    @Override
    public String generateAppNo()
    {
        // 生成格式：QJ + yyyyMMdd + 4位流水号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = "QJ" + dateStr;
        
        // 查询当天最大的申请单号
        String maxAppNo = getMaxAppNoByDate(prefix);
        int sequence = 1;
        if (StringUtils.isNotEmpty(maxAppNo) && maxAppNo.length() >= 12)
        {
            try
            {
                String seqStr = maxAppNo.substring(10);
                sequence = Integer.parseInt(seqStr) + 1;
            }
            catch (NumberFormatException e)
            {
                sequence = 1;
            }
        }
        
        return prefix + String.format("%04d", sequence);
    }

    /**
     * 查询指定日期前缀的最大申请单号
     * 
     * @param prefix 前缀
     * @return 最大申请单号
     */
    private String getMaxAppNoByDate(String prefix)
    {
        // 这里可以通过数据库查询获取最大单号，为简化实现，使用时间戳
        return null;
    }

    /**
     * 新增请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    @Override
    public int insertLeaveApplication(LeaveApplication leaveApplication)
    {
        // 自动生成申请单号
        if (StringUtils.isEmpty(leaveApplication.getAppNo()))
        {
            leaveApplication.setAppNo(generateAppNo());
        }
        
        // 校验申请单号唯一性
        if (!checkAppNoUnique(leaveApplication))
        {
            throw new ServiceException("新增请假申请失败，申请单号已存在");
        }
        
        // 设置申请人信息
        if (StringUtils.isNull(leaveApplication.getUserId()))
        {
            leaveApplication.setUserId(SecurityUtils.getUserId());
        }
        
        // 设置假期类型名称
        if (StringUtils.isNotNull(leaveApplication.getLeaveTypeId()) && StringUtils.isEmpty(leaveApplication.getLeaveTypeName()))
        {
            com.ruoyi.system.domain.LeaveType leaveType = leaveTypeService.selectLeaveTypeById(leaveApplication.getLeaveTypeId());
            if (StringUtils.isNotNull(leaveType))
            {
                leaveApplication.setLeaveTypeName(leaveType.getTypeName());
            }
        }
        
        // 设置默认状态为待审批
        if (StringUtils.isNull(leaveApplication.getStatus()))
        {
            leaveApplication.setStatus(1);
        }
        
        leaveApplication.setCreateTime(DateUtils.getNowDate());
        return leaveApplicationMapper.insertLeaveApplication(leaveApplication);
    }

    /**
     * 修改请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    @Override
    public int updateLeaveApplication(LeaveApplication leaveApplication)
    {
        // 获取原记录
        LeaveApplication oldRecord = selectLeaveApplicationById(leaveApplication.getId());
        if (StringUtils.isNull(oldRecord))
        {
            throw new ServiceException("请假申请不存在");
        }
        
        // 校验是否可以修改
        if (!checkCanEdit(oldRecord))
        {
            throw new ServiceException("当前状态下不允许修改请假申请");
        }
        
        // 校验申请单号唯一性
        if (!checkAppNoUnique(leaveApplication))
        {
            throw new ServiceException("修改请假申请失败，申请单号已存在");
        }
        
        leaveApplication.setUpdateTime(DateUtils.getNowDate());
        return leaveApplicationMapper.updateLeaveApplication(leaveApplication);
    }

    /**
     * 批量删除请假申请
     * 
     * @param ids 需要删除的请假申请主键
     * @return 结果
     */
    @Override
    public int deleteLeaveApplicationByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            LeaveApplication leaveApplication = selectLeaveApplicationById(id);
            if (StringUtils.isNull(leaveApplication))
            {
                throw new ServiceException(String.format("请假申请%1$s不存在，不能删除", id));
            }
            
            // 校验是否可以删除
            if (!checkCanDelete(leaveApplication))
            {
                throw new ServiceException(String.format("请假申请%1$s当前状态下不允许删除", leaveApplication.getAppNo()));
            }
        }
        return leaveApplicationMapper.deleteLeaveApplicationByIds(ids);
    }

    /**
     * 删除请假申请信息
     * 
     * @param id 请假申请主键
     * @return 结果
     */
    @Override
    public int deleteLeaveApplicationById(Long id)
    {
        LeaveApplication leaveApplication = selectLeaveApplicationById(id);
        if (StringUtils.isNull(leaveApplication))
        {
            throw new ServiceException(String.format("请假申请%1$s不存在，不能删除", id));
        }
        
        // 校验是否可以删除
        if (!checkCanDelete(leaveApplication))
        {
            throw new ServiceException(String.format("请假申请%1$s当前状态下不允许删除", leaveApplication.getAppNo()));
        }
        
        return leaveApplicationMapper.deleteLeaveApplicationById(id);
    }

    /**
     * 审批请假申请
     * 
     * @param id 请假申请ID
     * @param status 审批状态 2-通过 3-拒绝
     * @param remark 审批意见
     * @return 结果
     */
    @Override
    public int approveLeaveApplication(Long id, Integer status, String remark)
    {
        LeaveApplication leaveApplication = selectLeaveApplicationById(id);
        if (StringUtils.isNull(leaveApplication))
        {
            throw new ServiceException("请假申请不存在");
        }
        
        // 只有待审批状态才能审批
        if (!leaveApplication.getStatus().equals(1))
        {
            throw new ServiceException("当前状态下不允许审批");
        }
        
        // 校验审批状态
        if (!status.equals(2) && !status.equals(3))
        {
            throw new ServiceException("审批状态参数错误");
        }
        
        LeaveApplication updateRecord = new LeaveApplication();
        updateRecord.setId(id);
        updateRecord.setStatus(status);
        updateRecord.setRemark(remark);
        updateRecord.setUpdateTime(DateUtils.getNowDate());
        updateRecord.setUpdateBy(SecurityUtils.getUsername());
        
        return leaveApplicationMapper.updateLeaveApplication(updateRecord);
    }

    /**
     * 撤销请假申请
     * 
     * @param id 请假申请ID
     * @return 结果
     */
    @Override
    public int cancelLeaveApplication(Long id)
    {
        LeaveApplication leaveApplication = selectLeaveApplicationById(id);
        if (StringUtils.isNull(leaveApplication))
        {
            throw new ServiceException("请假申请不存在");
        }
        
        // 只有待审批状态才能撤销
        if (!leaveApplication.getStatus().equals(1))
        {
            throw new ServiceException("当前状态下不允许撤销");
        }
        
        // 只有申请人本人才能撤销
        if (!leaveApplication.getUserId().equals(SecurityUtils.getUserId()))
        {
            throw new ServiceException("只有申请人本人才能撤销申请");
        }
        
        LeaveApplication updateRecord = new LeaveApplication();
        updateRecord.setId(id);
        updateRecord.setStatus(4); // 已撤销
        updateRecord.setUpdateTime(DateUtils.getNowDate());
        updateRecord.setUpdateBy(SecurityUtils.getUsername());
        
        return leaveApplicationMapper.updateLeaveApplication(updateRecord);
    }

    /**
     * 校验是否可以删除请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    @Override
    public boolean checkCanDelete(LeaveApplication leaveApplication)
    {
        // 只有待审批和已撤销状态可以删除
        return leaveApplication.getStatus().equals(1) || leaveApplication.getStatus().equals(4);
    }

    /**
     * 校验是否可以修改请假申请
     * 
     * @param leaveApplication 请假申请
     * @return 结果
     */
    @Override
    public boolean checkCanEdit(LeaveApplication leaveApplication)
    {
        // 只有待审批状态可以修改
        return leaveApplication.getStatus().equals(1);
    }
}
