package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LeaveTypeMapper;
import com.ruoyi.system.domain.LeaveType;
import com.ruoyi.system.service.ILeaveTypeService;

/**
 * 假期类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class LeaveTypeServiceImpl implements ILeaveTypeService 
{
    @Autowired
    private LeaveTypeMapper leaveTypeMapper;

    /**
     * 查询假期类型
     * 
     * @param id 假期类型主键
     * @return 假期类型
     */
    @Override
    public LeaveType selectLeaveTypeById(Long id)
    {
        return leaveTypeMapper.selectLeaveTypeById(id);
    }

    /**
     * 查询假期类型列表
     * 
     * @param leaveType 假期类型
     * @return 假期类型
     */
    @Override
    public List<LeaveType> selectLeaveTypeList(LeaveType leaveType)
    {
        return leaveTypeMapper.selectLeaveTypeList(leaveType);
    }

    /**
     * 查询所有正常状态的假期类型
     * 
     * @return 假期类型集合
     */
    @Override
    public List<LeaveType> selectLeaveTypeAll()
    {
        return leaveTypeMapper.selectLeaveTypeAll();
    }

    /**
     * 根据假期代码查询假期类型
     * 
     * @param typeCode 假期代码
     * @return 假期类型
     */
    @Override
    public LeaveType selectLeaveTypeByCode(String typeCode)
    {
        return leaveTypeMapper.selectLeaveTypeByCode(typeCode);
    }

    /**
     * 校验假期代码是否唯一
     * 
     * @param typeCode 假期代码
     * @return 结果
     */
    @Override
    public boolean checkTypeCodeUnique(String typeCode)
    {
        LeaveType info = leaveTypeMapper.checkTypeCodeUnique(typeCode);
        return StringUtils.isNull(info);
    }

    /**
     * 校验假期代码是否唯一
     * 
     * @param leaveType 假期类型信息
     * @return 结果
     */
    @Override
    public boolean checkTypeCodeUnique(LeaveType leaveType)
    {
        Long id = StringUtils.isNull(leaveType.getId()) ? -1L : leaveType.getId();
        LeaveType info = leaveTypeMapper.checkTypeCodeUnique(leaveType.getTypeCode());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 新增假期类型
     * 
     * @param leaveType 假期类型
     * @return 结果
     */
    @Override
    public int insertLeaveType(LeaveType leaveType)
    {
        // 校验假期代码唯一性
        if (!checkTypeCodeUnique(leaveType))
        {
            throw new ServiceException("新增假期类型'" + leaveType.getTypeName() + "'失败，假期代码已存在");
        }
        
        leaveType.setCreateTime(DateUtils.getNowDate());
        return leaveTypeMapper.insertLeaveType(leaveType);
    }

    /**
     * 修改假期类型
     * 
     * @param leaveType 假期类型
     * @return 结果
     */
    @Override
    public int updateLeaveType(LeaveType leaveType)
    {
        // 校验假期代码唯一性
        if (!checkTypeCodeUnique(leaveType))
        {
            throw new ServiceException("修改假期类型'" + leaveType.getTypeName() + "'失败，假期代码已存在");
        }
        
        leaveType.setUpdateTime(DateUtils.getNowDate());
        return leaveTypeMapper.updateLeaveType(leaveType);
    }

    /**
     * 批量删除假期类型
     * 
     * @param ids 需要删除的假期类型主键
     * @return 结果
     */
    @Override
    public int deleteLeaveTypeByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            LeaveType leaveType = selectLeaveTypeById(id);
            if (StringUtils.isNull(leaveType))
            {
                throw new ServiceException(String.format("假期类型%1$s不存在，不能删除", id));
            }
            // 可以添加业务逻辑校验，比如检查是否有关联的请假申请
        }
        return leaveTypeMapper.deleteLeaveTypeByIds(ids);
    }

    /**
     * 删除假期类型信息
     * 
     * @param id 假期类型主键
     * @return 结果
     */
    @Override
    public int deleteLeaveTypeById(Long id)
    {
        LeaveType leaveType = selectLeaveTypeById(id);
        if (StringUtils.isNull(leaveType))
        {
            throw new ServiceException(String.format("假期类型%1$s不存在，不能删除", id));
        }
        // 可以添加业务逻辑校验，比如检查是否有关联的请假申请
        return leaveTypeMapper.deleteLeaveTypeById(id);
    }
} 