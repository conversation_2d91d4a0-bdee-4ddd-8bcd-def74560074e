package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;

import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.ProcurementItem;
import com.ruoyi.system.mapper.ProcurementApplicationMapper;
import com.ruoyi.system.domain.ProcurementApplication;
import com.ruoyi.system.service.IProcurementApplicationService;

/**
 * 采购申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProcurementApplicationServiceImpl implements IProcurementApplicationService
{
    @Autowired
    private ProcurementApplicationMapper procurementApplicationMapper;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询采购申请
     *
     * @param id 采购申请主键
     * @return 采购申请
     */
    @Override
    public ProcurementApplication selectProcurementApplicationById(Long id)
    {
        return procurementApplicationMapper.selectProcurementApplicationById(id);
    }

    /**
     * 查询采购申请列表
     *
     * @param procurementApplication 采购申请
     * @return 采购申请
     */
    @Override
    public List<ProcurementApplication> selectProcurementApplicationList(ProcurementApplication procurementApplication)
    {
        return procurementApplicationMapper.selectProcurementApplicationList(procurementApplication);
    }

    /**
     * 新增采购申请
     *
     * @param procurementApplication 采购申请
     * @return 结果
     */
    @Transactional
    @Override
    public int insertProcurementApplication(ProcurementApplication procurementApplication)
    {
        // 设置申请人信息
        SysUser currentUser = userService.selectUserById(SecurityUtils.getUserId());
        procurementApplication.setApplicantId(currentUser.getUserId());
        procurementApplication.setApplicantName(currentUser.getNickName());
        procurementApplication.setDeptId(currentUser.getDeptId());
        procurementApplication.setDeptName(currentUser.getDept().getDeptName());

        // 生成申请单号
        if (StringUtils.isEmpty(procurementApplication.getAppNo())) {
            procurementApplication.setAppNo(generateAppNo());
        }

        // 计算总金额
        calculateTotalAmount(procurementApplication);

        procurementApplication.setCreateTime(DateUtils.getNowDate());
        procurementApplication.setCreateBy(SecurityUtils.getUsername());
        procurementApplication.setDelFlag("0");

        int rows = procurementApplicationMapper.insertProcurementApplication(procurementApplication);
        insertProcurementItem(procurementApplication);
        return rows;
    }

    /**
     * 修改采购申请
     *
     * @param procurementApplication 采购申请
     * @return 结果
     */
    @Transactional
    @Override
    public int updateProcurementApplication(ProcurementApplication procurementApplication)
    {
        // 计算总金额
        calculateTotalAmount(procurementApplication);

        procurementApplication.setUpdateTime(DateUtils.getNowDate());
        procurementApplication.setUpdateBy(SecurityUtils.getUsername());

        procurementApplicationMapper.deleteProcurementItemByAppId(procurementApplication.getId());
        insertProcurementItem(procurementApplication);
        return procurementApplicationMapper.updateProcurementApplication(procurementApplication);
    }

    /**
     * 批量删除采购申请
     *
     * @param ids 需要删除的采购申请主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteProcurementApplicationByIds(Long[] ids)
    {
        procurementApplicationMapper.deleteProcurementItemByAppIds(ids);
        return procurementApplicationMapper.deleteProcurementApplicationByIds(ids);
    }

    /**
     * 删除采购申请信息
     *
     * @param id 采购申请主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteProcurementApplicationById(Long id)
    {
        procurementApplicationMapper.deleteProcurementItemByAppId(id);
        return procurementApplicationMapper.deleteProcurementApplicationById(id);
    }

    @Override
    public ProcurementApplication selectProcurementApplicationByAppNo(String appNo) {
        return procurementApplicationMapper.selectProcurementApplicationByAppNo(appNo);
    }

    @Override
    public String generateAppNo() {
        return procurementApplicationMapper.generateAppNo();
    }

    @Transactional
    @Override
    public int submitProcurementApplication(ProcurementApplication procurementApplication) {
        // 提交时将状态改为待审批
        procurementApplication.setStatus(2);
        procurementApplication.setCurrentStep(1);
        return updateProcurementApplication(procurementApplication);
    }

    /**
     * 新增采购明细信息
     *
     * @param procurementApplication 采购申请对象
     */
    public void insertProcurementItem(ProcurementApplication procurementApplication)
    {
        List<ProcurementItem> procurementItemList = procurementApplication.getProcurementItemList();
        Long id = procurementApplication.getId();
        if (StringUtils.isNotNull(procurementItemList))
        {
            List<ProcurementItem> list = new ArrayList<ProcurementItem>();
            for (ProcurementItem procurementItem : procurementItemList)
            {
                procurementItem.setAppId(id);
                procurementItem.setCreateTime(DateUtils.getNowDate());
                procurementItem.setCreateBy(SecurityUtils.getUsername());
                procurementItem.setDelFlag("0");
                list.add(procurementItem);
            }
            if (list.size() > 0)
            {
                procurementApplicationMapper.batchProcurementItem(list);
            }
        }
    }

    /**
     * 计算总金额
     *
     * @param procurementApplication 采购申请对象
     */
    private void calculateTotalAmount(ProcurementApplication procurementApplication) {
        List<ProcurementItem> itemList = procurementApplication.getProcurementItemList();
        if (StringUtils.isNotNull(itemList)) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (ProcurementItem item : itemList) {
                if (item.getTotalPrice() != null) {
                    totalAmount = totalAmount.add(item.getTotalPrice());
                }
            }
            procurementApplication.setTotalAmount(totalAmount);
        }
    }
}
