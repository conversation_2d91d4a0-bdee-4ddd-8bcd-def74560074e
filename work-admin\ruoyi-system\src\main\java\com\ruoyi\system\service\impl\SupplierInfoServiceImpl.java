package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.SupplierInfoMapper;
import com.ruoyi.system.domain.SupplierInfo;
import com.ruoyi.system.service.ISupplierInfoService;

/**
 * 供应商信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SupplierInfoServiceImpl implements ISupplierInfoService 
{
    @Autowired
    private SupplierInfoMapper supplierInfoMapper;

    /**
     * 查询供应商信息
     * 
     * @param id 供应商信息主键
     * @return 供应商信息
     */
    @Override
    public SupplierInfo selectSupplierInfoById(Long id)
    {
        return supplierInfoMapper.selectSupplierInfoById(id);
    }

    /**
     * 查询供应商信息列表
     * 
     * @param supplierInfo 供应商信息
     * @return 供应商信息
     */
    @Override
    public List<SupplierInfo> selectSupplierInfoList(SupplierInfo supplierInfo)
    {
        return supplierInfoMapper.selectSupplierInfoList(supplierInfo);
    }

    /**
     * 新增供应商信息
     *
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSupplierInfo(SupplierInfo supplierInfo)
    {
        // 如果没有提供供应商编码，则自动生成
        if (StringUtils.isEmpty(supplierInfo.getSupplierCode())) {
            supplierInfo.setSupplierCode(generateSupplierCode());
        }

        return supplierInfoMapper.insertSupplierInfo(supplierInfo);
    }

    /**
     * 修改供应商信息
     *
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSupplierInfo(SupplierInfo supplierInfo)
    {
        return supplierInfoMapper.updateSupplierInfo(supplierInfo);
    }

    /**
     * 批量删除供应商信息
     *
     * @param ids 需要删除的供应商信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSupplierInfoByIds(Long[] ids)
    {
        return supplierInfoMapper.deleteSupplierInfoByIds(ids);
    }

    /**
     * 删除供应商信息信息
     *
     * @param id 供应商信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSupplierInfoById(Long id)
    {
        return supplierInfoMapper.deleteSupplierInfoById(id);
    }

    /**
     * 校验供应商编码是否唯一
     * 
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    @Override
    public String checkSupplierCodeUnique(SupplierInfo supplierInfo)
    {
        Long id = StringUtils.isNull(supplierInfo.getId()) ? -1L : supplierInfo.getId();
        SupplierInfo info = supplierInfoMapper.checkSupplierCodeUnique(supplierInfo.getSupplierCode());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return "1"; // 不唯一
        }
        return "0"; // 唯一
    }

    /**
     * 生成供应商编码
     * 
     * @return 供应商编码
     */
    @Override
    public String generateSupplierCode()
    {
        return supplierInfoMapper.generateSupplierCode();
    }

    /**
     * 查询供应商选项列表（用于下拉选择）
     * 
     * @return 供应商选项集合
     */
    @Override
    public List<SupplierInfo> selectSupplierOptions()
    {
        return supplierInfoMapper.selectSupplierOptions();
    }
}