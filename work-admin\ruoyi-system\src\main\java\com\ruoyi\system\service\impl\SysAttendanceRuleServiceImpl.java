package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysAttendanceRuleMapper;
import com.ruoyi.system.domain.SysAttendanceRule;
import com.ruoyi.system.service.ISysAttendanceRuleService;

/**
 * 考勤规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SysAttendanceRuleServiceImpl implements ISysAttendanceRuleService 
{
    @Autowired
    private SysAttendanceRuleMapper sysAttendanceRuleMapper;

    /**
     * 查询考勤规则
     * 
     * @param ruleId 考勤规则主键
     * @return 考勤规则
     */
    @Override
    public SysAttendanceRule selectSysAttendanceRuleByRuleId(Long ruleId)
    {
        return sysAttendanceRuleMapper.selectSysAttendanceRuleByRuleId(ruleId);
    }

    /**
     * 查询考勤规则列表
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 考勤规则
     */
    @Override
    public List<SysAttendanceRule> selectSysAttendanceRuleList(SysAttendanceRule sysAttendanceRule)
    {
        return sysAttendanceRuleMapper.selectSysAttendanceRuleList(sysAttendanceRule);
    }

    /**
     * 新增考勤规则
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 结果
     */
    @Override
    public int insertSysAttendanceRule(SysAttendanceRule sysAttendanceRule)
    {
        sysAttendanceRule.setCreateTime(DateUtils.getNowDate());
        return sysAttendanceRuleMapper.insertSysAttendanceRule(sysAttendanceRule);
    }

    /**
     * 修改考勤规则
     * 
     * @param sysAttendanceRule 考勤规则
     * @return 结果
     */
    @Override
    public int updateSysAttendanceRule(SysAttendanceRule sysAttendanceRule)
    {
        sysAttendanceRule.setUpdateTime(DateUtils.getNowDate());
        return sysAttendanceRuleMapper.updateSysAttendanceRule(sysAttendanceRule);
    }

    /**
     * 批量删除考勤规则
     * 
     * @param ruleIds 需要删除的考勤规则主键
     * @return 结果
     */
    @Override
    public int deleteSysAttendanceRuleByRuleIds(Long[] ruleIds)
    {
        return sysAttendanceRuleMapper.deleteSysAttendanceRuleByRuleIds(ruleIds);
    }

    /**
     * 删除考勤规则信息
     * 
     * @param ruleId 考勤规则主键
     * @return 结果
     */
    @Override
    public int deleteSysAttendanceRuleByRuleId(Long ruleId)
    {
        return sysAttendanceRuleMapper.deleteSysAttendanceRuleByRuleId(ruleId);
    }
} 