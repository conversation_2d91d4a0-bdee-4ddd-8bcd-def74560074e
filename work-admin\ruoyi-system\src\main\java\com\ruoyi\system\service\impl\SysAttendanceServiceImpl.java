package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysAttendanceMapper;
import com.ruoyi.system.domain.SysAttendance;
import com.ruoyi.system.service.ISysAttendanceService;

/**
 * 考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SysAttendanceServiceImpl implements ISysAttendanceService 
{
    @Autowired
    private SysAttendanceMapper sysAttendanceMapper;

    /**
     * 查询考勤记录
     * 
     * @param attendanceId 考勤记录主键
     * @return 考勤记录
     */
    @Override
    public SysAttendance selectSysAttendanceByAttendanceId(Long attendanceId)
    {
        return sysAttendanceMapper.selectSysAttendanceByAttendanceId(attendanceId);
    }

    /**
     * 查询考勤记录列表
     * 
     * @param sysAttendance 考勤记录
     * @return 考勤记录
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysAttendance> selectSysAttendanceList(SysAttendance sysAttendance)
    {
        return sysAttendanceMapper.selectSysAttendanceList(sysAttendance);
    }

    /**
     * 新增考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    @Override
    public int insertSysAttendance(SysAttendance sysAttendance)
    {
        sysAttendance.setCreateTime(DateUtils.getNowDate());
        return sysAttendanceMapper.insertSysAttendance(sysAttendance);
    }

    /**
     * 修改考勤记录
     * 
     * @param sysAttendance 考勤记录
     * @return 结果
     */
    @Override
    public int updateSysAttendance(SysAttendance sysAttendance)
    {
        sysAttendance.setUpdateTime(DateUtils.getNowDate());
        return sysAttendanceMapper.updateSysAttendance(sysAttendance);
    }

    /**
     * 批量删除考勤记录
     * 
     * @param attendanceIds 需要删除的考勤记录主键
     * @return 结果
     */
    @Override
    public int deleteSysAttendanceByAttendanceIds(Long[] attendanceIds)
    {
        return sysAttendanceMapper.deleteSysAttendanceByAttendanceIds(attendanceIds);
    }

    /**
     * 删除考勤记录信息
     * 
     * @param attendanceId 考勤记录主键
     * @return 结果
     */
    @Override
    public int deleteSysAttendanceByAttendanceId(Long attendanceId)
    {
        return sysAttendanceMapper.deleteSysAttendanceByAttendanceId(attendanceId);
    }

    /**
     * 查询用户当日考勤记录
     * 
     * @param userId 用户ID
     * @return 考勤记录
     */
    @Override
    public SysAttendance selectTodayAttendanceByUserId(Long userId)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(new Date());
        return sysAttendanceMapper.selectTodayAttendanceByUserId(userId, today);
    }

    /**
     * 签到
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int checkIn(Long userId)
    {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(now);
        
        SysAttendance attendance = new SysAttendance();
        attendance.setUserId(userId);
        attendance.setAttendanceDate(DateUtils.parseDate(today));
        attendance.setCheckInTime(now);
        attendance.setCreateBy(SecurityUtils.getUsername());
        attendance.setCreateTime(now);
        attendance.setUpdateBy(SecurityUtils.getUsername());
        attendance.setUpdateTime(now);
        
        // 计算是否迟到 (默认9点上班)
        String workStartTime = "09:00:00";
        try {
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            Date startTime = timeFormat.parse(workStartTime);
            Date currentTime = timeFormat.parse(timeFormat.format(now));
            
            long diffInMillies = currentTime.getTime() - startTime.getTime();
            long diffInMinutes = diffInMillies / (1000 * 60);
            
            if (diffInMinutes > 0) {
                attendance.setAttendanceStatus("1"); // 迟到
                attendance.setLateMinutes((int) diffInMinutes);
            } else {
                attendance.setAttendanceStatus("0"); // 正常
                attendance.setLateMinutes(0);
            }
        } catch (Exception e) {
            attendance.setAttendanceStatus("0"); // 默认正常
            attendance.setLateMinutes(0);
        }
        
        return sysAttendanceMapper.checkIn(attendance);
    }

    /**
     * 签退
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int checkOut(Long userId)
    {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(now);
        
        // 查询当日考勤记录
        SysAttendance todayAttendance = sysAttendanceMapper.selectTodayAttendanceByUserId(userId, today);
        if (todayAttendance == null || todayAttendance.getCheckInTime() == null) {
            return 0; // 未签到不能签退
        }
        
        SysAttendance attendance = new SysAttendance();
        attendance.setUserId(userId);
        attendance.setAttendanceDate(DateUtils.parseDate(today));
        attendance.setCheckOutTime(now);
        attendance.setUpdateBy(SecurityUtils.getUsername());
        attendance.setUpdateTime(now);
        
        // 计算工作时长
        long diffInMillies = now.getTime() - todayAttendance.getCheckInTime().getTime();
        double workHours = diffInMillies / (1000.0 * 60 * 60);
        attendance.setWorkHours(new BigDecimal(workHours).setScale(2, BigDecimal.ROUND_HALF_UP));
        
        // 计算是否早退 (默认18点下班)
        String workEndTime = "18:00:00";
        try {
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            Date endTime = timeFormat.parse(workEndTime);
            Date currentTime = timeFormat.parse(timeFormat.format(now));
            
            long diffInMinutes = (endTime.getTime() - currentTime.getTime()) / (1000 * 60);
            
            String currentStatus = todayAttendance.getAttendanceStatus();
            if (diffInMinutes > 0) {
                // 早退
                if ("1".equals(currentStatus)) {
                    attendance.setAttendanceStatus("2"); // 迟到且早退
                } else {
                    attendance.setAttendanceStatus("2"); // 早退
                }
                attendance.setEarlyMinutes((int) diffInMinutes);
            } else {
                // 正常下班
                attendance.setAttendanceStatus(currentStatus); // 保持原状态
                attendance.setEarlyMinutes(0);
            }
        } catch (Exception e) {
            attendance.setAttendanceStatus(todayAttendance.getAttendanceStatus());
            attendance.setEarlyMinutes(0);
        }
        
        return sysAttendanceMapper.checkOut(attendance);
    }
} 