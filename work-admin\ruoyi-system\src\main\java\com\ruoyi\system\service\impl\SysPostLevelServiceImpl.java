package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysPostLevelMapper;
import com.ruoyi.system.domain.SysPostLevel;
import com.ruoyi.system.service.ISysPostLevelService;

/**
 * 岗位级别工资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SysPostLevelServiceImpl implements ISysPostLevelService 
{
    @Autowired
    private SysPostLevelMapper sysPostLevelMapper;

    /**
     * 查询岗位级别工资
     * 
     * @param levelId 岗位级别工资主键
     * @return 岗位级别工资
     */
    @Override
    public SysPostLevel selectSysPostLevelByLevelId(Long levelId)
    {
        return sysPostLevelMapper.selectSysPostLevelByLevelId(levelId);
    }

    /**
     * 查询岗位级别工资列表
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 岗位级别工资
     */
    @Override
    public List<SysPostLevel> selectSysPostLevelList(SysPostLevel sysPostLevel)
    {
        return sysPostLevelMapper.selectSysPostLevelList(sysPostLevel);
    }

    /**
     * 根据岗位ID查询级别工资列表
     * 
     * @param postId 岗位ID
     * @return 岗位级别工资集合
     */
    @Override
    public List<SysPostLevel> selectSysPostLevelByPostId(Long postId)
    {
        return sysPostLevelMapper.selectSysPostLevelByPostId(postId);
    }

    /**
     * 新增岗位级别工资
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 结果
     */
    @Override
    public int insertSysPostLevel(SysPostLevel sysPostLevel)
    {
        return sysPostLevelMapper.insertSysPostLevel(sysPostLevel);
    }

    /**
     * 修改岗位级别工资
     * 
     * @param sysPostLevel 岗位级别工资
     * @return 结果
     */
    @Override
    public int updateSysPostLevel(SysPostLevel sysPostLevel)
    {
        return sysPostLevelMapper.updateSysPostLevel(sysPostLevel);
    }

    /**
     * 批量删除岗位级别工资
     * 
     * @param levelIds 需要删除的岗位级别工资主键
     * @return 结果
     */
    @Override
    public int deleteSysPostLevelByLevelIds(Long[] levelIds)
    {
        return sysPostLevelMapper.deleteSysPostLevelByLevelIds(levelIds);
    }

    /**
     * 删除岗位级别工资信息
     * 
     * @param levelId 岗位级别工资主键
     * @return 结果
     */
    @Override
    public int deleteSysPostLevelByLevelId(Long levelId)
    {
        return sysPostLevelMapper.deleteSysPostLevelByLevelId(levelId);
    }

    /**
     * 根据岗位ID删除级别工资
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int deleteSysPostLevelByPostId(Long postId)
    {
        return sysPostLevelMapper.deleteSysPostLevelByPostId(postId);
    }

    /**
     * 批量保存岗位级别工资
     * 
     * @param postId 岗位ID
     * @param postLevelList 级别工资列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSavePostLevels(Long postId, List<SysPostLevel> postLevelList)
    {
        // 先删除该岗位的所有级别工资
        sysPostLevelMapper.deleteSysPostLevelByPostId(postId);
        
        // 再批量插入新的级别工资
        int result = 0;
        for (SysPostLevel postLevel : postLevelList) {
            postLevel.setPostId(postId);
            result += sysPostLevelMapper.insertSysPostLevel(postLevel);
        }
        return result;
    }

    /**
     * 校验级别编码是否唯一
     * 
     * @param postLevel 岗位级别工资信息
     * @return 结果
     */
    @Override
    public String checkLevelCodeUnique(SysPostLevel postLevel)
    {
        Long levelId = StringUtils.isNull(postLevel.getLevelId()) ? -1L : postLevel.getLevelId();
        SysPostLevel info = sysPostLevelMapper.checkLevelCodeUnique(postLevel.getPostId(), postLevel.getLevelCode());
        if (StringUtils.isNotNull(info) && info.getLevelId().longValue() != levelId.longValue())
        {
            return "1"; // 不唯一
        }
        return "0"; // 唯一
    }
}