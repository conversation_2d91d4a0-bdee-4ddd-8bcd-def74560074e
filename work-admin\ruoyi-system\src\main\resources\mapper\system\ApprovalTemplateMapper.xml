<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ApprovalTemplateMapper">
    
    <resultMap type="ApprovalTemplate" id="ApprovalTemplateResult">
        <result property="id"    column="id"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateCode"    column="template_code"    />
        <result property="businessType"    column="business_type"    />
        <result property="procurementType"    column="procurement_type"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="urgencyLevels"    column="urgency_levels"    />
        <result property="isDefault"    column="is_default"    />
        <result property="isActive"    column="is_active"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap id="ApprovalTemplateApprovalStepTemplateResult" type="ApprovalTemplate" extends="ApprovalTemplateResult">
        <collection property="approvalStepTemplateList" notNullColumn="sub_id" javaType="java.util.List" resultMap="ApprovalStepTemplateResult" />
    </resultMap>

    <resultMap type="ApprovalStepTemplate" id="ApprovalStepTemplateResult">
        <result property="id"    column="sub_id"    />
        <result property="templateId"    column="sub_template_id"    />
        <result property="stepNo"    column="sub_step_no"    />
        <result property="stepName"    column="sub_step_name"    />
        <result property="stepType"    column="sub_step_type"    />
        <result property="approverType"    column="sub_approver_type"    />
        <result property="approverValue"    column="sub_approver_value"    />
        <result property="approverPolicy"    column="sub_approver_policy"    />
        <result property="sortOrder"    column="sub_sort_order"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="remark"    column="sub_remark"    />
    </resultMap>

    <sql id="selectApprovalTemplateVo">
        select id, template_name, template_code, business_type, procurement_type, min_amount, max_amount, urgency_levels, is_default, is_active, sort_order, description, create_by, create_time, update_by, update_time, remark from approval_template
    </sql>

    <select id="selectApprovalTemplateList" parameterType="ApprovalTemplate" resultMap="ApprovalTemplateResult">
        <include refid="selectApprovalTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="procurementType != null  and procurementType != ''"> and procurement_type = #{procurementType}</if>
            <if test="minAmount != null "> and min_amount = #{minAmount}</if>
            <if test="maxAmount != null "> and max_amount = #{maxAmount}</if>
            <if test="urgencyLevels != null  and urgencyLevels != ''"> and urgency_levels = #{urgencyLevels}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
        </where>
        order by sort_order, create_time desc
    </select>
    
    <select id="selectApprovalTemplateById" parameterType="Long" resultMap="ApprovalTemplateApprovalStepTemplateResult">
        select a.id, a.template_name, a.template_code, a.business_type, a.procurement_type, a.min_amount, a.max_amount, a.urgency_levels, a.is_default, a.is_active, a.sort_order, a.description, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
               b.id as sub_id, b.template_id as sub_template_id, b.step_no as sub_step_no, b.step_name as sub_step_name, b.step_type as sub_step_type, b.approver_type as sub_approver_type, b.approver_value as sub_approver_value, b.approver_policy as sub_approver_policy, b.sort_order as sub_sort_order, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark
        from approval_template a
        left join approval_step_template b on b.template_id = a.id
        where a.id = #{id}
        order by b.step_no
    </select>
        
    <insert id="insertApprovalTemplate" parameterType="ApprovalTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into approval_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="templateCode != null and templateCode != ''">template_code,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="procurementType != null">procurement_type,</if>
            <if test="minAmount != null">min_amount,</if>
            <if test="maxAmount != null">max_amount,</if>
            <if test="urgencyLevels != null">urgency_levels,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="isActive != null">is_active,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="templateCode != null and templateCode != ''">#{templateCode},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="procurementType != null">#{procurementType},</if>
            <if test="minAmount != null">#{minAmount},</if>
            <if test="maxAmount != null">#{maxAmount},</if>
            <if test="urgencyLevels != null">#{urgencyLevels},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateApprovalTemplate" parameterType="ApprovalTemplate">
        update approval_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="templateCode != null and templateCode != ''">template_code = #{templateCode},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="procurementType != null">procurement_type = #{procurementType},</if>
            <if test="minAmount != null">min_amount = #{minAmount},</if>
            <if test="maxAmount != null">max_amount = #{maxAmount},</if>
            <if test="urgencyLevels != null">urgency_levels = #{urgencyLevels},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApprovalTemplateById" parameterType="Long">
        delete from approval_template where id = #{id}
    </delete>

    <delete id="deleteApprovalTemplateByIds" parameterType="String">
        delete from approval_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="clearDefaultTemplate">
        update approval_template set is_default = 0
    </update>

    <update id="setDefaultTemplate" parameterType="Long">
        update approval_template set is_default = 1 where id = #{id}
    </update>

    <update id="changeTemplateStatus">
        update approval_template set is_active = #{status} where id = #{id}
    </update>

    <select id="getTemplateByCondition" resultMap="ApprovalTemplateApprovalStepTemplateResult">
        select a.id, a.template_name, a.template_code, a.business_type, a.procurement_type, a.min_amount, a.max_amount, a.urgency_levels, a.is_default, a.is_active, a.sort_order, a.description, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
               b.id as sub_id, b.template_id as sub_template_id, b.step_no as sub_step_no, b.step_name as sub_step_name, b.step_type as sub_step_type, b.approver_type as sub_approver_type, b.approver_value as sub_approver_value, b.approver_policy as sub_approver_policy, b.sort_order as sub_sort_order, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark
        from approval_template a
        left join approval_step_template b on b.template_id = a.id
        <where>
            a.is_active = 1
            <if test="businessType != null and businessType != ''"> and a.business_type = #{businessType}</if>
            <if test="procurementType != null and procurementType != ''"> and (a.procurement_type = #{procurementType} or a.procurement_type is null)</if>
            <if test="amount != null">
                and (a.min_amount is null or a.min_amount &lt;= #{amount})
                and (a.max_amount is null or a.max_amount &gt;= #{amount})
            </if>
            <if test="urgencyLevel != null">
                and (a.urgency_levels is null or FIND_IN_SET(#{urgencyLevel}, a.urgency_levels) > 0)
            </if>
        </where>
        order by a.is_default desc, a.sort_order, b.step_no
        limit 1
    </select>

</mapper>