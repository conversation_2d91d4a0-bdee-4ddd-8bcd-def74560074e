<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LeaveApplicationMapper">
    
    <resultMap type="LeaveApplication" id="LeaveApplicationResult">
        <result property="id"              column="id"                />
        <result property="appNo"           column="app_no"            />
        <result property="userId"          column="user_id"           />
        <result property="userName"        column="user_name"         />
        <result property="deptName"        column="dept_name"         />
        <result property="leaveTypeId"     column="leave_type_id"     />
        <result property="leaveTypeName"   column="leave_type_name"   />
        <result property="startDate"       column="start_date"        />
        <result property="endDate"         column="end_date"          />
        <result property="leaveDays"       column="leave_days"        />
        <result property="reason"          column="reason"            />
        <result property="proofFile"       column="proof_file"        />
        <result property="status"          column="status"            />
        <result property="createBy"        column="create_by"         />
        <result property="createTime"      column="create_time"       />
        <result property="updateBy"        column="update_by"         />
        <result property="updateTime"      column="update_time"       />
        <result property="remark"          column="remark"            />
    </resultMap>

    <sql id="selectLeaveApplicationVo">
        select 
            la.id, la.app_no, la.user_id, la.leave_type_id, la.start_date, la.end_date, 
            la.leave_days, la.reason, la.proof_file, la.status, la.create_by, la.create_time, 
            la.update_by, la.update_time, la.remark,
            u.nick_name as user_name,
            d.dept_name,
            lt.type_name as leave_type_name
        from leave_application la
            left join sys_user u on la.user_id = u.user_id
            left join sys_dept d on u.dept_id = d.dept_id
            left join leave_type lt on la.leave_type_id = lt.id
    </sql>

    <select id="selectLeaveApplicationList" parameterType="LeaveApplication" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        <where>  
            <if test="appNo != null and appNo != ''"> 
                and la.app_no like concat('%', #{appNo}, '%')
            </if>
            <if test="userId != null"> 
                and la.user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''"> 
                and u.nick_name like concat('%', #{userName}, '%')
            </if>
            <if test="leaveTypeId != null"> 
                and la.leave_type_id = #{leaveTypeId}
            </if>
            <if test="status != null"> 
                and la.status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(la.start_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(la.end_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by la.id desc
    </select>
    
    <select id="selectLeaveApplicationById" parameterType="Long" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        where la.id = #{id}
    </select>

    <select id="selectLeaveApplicationByAppNo" parameterType="String" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        where la.app_no = #{appNo} limit 1
    </select>

    <select id="checkAppNoUnique" parameterType="String" resultMap="LeaveApplicationResult">
        select id, app_no from leave_application where app_no = #{appNo} limit 1
    </select>

    <select id="selectLeaveApplicationByUserId" parameterType="Long" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        where la.user_id = #{userId}
        order by la.create_time desc
    </select>

    <select id="selectPendingLeaveApplicationList" parameterType="LeaveApplication" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        where la.status = 1
        <if test="userId != null"> 
            and la.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''"> 
            and u.nick_name like concat('%', #{userName}, '%')
        </if>
        order by la.create_time desc
    </select>

    <select id="selectLeaveApplicationByUserAndDate" resultMap="LeaveApplicationResult">
        <include refid="selectLeaveApplicationVo"/>
        where la.user_id = #{userId} 
        and la.status in (1, 2)
        and (
            (la.start_date &lt;= #{endDate} and la.end_date &gt;= #{startDate})
        )
        order by la.start_date desc
    </select>

    <insert id="insertLeaveApplication" parameterType="LeaveApplication" useGeneratedKeys="true" keyProperty="id">
        insert into leave_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">app_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="leaveTypeId != null">leave_type_id,</if>
            <if test="leaveTypeName != null and leaveTypeName != ''">leave_type_name,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="leaveDays != null">leave_days,</if>
            <if test="reason != null">reason,</if>
            <if test="proofFile != null">proof_file,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">#{appNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="leaveTypeId != null">#{leaveTypeId},</if>
            <if test="leaveTypeName != null and leaveTypeName != ''">#{leaveTypeName},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="leaveDays != null">#{leaveDays},</if>
            <if test="reason != null">#{reason},</if>
            <if test="proofFile != null">#{proofFile},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateLeaveApplication" parameterType="LeaveApplication">
        update leave_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">app_no = #{appNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="leaveTypeId != null">leave_type_id = #{leaveTypeId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="leaveDays != null">leave_days = #{leaveDays},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="proofFile != null">proof_file = #{proofFile},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveApplicationById" parameterType="Long">
        delete from leave_application where id = #{id}
    </delete>

    <delete id="deleteLeaveApplicationByIds" parameterType="String">
        delete from leave_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
