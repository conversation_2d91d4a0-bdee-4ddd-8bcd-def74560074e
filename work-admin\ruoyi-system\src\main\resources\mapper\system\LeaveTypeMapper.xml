<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LeaveTypeMapper">
    
    <resultMap type="LeaveType" id="LeaveTypeResult">
        <result property="id"          column="id"            />
        <result property="typeName"    column="type_name"     />
        <result property="typeCode"    column="type_code"     />
        <result property="maxDays"     column="max_days"      />
        <result property="isPaid"      column="is_paid"       />
        <result property="needProof"   column="need_proof"    />
        <result property="advanceDays" column="advance_days"  />
        <result property="status"      column="status"        />
        <result property="createBy"    column="create_by"     />
        <result property="createTime"  column="create_time"   />
        <result property="updateBy"    column="update_by"     />
        <result property="updateTime"  column="update_time"   />
        <result property="remark"      column="remark"        />
    </resultMap>

    <sql id="selectLeaveTypeVo">
        select id, type_name, type_code, max_days, is_paid, need_proof, advance_days, status, create_by, create_time, update_by, update_time, remark from leave_type
    </sql>

    <select id="selectLeaveTypeList" parameterType="LeaveType" resultMap="LeaveTypeResult">
        <include refid="selectLeaveTypeVo"/>
        <where>  
            <if test="typeName != null and typeName != ''"> 
                and type_name like concat('%', #{typeName}, '%')
            </if>
            <if test="typeCode != null and typeCode != ''"> 
                and type_code = #{typeCode}
            </if>
            <if test="status != null"> 
                and status = #{status}
            </if>
        </where>
        order by id desc
    </select>
    
    <select id="selectLeaveTypeById" parameterType="Long" resultMap="LeaveTypeResult">
        <include refid="selectLeaveTypeVo"/>
        where id = #{id}
    </select>

    <select id="selectLeaveTypeAll" resultMap="LeaveTypeResult">
        <include refid="selectLeaveTypeVo"/>
        where status = 1
        order by id desc
    </select>

    <select id="selectLeaveTypeByCode" parameterType="String" resultMap="LeaveTypeResult">
        <include refid="selectLeaveTypeVo"/>
        where type_code = #{typeCode} and status = 1 limit 1
    </select>

    <select id="checkTypeCodeUnique" parameterType="String" resultMap="LeaveTypeResult">
        <include refid="selectLeaveTypeVo"/>
        where type_code = #{typeCode} limit 1
    </select>

    <insert id="insertLeaveType" parameterType="LeaveType" useGeneratedKeys="true" keyProperty="id">
        insert into leave_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="maxDays != null">max_days,</if>
            <if test="isPaid != null">is_paid,</if>
            <if test="needProof != null">need_proof,</if>
            <if test="advanceDays != null">advance_days,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="maxDays != null">#{maxDays},</if>
            <if test="isPaid != null">#{isPaid},</if>
            <if test="needProof != null">#{needProof},</if>
            <if test="advanceDays != null">#{advanceDays},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateLeaveType" parameterType="LeaveType">
        update leave_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="maxDays != null">max_days = #{maxDays},</if>
            <if test="isPaid != null">is_paid = #{isPaid},</if>
            <if test="needProof != null">need_proof = #{needProof},</if>
            <if test="advanceDays != null">advance_days = #{advanceDays},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveTypeById" parameterType="Long">
        delete from leave_type where id = #{id}
    </delete>

    <delete id="deleteLeaveTypeByIds" parameterType="String">
        delete from leave_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 