<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysAttendanceMapper">
    
    <resultMap type="SysAttendance" id="SysAttendanceResult">
        <result property="attendanceId"    column="attendance_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="attendanceDate"    column="attendance_date"    />
        <result property="checkInTime"    column="check_in_time"    />
        <result property="checkOutTime"    column="check_out_time"    />
        <result property="workHours"    column="work_hours"    />
        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="lateMinutes"    column="late_minutes"    />
        <result property="earlyMinutes"    column="early_minutes"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysAttendanceVo">
        select a.attendance_id, a.user_id, u.user_name, u.nick_name, d.dept_name, a.attendance_date, a.check_in_time, a.check_out_time, a.work_hours, a.attendance_status, a.late_minutes, a.early_minutes, a.remark, a.create_by, a.create_time, a.update_by, a.update_time 
        from sys_attendance a
        left join sys_user u on u.user_id = a.user_id
        left join sys_dept d on d.dept_id = u.dept_id
    </sql>

    <select id="selectSysAttendanceList" parameterType="SysAttendance" resultMap="SysAttendanceResult">
        <include refid="selectSysAttendanceVo"/>
        <where>  
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and u.user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and u.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="attendanceDate != null "> and date_format(a.attendance_date,'%y%m%d') = date_format(#{attendanceDate},'%y%m%d')</if>
            <if test="attendanceStatus != null  and attendanceStatus != ''"> and a.attendance_status = #{attendanceStatus}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(a.attendance_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(a.attendance_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by a.attendance_date desc, a.attendance_id desc
    </select>
    
    <select id="selectSysAttendanceByAttendanceId" parameterType="Long" resultMap="SysAttendanceResult">
        <include refid="selectSysAttendanceVo"/>
        where a.attendance_id = #{attendanceId}
    </select>

    <select id="selectTodayAttendanceByUserId" resultMap="SysAttendanceResult">
        <include refid="selectSysAttendanceVo"/>
        where a.user_id = #{userId} and date_format(a.attendance_date,'%Y-%m-%d') = #{attendanceDate}
    </select>
        
    <insert id="insertSysAttendance" parameterType="SysAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into sys_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="attendanceDate != null">attendance_date,</if>
            <if test="checkInTime != null">check_in_time,</if>
            <if test="checkOutTime != null">check_out_time,</if>
            <if test="workHours != null">work_hours,</if>
            <if test="attendanceStatus != null">attendance_status,</if>
            <if test="lateMinutes != null">late_minutes,</if>
            <if test="earlyMinutes != null">early_minutes,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="attendanceDate != null">#{attendanceDate},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="checkOutTime != null">#{checkOutTime},</if>
            <if test="workHours != null">#{workHours},</if>
            <if test="attendanceStatus != null">#{attendanceStatus},</if>
            <if test="lateMinutes != null">#{lateMinutes},</if>
            <if test="earlyMinutes != null">#{earlyMinutes},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysAttendance" parameterType="SysAttendance">
        update sys_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="attendanceDate != null">attendance_date = #{attendanceDate},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="checkOutTime != null">check_out_time = #{checkOutTime},</if>
            <if test="workHours != null">work_hours = #{workHours},</if>
            <if test="attendanceStatus != null">attendance_status = #{attendanceStatus},</if>
            <if test="lateMinutes != null">late_minutes = #{lateMinutes},</if>
            <if test="earlyMinutes != null">early_minutes = #{earlyMinutes},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where attendance_id = #{attendanceId}
    </update>

    <delete id="deleteSysAttendanceByAttendanceId" parameterType="Long">
        delete from sys_attendance where attendance_id = #{attendanceId}
    </delete>

    <delete id="deleteSysAttendanceByAttendanceIds" parameterType="String">
        delete from sys_attendance where attendance_id in 
        <foreach item="attendanceId" collection="array" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
    </delete>

    <insert id="checkIn" parameterType="SysAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into sys_attendance (user_id, attendance_date, check_in_time, attendance_status, late_minutes, create_by, create_time)
        values (#{userId}, #{attendanceDate}, #{checkInTime}, #{attendanceStatus}, #{lateMinutes}, #{createBy}, #{createTime})
        ON DUPLICATE KEY UPDATE 
        check_in_time = #{checkInTime}, 
        attendance_status = #{attendanceStatus}, 
        late_minutes = #{lateMinutes},
        update_by = #{updateBy},
        update_time = #{updateTime}
    </insert>

    <update id="checkOut" parameterType="SysAttendance">
        update sys_attendance 
        set check_out_time = #{checkOutTime}, 
            work_hours = #{workHours}, 
            attendance_status = #{attendanceStatus}, 
            early_minutes = #{earlyMinutes},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where user_id = #{userId} and date_format(attendance_date,'%Y-%m-%d') = date_format(#{attendanceDate},'%Y-%m-%d')
    </update>
</mapper> 