<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.InvoiceRecognitionMapper">
    
    <resultMap type="InvoiceRecognition" id="InvoiceRecognitionResult">
        <result property="id"    column="id"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="invoiceCode"    column="invoice_code"    />
        <result property="invoiceNumber"    column="invoice_number"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="checkCode"    column="check_code"    />
        <result property="machineNumber"    column="machine_number"    />
        <result property="taxControlCode"    column="tax_control_code"    />
        <result property="buyerName"    column="buyer_name"    />
        <result property="buyerTaxId"    column="buyer_tax_id"    />
        <result property="buyerAddrTel"    column="buyer_addr_tel"    />
        <result property="buyerFinancialAccount"    column="buyer_financial_account"    />
        <result property="sellerName"    column="seller_name"    />
        <result property="sellerTaxId"    column="seller_tax_id"    />
        <result property="sellerAddrTel"    column="seller_addr_tel"    />
        <result property="sellerFinancialAccount"    column="seller_financial_account"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="taxAmount"    column="tax_amount"    />
        <result property="amountWithoutTax"    column="amount_without_tax"    />
        <result property="invoiceClerk"    column="invoice_clerk"    />
        <result property="payee"    column="payee"    />
        <result property="checker"    column="checker"    />
        <result property="note"    column="note"    />
        <result property="invoiceTitle"    column="invoice_title"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="status"    column="status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="goodsInfoJson"    column="goods_info_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectInvoiceRecognitionVo">
        select id, invoice_type, invoice_code, invoice_number, issue_date, check_code, machine_number, tax_control_code, buyer_name, buyer_tax_id, buyer_addr_tel, buyer_financial_account, seller_name, seller_tax_id, seller_addr_tel, seller_financial_account, total_amount, tax_amount, amount_without_tax, invoice_clerk, payee, checker, note, invoice_title, original_file_name, file_path, status, error_message, goods_info_json, create_by, create_time, update_by, update_time, remark from invoice_recognition
    </sql>

    <select id="selectInvoiceRecognitionList" parameterType="InvoiceRecognition" resultMap="InvoiceRecognitionResult">
        <include refid="selectInvoiceRecognitionVo"/>
        <where>  
            <if test="invoiceType != null  and invoiceType != ''"> and invoice_type = #{invoiceType}</if>
            <if test="invoiceCode != null  and invoiceCode != ''"> and invoice_code = #{invoiceCode}</if>
            <if test="invoiceNumber != null  and invoiceNumber != ''"> and invoice_number like concat('%', #{invoiceNumber}, '%')</if>
            <if test="issueDate != null "> and issue_date = #{issueDate}</if>
            <if test="checkCode != null  and checkCode != ''"> and check_code = #{checkCode}</if>
            <if test="buyerName != null  and buyerName != ''"> and buyer_name like concat('%', #{buyerName}, '%')</if>
            <if test="buyerTaxId != null  and buyerTaxId != ''"> and buyer_tax_id = #{buyerTaxId}</if>
            <if test="sellerName != null  and sellerName != ''"> and seller_name like concat('%', #{sellerName}, '%')</if>
            <if test="sellerTaxId != null  and sellerTaxId != ''"> and seller_tax_id = #{sellerTaxId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="originalFileName != null  and originalFileName != ''"> and original_file_name like concat('%', #{originalFileName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectInvoiceRecognitionById" parameterType="Long" resultMap="InvoiceRecognitionResult">
        <include refid="selectInvoiceRecognitionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertInvoiceRecognition" parameterType="InvoiceRecognition" useGeneratedKeys="true" keyProperty="id">
        insert into invoice_recognition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceType != null">invoice_type,</if>
            <if test="invoiceCode != null">invoice_code,</if>
            <if test="invoiceNumber != null">invoice_number,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="checkCode != null">check_code,</if>
            <if test="machineNumber != null">machine_number,</if>
            <if test="taxControlCode != null">tax_control_code,</if>
            <if test="buyerName != null">buyer_name,</if>
            <if test="buyerTaxId != null">buyer_tax_id,</if>
            <if test="buyerAddrTel != null">buyer_addr_tel,</if>
            <if test="buyerFinancialAccount != null">buyer_financial_account,</if>
            <if test="sellerName != null">seller_name,</if>
            <if test="sellerTaxId != null">seller_tax_id,</if>
            <if test="sellerAddrTel != null">seller_addr_tel,</if>
            <if test="sellerFinancialAccount != null">seller_financial_account,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="taxAmount != null">tax_amount,</if>
            <if test="amountWithoutTax != null">amount_without_tax,</if>
            <if test="invoiceClerk != null">invoice_clerk,</if>
            <if test="payee != null">payee,</if>
            <if test="checker != null">checker,</if>
            <if test="note != null">note,</if>
            <if test="invoiceTitle != null">invoice_title,</if>
            <if test="originalFileName != null">original_file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="status != null">status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="goodsInfoJson != null">goods_info_json,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceCode != null">#{invoiceCode},</if>
            <if test="invoiceNumber != null">#{invoiceNumber},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="checkCode != null">#{checkCode},</if>
            <if test="machineNumber != null">#{machineNumber},</if>
            <if test="taxControlCode != null">#{taxControlCode},</if>
            <if test="buyerName != null">#{buyerName},</if>
            <if test="buyerTaxId != null">#{buyerTaxId},</if>
            <if test="buyerAddrTel != null">#{buyerAddrTel},</if>
            <if test="buyerFinancialAccount != null">#{buyerFinancialAccount},</if>
            <if test="sellerName != null">#{sellerName},</if>
            <if test="sellerTaxId != null">#{sellerTaxId},</if>
            <if test="sellerAddrTel != null">#{sellerAddrTel},</if>
            <if test="sellerFinancialAccount != null">#{sellerFinancialAccount},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="taxAmount != null">#{taxAmount},</if>
            <if test="amountWithoutTax != null">#{amountWithoutTax},</if>
            <if test="invoiceClerk != null">#{invoiceClerk},</if>
            <if test="payee != null">#{payee},</if>
            <if test="checker != null">#{checker},</if>
            <if test="note != null">#{note},</if>
            <if test="invoiceTitle != null">#{invoiceTitle},</if>
            <if test="originalFileName != null">#{originalFileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="status != null">#{status},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="goodsInfoJson != null">#{goodsInfoJson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateInvoiceRecognition" parameterType="InvoiceRecognition">
        update invoice_recognition
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="invoiceCode != null">invoice_code = #{invoiceCode},</if>
            <if test="invoiceNumber != null">invoice_number = #{invoiceNumber},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="checkCode != null">check_code = #{checkCode},</if>
            <if test="machineNumber != null">machine_number = #{machineNumber},</if>
            <if test="taxControlCode != null">tax_control_code = #{taxControlCode},</if>
            <if test="buyerName != null">buyer_name = #{buyerName},</if>
            <if test="buyerTaxId != null">buyer_tax_id = #{buyerTaxId},</if>
            <if test="buyerAddrTel != null">buyer_addr_tel = #{buyerAddrTel},</if>
            <if test="buyerFinancialAccount != null">buyer_financial_account = #{buyerFinancialAccount},</if>
            <if test="sellerName != null">seller_name = #{sellerName},</if>
            <if test="sellerTaxId != null">seller_tax_id = #{sellerTaxId},</if>
            <if test="sellerAddrTel != null">seller_addr_tel = #{sellerAddrTel},</if>
            <if test="sellerFinancialAccount != null">seller_financial_account = #{sellerFinancialAccount},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="taxAmount != null">tax_amount = #{taxAmount},</if>
            <if test="amountWithoutTax != null">amount_without_tax = #{amountWithoutTax},</if>
            <if test="invoiceClerk != null">invoice_clerk = #{invoiceClerk},</if>
            <if test="payee != null">payee = #{payee},</if>
            <if test="checker != null">checker = #{checker},</if>
            <if test="note != null">note = #{note},</if>
            <if test="invoiceTitle != null">invoice_title = #{invoiceTitle},</if>
            <if test="originalFileName != null">original_file_name = #{originalFileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="goodsInfoJson != null">goods_info_json = #{goodsInfoJson},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInvoiceRecognitionById" parameterType="Long">
        delete from invoice_recognition where id = #{id}
    </delete>

    <delete id="deleteInvoiceRecognitionByIds" parameterType="String">
        delete from invoice_recognition where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
