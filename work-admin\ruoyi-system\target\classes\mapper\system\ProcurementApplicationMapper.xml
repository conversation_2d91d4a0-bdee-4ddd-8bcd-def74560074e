<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProcurementApplicationMapper">
    
    <resultMap type="ProcurementApplication" id="ProcurementApplicationResult">
        <result property="id"    column="id"    />
        <result property="appNo"    column="app_no"    />
        <result property="title"    column="title"    />
        <result property="applicantId"    column="applicant_id"    />
        <result property="applicantName"    column="applicant_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="procurementType"    column="procurement_type"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="urgencyLevel"    column="urgency_level"    />
        <result property="expectedDate"    column="expected_date"    />
        <result property="reason"    column="reason"    />
        <result property="attachmentUrls"    column="attachment_urls"    />
        <result property="status"    column="status"    />
        <result property="currentStep"    column="current_step"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap id="ProcurementApplicationProcurementItemResult" type="ProcurementApplication" extends="ProcurementApplicationResult">
        <collection property="procurementItemList" notNullColumn="sub_id" javaType="java.util.List" resultMap="ProcurementItemResult" />
    </resultMap>

    <resultMap type="ProcurementItem" id="ProcurementItemResult">
        <result property="id"    column="sub_id"    />
        <result property="appId"    column="sub_app_id"    />
        <result property="itemCategory"    column="sub_item_category"    />
        <result property="itemName"    column="sub_item_name"    />
        <result property="itemCode"    column="sub_item_code"    />
        <result property="brand"    column="sub_brand"    />
        <result property="model"    column="sub_model"    />
        <result property="specification"    column="sub_specification"    />
        <result property="unit"    column="sub_unit"    />
        <result property="quantity"    column="sub_quantity"    />
        <result property="unitPrice"    column="sub_unit_price"    />
        <result property="totalPrice"    column="sub_total_price"    />
        <result property="actualUnitPrice"    column="sub_actual_unit_price"    />
        <result property="actualTotalPrice"    column="sub_actual_total_price"    />
        <result property="supplierName"    column="sub_supplier_name"    />
        <result property="purchaseUrl"    column="sub_purchase_url"    />
        <result property="screenshotUrl"    column="sub_screenshot_url"    />
        <result property="remark"    column="sub_remark"    />
        <result property="status"    column="sub_status"    />
        <result property="purchaseDate"    column="sub_purchase_date"    />
        <result property="receiptDate"    column="sub_receipt_date"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="delFlag"    column="sub_del_flag"    />
    </resultMap>

    <sql id="selectProcurementApplicationVo">
        select id, app_no, title, applicant_id, applicant_name, dept_id, dept_name, procurement_type, total_amount, urgency_level, expected_date, reason, attachment_urls, status, current_step, process_instance_id, create_time, update_time, create_by, update_by, del_flag, remark from procurement_application
    </sql>

    <select id="selectProcurementApplicationList" parameterType="ProcurementApplication" resultMap="ProcurementApplicationResult">
        <include refid="selectProcurementApplicationVo"/>
        <where>  
            <if test="appNo != null  and appNo != ''"> and app_no like concat('%', #{appNo}, '%')</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="applicantId != null "> and applicant_id = #{applicantId}</if>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="procurementType != null  and procurementType != ''"> and procurement_type = #{procurementType}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="urgencyLevel != null "> and urgency_level = #{urgencyLevel}</if>
            <if test="expectedDate != null "> and expected_date = #{expectedDate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="currentStep != null "> and current_step = #{currentStep}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProcurementApplicationById" parameterType="Long" resultMap="ProcurementApplicationProcurementItemResult">
        select a.id, a.app_no, a.title, a.applicant_id, a.applicant_name, a.dept_id, a.dept_name, a.procurement_type, a.total_amount, a.urgency_level, a.expected_date, a.reason, a.attachment_urls, a.status, a.current_step, a.process_instance_id, a.create_time, a.update_time, a.create_by, a.update_by, a.del_flag, a.remark,
 b.id as sub_id, b.app_id as sub_app_id, b.item_category as sub_item_category, b.item_name as sub_item_name, b.item_code as sub_item_code, b.brand as sub_brand, b.model as sub_model, b.specification as sub_specification, b.unit as sub_unit, b.quantity as sub_quantity, b.unit_price as sub_unit_price, b.total_price as sub_total_price, b.actual_unit_price as sub_actual_unit_price, b.actual_total_price as sub_actual_total_price, b.supplier_name as sub_supplier_name, b.purchase_url as sub_purchase_url, b.screenshot_url as sub_screenshot_url, b.remark as sub_remark, b.status as sub_status, b.purchase_date as sub_purchase_date, b.receipt_date as sub_receipt_date, b.create_time as sub_create_time, b.update_time as sub_update_time, b.create_by as sub_create_by, b.update_by as sub_update_by, b.del_flag as sub_del_flag
        from procurement_application a
        left join procurement_item b on b.app_id = a.id and b.del_flag = '0'
        where a.id = #{id} and a.del_flag = '0'
    </select>

    <select id="selectProcurementApplicationByAppNo" parameterType="String" resultMap="ProcurementApplicationResult">
        <include refid="selectProcurementApplicationVo"/>
        where app_no = #{appNo} and del_flag = '0'
    </select>
        
    <insert id="insertProcurementApplication" parameterType="ProcurementApplication" useGeneratedKeys="true" keyProperty="id">
        insert into procurement_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">app_no,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="applicantId != null">applicant_id,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="procurementType != null and procurementType != ''">procurement_type,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="urgencyLevel != null">urgency_level,</if>
            <if test="expectedDate != null">expected_date,</if>
            <if test="reason != null">reason,</if>
            <if test="attachmentUrls != null">attachment_urls,</if>
            <if test="status != null">status,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="processInstanceId != null">process_instance_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">#{appNo},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="applicantId != null">#{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="procurementType != null and procurementType != ''">#{procurementType},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="urgencyLevel != null">#{urgencyLevel},</if>
            <if test="expectedDate != null">#{expectedDate},</if>
            <if test="reason != null">#{reason},</if>
            <if test="attachmentUrls != null">#{attachmentUrls},</if>
            <if test="status != null">#{status},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="processInstanceId != null">#{processInstanceId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProcurementApplication" parameterType="ProcurementApplication">
        update procurement_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="appNo != null and appNo != ''">app_no = #{appNo},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="applicantId != null">applicant_id = #{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="procurementType != null and procurementType != ''">procurement_type = #{procurementType},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
            <if test="expectedDate != null">expected_date = #{expectedDate},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="attachmentUrls != null">attachment_urls = #{attachmentUrls},</if>
            <if test="status != null">status = #{status},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="processInstanceId != null">process_instance_id = #{processInstanceId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteProcurementApplicationById" parameterType="Long">
        update procurement_application set del_flag = '2' where id = #{id}
    </update>

    <update id="deleteProcurementApplicationByIds" parameterType="String">
        update procurement_application set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteProcurementItemByAppIds" parameterType="String">
        update procurement_item set del_flag = '2' where app_id in 
        <foreach item="appId" collection="array" open="(" separator="," close=")">
            #{appId}
        </foreach>
    </update>

    <update id="deleteProcurementItemByAppId" parameterType="Long">
        update procurement_item set del_flag = '2' where app_id = #{appId}
    </update>

    <insert id="batchProcurementItem">
        insert into procurement_item( id, app_id, item_category, item_name, item_code, brand, model, specification, unit, quantity, unit_price, total_price, actual_unit_price, actual_total_price, supplier_name, purchase_url, screenshot_url, remark, status, purchase_date, receipt_date, create_time, update_time, create_by, update_by, del_flag) values
		<foreach item="item" collection="list" separator=",">
            ( #{item.id}, #{item.appId}, #{item.itemCategory}, #{item.itemName}, #{item.itemCode}, #{item.brand}, #{item.model}, #{item.specification}, #{item.unit}, #{item.quantity}, #{item.unitPrice}, #{item.totalPrice}, #{item.actualUnitPrice}, #{item.actualTotalPrice}, #{item.supplierName}, #{item.purchaseUrl}, #{item.screenshotUrl}, #{item.remark}, #{item.status}, #{item.purchaseDate}, #{item.receiptDate}, #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}, #{item.delFlag})
        </foreach>
    </insert>

    <select id="generateAppNo" resultType="String">
        SELECT CONCAT('CG', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(IFNULL(MAX(CAST(SUBSTRING(app_no, 11) AS UNSIGNED)), 0) + 1, 4, '0'))
        FROM procurement_application 
        WHERE app_no LIKE CONCAT('CG', DATE_FORMAT(NOW(), '%Y%m%d'), '%')
        AND del_flag = '0'
    </select>
</mapper>