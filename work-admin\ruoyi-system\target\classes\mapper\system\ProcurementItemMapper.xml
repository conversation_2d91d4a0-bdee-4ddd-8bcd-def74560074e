<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProcurementItemMapper">
    
    <resultMap type="ProcurementItem" id="ProcurementItemResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="itemCategory"    column="item_category"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="brand"    column="brand"    />
        <result property="model"    column="model"    />
        <result property="specification"    column="specification"    />
        <result property="unit"    column="unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="actualUnitPrice"    column="actual_unit_price"    />
        <result property="actualTotalPrice"    column="actual_total_price"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="purchaseUrl"    column="purchase_url"    />
        <result property="screenshotUrl"    column="screenshot_url"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="purchaseDate"    column="purchase_date"    />
        <result property="receiptDate"    column="receipt_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProcurementItemVo">
        select id, app_id, item_category, item_name, item_code, brand, model, specification, unit, quantity, unit_price, total_price, actual_unit_price, actual_total_price, supplier_name, purchase_url, screenshot_url, remark, status, purchase_date, receipt_date, create_time, update_time, create_by, update_by, del_flag from procurement_item
    </sql>

    <select id="selectProcurementItemList" parameterType="ProcurementItem" resultMap="ProcurementItemResult">
        <include refid="selectProcurementItemVo"/>
        <where>  
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="itemCategory != null  and itemCategory != ''"> and item_category = #{itemCategory}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="model != null  and model != ''"> and model like concat('%', #{model}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="totalPrice != null "> and total_price = #{totalPrice}</if>
            <if test="actualUnitPrice != null "> and actual_unit_price = #{actualUnitPrice}</if>
            <if test="actualTotalPrice != null "> and actual_total_price = #{actualTotalPrice}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="purchaseDate != null "> and purchase_date = #{purchaseDate}</if>
            <if test="receiptDate != null "> and receipt_date = #{receiptDate}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProcurementItemById" parameterType="Long" resultMap="ProcurementItemResult">
        <include refid="selectProcurementItemVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectProcurementItemByAppId" parameterType="Long" resultMap="ProcurementItemResult">
        <include refid="selectProcurementItemVo"/>
        where app_id = #{appId} and del_flag = '0'
        order by create_time desc
    </select>
        
    <insert id="insertProcurementItem" parameterType="ProcurementItem" useGeneratedKeys="true" keyProperty="id">
        insert into procurement_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="itemCategory != null">item_category,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="brand != null">brand,</if>
            <if test="model != null">model,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="actualUnitPrice != null">actual_unit_price,</if>
            <if test="actualTotalPrice != null">actual_total_price,</if>
            <if test="supplierName != null">supplier_name,</if>
            <if test="purchaseUrl != null">purchase_url,</if>
            <if test="screenshotUrl != null">screenshot_url,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="receiptDate != null">receipt_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="itemCategory != null">#{itemCategory},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="brand != null">#{brand},</if>
            <if test="model != null">#{model},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="actualUnitPrice != null">#{actualUnitPrice},</if>
            <if test="actualTotalPrice != null">#{actualTotalPrice},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="purchaseUrl != null">#{purchaseUrl},</if>
            <if test="screenshotUrl != null">#{screenshotUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="receiptDate != null">#{receiptDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateProcurementItem" parameterType="ProcurementItem">
        update procurement_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="itemCategory != null">item_category = #{itemCategory},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="model != null">model = #{model},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="actualUnitPrice != null">actual_unit_price = #{actualUnitPrice},</if>
            <if test="actualTotalPrice != null">actual_total_price = #{actualTotalPrice},</if>
            <if test="supplierName != null">supplier_name = #{supplierName},</if>
            <if test="purchaseUrl != null">purchase_url = #{purchaseUrl},</if>
            <if test="screenshotUrl != null">screenshot_url = #{screenshotUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="receiptDate != null">receipt_date = #{receiptDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteProcurementItemById" parameterType="Long">
        update procurement_item set del_flag = '2' where id = #{id}
    </update>

    <update id="deleteProcurementItemByIds" parameterType="String">
        update procurement_item set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>