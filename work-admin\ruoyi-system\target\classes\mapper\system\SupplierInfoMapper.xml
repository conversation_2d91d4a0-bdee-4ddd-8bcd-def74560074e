<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SupplierInfoMapper">
    
    <resultMap type="SupplierInfo" id="SupplierInfoResult">
        <result property="id"                    column="id"                    />
        <result property="supplierCode"          column="supplier_code"         />
        <result property="supplierName"          column="supplier_name"         />
        <result property="supplierShortName"     column="supplier_short_name"   />
        <result property="supplierType"          column="supplier_type"         />
        <result property="supplierLevel"         column="supplier_level"        />
        <result property="mainBusiness"          column="main_business"          />
        <result property="contactPerson"         column="contact_person"        />
        <result property="contactPhone"          column="contact_phone"         />
        <result property="mobilePhone"           column="mobile_phone"          />
        <result property="contactEmail"          column="contact_email"         />
        <result property="faxNumber"             column="fax_number"            />
        <result property="address"               column="address"               />
        <result property="province"              column="province"              />
        <result property="city"                  column="city"                  />
        <result property="district"              column="district"              />
        <result property="postalCode"            column="postal_code"           />
        <result property="businessLicense"       column="business_license"      />
        <result property="taxNumber"             column="tax_number"            />
        <result property="bankAccount"           column="bank_account"          />
        <result property="bankName"              column="bank_name"             />
        <result property="creditRating"          column="credit_rating"         />
        <result property="registeredCapital"     column="registered_capital"    />
        <result property="establishDate"         column="establish_date"        />
        <result property="cooperationStartDate"  column="cooperation_start_date" />
        <result property="cooperationYears"      column="cooperation_years"     />
        <result property="totalAmount"           column="total_amount"          />
        <result property="yearlyAmount"          column="yearly_amount"         />
        <result property="paymentMethod"         column="payment_method"        />
        <result property="paymentCycle"          column="payment_cycle"         />
        <result property="invoiceType"           column="invoice_type"          />
        <result property="qualityCertification"  column="quality_certification" />
        <result property="environmentCertification" column="environment_certification" />
        <result property="status"                column="status"                />
        <result property="createTime"            column="create_time"           />
        <result property="updateTime"            column="update_time"           />
        <result property="createBy"              column="create_by"             />
        <result property="updateBy"              column="update_by"             />
        <result property="remark"                column="remark"                />
    </resultMap>

    <sql id="selectSupplierInfoVo">
        select id, supplier_code, supplier_name, supplier_short_name, supplier_type, supplier_level, main_business, 
               contact_person, contact_phone, mobile_phone, contact_email, fax_number, 
               address, province, city, district, postal_code, 
               business_license, tax_number, bank_account, bank_name, credit_rating, 
               registered_capital, establish_date, cooperation_start_date, cooperation_years, 
               total_amount, yearly_amount, payment_method, payment_cycle, invoice_type, 
               quality_certification, environment_certification, status, 
               create_time, update_time, create_by, update_by, remark 
        from sys_supplier
    </sql>

    <select id="selectSupplierInfoList" parameterType="SupplierInfo" resultMap="SupplierInfoResult">
        <include refid="selectSupplierInfoVo"/>
        <where>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code like concat('%', #{supplierCode}, '%')</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="supplierShortName != null  and supplierShortName != ''"> and supplier_short_name like concat('%', #{supplierShortName}, '%')</if>
            <if test="supplierType != null  and supplierType != ''"> and supplier_type = #{supplierType}</if>
            <if test="supplierLevel != null  and supplierLevel != ''"> and supplier_level = #{supplierLevel}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone like concat('%', #{mobilePhone}, '%')</if>
            <if test="contactEmail != null  and contactEmail != ''"> and contact_email like concat('%', #{contactEmail}, '%')</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="creditRating != null  and creditRating != ''"> and credit_rating = #{creditRating}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSupplierInfoById" parameterType="Long" resultMap="SupplierInfoResult">
        <include refid="selectSupplierInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupplierInfo" parameterType="SupplierInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sys_supplier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code,</if>
            <if test="supplierName != null and supplierName != ''">supplier_name,</if>
            <if test="supplierShortName != null">supplier_short_name,</if>
            <if test="supplierType != null and supplierType != ''">supplier_type,</if>
            <if test="supplierLevel != null">supplier_level,</if>
            <if test="mainBusiness != null">main_business,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="faxNumber != null">fax_number,</if>
            <if test="address != null">address,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="postalCode != null">postal_code,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="establishDate != null">establish_date,</if>
            <if test="cooperationStartDate != null">cooperation_start_date,</if>
            <if test="cooperationYears != null">cooperation_years,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="yearlyAmount != null">yearly_amount,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="paymentCycle != null">payment_cycle,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="qualityCertification != null">quality_certification,</if>
            <if test="environmentCertification != null">environment_certification,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">#{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">#{supplierName},</if>
            <if test="supplierShortName != null">#{supplierShortName},</if>
            <if test="supplierType != null and supplierType != ''">#{supplierType},</if>
            <if test="supplierLevel != null">#{supplierLevel},</if>
            <if test="mainBusiness != null">#{mainBusiness},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="faxNumber != null">#{faxNumber},</if>
            <if test="address != null">#{address},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="postalCode != null">#{postalCode},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="establishDate != null">#{establishDate},</if>
            <if test="cooperationStartDate != null">#{cooperationStartDate},</if>
            <if test="cooperationYears != null">#{cooperationYears},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="yearlyAmount != null">#{yearlyAmount},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="paymentCycle != null">#{paymentCycle},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="qualityCertification != null">#{qualityCertification},</if>
            <if test="environmentCertification != null">#{environmentCertification},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSupplierInfo" parameterType="SupplierInfo">
        update sys_supplier
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="supplierShortName != null">supplier_short_name = #{supplierShortName},</if>
            <if test="supplierType != null and supplierType != ''">supplier_type = #{supplierType},</if>
            <if test="supplierLevel != null">supplier_level = #{supplierLevel},</if>
            <if test="mainBusiness != null">main_business = #{mainBusiness},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="faxNumber != null">fax_number = #{faxNumber},</if>
            <if test="address != null">address = #{address},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="postalCode != null">postal_code = #{postalCode},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="establishDate != null">establish_date = #{establishDate},</if>
            <if test="cooperationStartDate != null">cooperation_start_date = #{cooperationStartDate},</if>
            <if test="cooperationYears != null">cooperation_years = #{cooperationYears},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="yearlyAmount != null">yearly_amount = #{yearlyAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentCycle != null">payment_cycle = #{paymentCycle},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="qualityCertification != null">quality_certification = #{qualityCertification},</if>
            <if test="environmentCertification != null">environment_certification = #{environmentCertification},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplierInfoById" parameterType="Long">
        delete from sys_supplier where id = #{id}
    </delete>

    <delete id="deleteSupplierInfoByIds" parameterType="String">
        delete from sys_supplier where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkSupplierCodeUnique" parameterType="String" resultMap="SupplierInfoResult">
        <include refid="selectSupplierInfoVo"/>
        where supplier_code = #{supplierCode} limit 1
    </select>

    <select id="generateSupplierCode" resultType="String">
        select concat('SUP', lpad(ifnull(max(cast(substring(supplier_code, 4) as unsigned)), 0) + 1, 6, '0'))
        from sys_supplier
        where supplier_code regexp '^SUP[0-9]{6}$'
    </select>

    <select id="selectSupplierOptions" resultMap="SupplierInfoResult">
        select id, supplier_code, supplier_name, supplier_type, status
        from sys_supplier
        where status = 1
        order by supplier_name
    </select>

</mapper>
