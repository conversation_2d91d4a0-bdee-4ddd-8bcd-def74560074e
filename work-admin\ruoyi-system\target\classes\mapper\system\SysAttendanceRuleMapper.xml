<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysAttendanceRuleMapper">
    
    <resultMap type="SysAttendanceRule" id="SysAttendanceRuleResult">
        <result property="ruleId"          column="rule_id"          />
        <result property="ruleName"        column="rule_name"        />
        <result property="deptId"          column="dept_id"          />
        <result property="deptName"        column="dept_name"        />
        <result property="workStartTime"   column="work_start_time"  />
        <result property="workEndTime"     column="work_end_time"    />
        <result property="lateThreshold"   column="late_threshold"   />
        <result property="earlyThreshold"  column="early_threshold"  />
        <result property="workDays"        column="work_days"        />
        <result property="status"          column="status"           />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
    </resultMap>

    <sql id="selectSysAttendanceRuleVo">
        select r.rule_id, r.rule_name, r.dept_id, d.dept_name, r.work_start_time, r.work_end_time, 
               r.late_threshold, r.early_threshold, r.work_days, r.status, r.create_by, r.create_time, 
               r.update_by, r.update_time
        from sys_attendance_rule r
        left join sys_dept d on d.dept_id = r.dept_id
    </sql>

    <select id="selectSysAttendanceRuleList" parameterType="SysAttendanceRule" resultMap="SysAttendanceRuleResult">
        <include refid="selectSysAttendanceRuleVo"/>
        <where>  
            <if test="ruleName != null and ruleName != ''"> and r.rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="deptId != null"> and r.dept_id = #{deptId}</if>
            <if test="status != null and status != ''"> and r.status = #{status}</if>
        </where>
        order by r.rule_id desc
    </select>
    
    <select id="selectSysAttendanceRuleByRuleId" parameterType="Long" resultMap="SysAttendanceRuleResult">
        <include refid="selectSysAttendanceRuleVo"/>
        where r.rule_id = #{ruleId}
    </select>
        
    <insert id="insertSysAttendanceRule" parameterType="SysAttendanceRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into sys_attendance_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="workStartTime != null">work_start_time,</if>
            <if test="workEndTime != null">work_end_time,</if>
            <if test="lateThreshold != null">late_threshold,</if>
            <if test="earlyThreshold != null">early_threshold,</if>
            <if test="workDays != null and workDays != ''">work_days,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="workStartTime != null">#{workStartTime},</if>
            <if test="workEndTime != null">#{workEndTime},</if>
            <if test="lateThreshold != null">#{lateThreshold},</if>
            <if test="earlyThreshold != null">#{earlyThreshold},</if>
            <if test="workDays != null and workDays != ''">#{workDays},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysAttendanceRule" parameterType="SysAttendanceRule">
        update sys_attendance_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="workStartTime != null">work_start_time = #{workStartTime},</if>
            <if test="workEndTime != null">work_end_time = #{workEndTime},</if>
            <if test="lateThreshold != null">late_threshold = #{lateThreshold},</if>
            <if test="earlyThreshold != null">early_threshold = #{earlyThreshold},</if>
            <if test="workDays != null and workDays != ''">work_days = #{workDays},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteSysAttendanceRuleByRuleId" parameterType="Long">
        delete from sys_attendance_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteSysAttendanceRuleByRuleIds" parameterType="String">
        delete from sys_attendance_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>
</mapper> 