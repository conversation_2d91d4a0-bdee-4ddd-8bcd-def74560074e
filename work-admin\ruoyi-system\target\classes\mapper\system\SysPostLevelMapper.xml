<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysPostLevelMapper">

	<resultMap type="SysPostLevel" id="SysPostLevelResult">
		<id     property="levelId"              column="level_id"           />
		<result property="postId"               column="post_id"            />
		<result property="levelCode"            column="level_code"         />
		<result property="levelName"            column="level_name"         />
		<result property="levelOrder"           column="level_order"        />
		<result property="baseSalary"           column="base_salary"        />
		<result property="performanceSalary"    column="performance_salary" />
		<result property="allowance"            column="allowance"          />
		<result property="totalSalary"          column="total_salary"       />
		<result property="minExperience"        column="min_experience"     />
		<result property="maxExperience"        column="max_experience"     />
		<result property="skillRequirements"    column="skill_requirements" />
		<result property="status"               column="status"             />
		<result property="createBy"             column="create_by"          />
		<result property="createTime"           column="create_time"        />
		<result property="updateBy"             column="update_by"          />
		<result property="updateTime"           column="update_time"        />
		<result property="remark"               column="remark"             />
		<result property="postName"             column="post_name"          />
	</resultMap>
	
	<sql id="selectSysPostLevelVo">
        select l.level_id, l.post_id, l.level_code, l.level_name, l.level_order, 
               l.base_salary, l.performance_salary, l.allowance, l.total_salary,
               l.min_experience, l.max_experience, l.skill_requirements, l.status,
               l.create_by, l.create_time, l.update_by, l.update_time, l.remark,
               p.post_name
		from sys_post_level l
		left join sys_post p on l.post_id = p.post_id
    </sql>
	
	<select id="selectSysPostLevelList" parameterType="SysPostLevel" resultMap="SysPostLevelResult">
	    <include refid="selectSysPostLevelVo"/>
		<where>
			<if test="postId != null">
				AND l.post_id = #{postId}
			</if>
			<if test="levelCode != null and levelCode != ''">
				AND l.level_code like concat('%', #{levelCode}, '%')
			</if>
			<if test="levelName != null and levelName != ''">
				AND l.level_name like concat('%', #{levelName}, '%')
			</if>
			<if test="status != null and status != ''">
				AND l.status = #{status}
			</if>
		</where>
		order by l.post_id, l.level_order
	</select>
	
	<select id="selectSysPostLevelByLevelId" parameterType="Long" resultMap="SysPostLevelResult">
		<include refid="selectSysPostLevelVo"/>
		where l.level_id = #{levelId}
	</select>

	<select id="selectSysPostLevelByPostId" parameterType="Long" resultMap="SysPostLevelResult">
		<include refid="selectSysPostLevelVo"/>
		where l.post_id = #{postId}
		order by l.level_order
	</select>
	
	<select id="checkLevelCodeUnique" resultMap="SysPostLevelResult">
		<include refid="selectSysPostLevelVo"/>
		where l.post_id = #{postId} and l.level_code = #{levelCode} limit 1
	</select>
	
	<update id="updateSysPostLevel" parameterType="SysPostLevel">
 		update sys_post_level
 		<set>
 			<if test="postId != null">post_id = #{postId},</if>
 			<if test="levelCode != null and levelCode != ''">level_code = #{levelCode},</if>
 			<if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
 			<if test="levelOrder != null">level_order = #{levelOrder},</if>
 			<if test="baseSalary != null">base_salary = #{baseSalary},</if>
 			<if test="performanceSalary != null">performance_salary = #{performanceSalary},</if>
 			<if test="allowance != null">allowance = #{allowance},</if>
 			<if test="totalSalary != null">total_salary = #{totalSalary},</if>
 			<if test="minExperience != null">min_experience = #{minExperience},</if>
 			<if test="maxExperience != null">max_experience = #{maxExperience},</if>
 			<if test="skillRequirements != null">skill_requirements = #{skillRequirements},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where level_id = #{levelId}
	</update>
 	
 	<insert id="insertSysPostLevel" parameterType="SysPostLevel" useGeneratedKeys="true" keyProperty="levelId">
 		insert into sys_post_level(
 			<if test="levelId != null and levelId != 0">level_id,</if>
 			<if test="postId != null">post_id,</if>
 			<if test="levelCode != null and levelCode != ''">level_code,</if>
 			<if test="levelName != null and levelName != ''">level_name,</if>
 			<if test="levelOrder != null">level_order,</if>
 			<if test="baseSalary != null">base_salary,</if>
 			<if test="performanceSalary != null">performance_salary,</if>
 			<if test="allowance != null">allowance,</if>
 			<if test="totalSalary != null">total_salary,</if>
 			<if test="minExperience != null">min_experience,</if>
 			<if test="maxExperience != null">max_experience,</if>
 			<if test="skillRequirements != null">skill_requirements,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="levelId != null and levelId != 0">#{levelId},</if>
 			<if test="postId != null">#{postId},</if>
 			<if test="levelCode != null and levelCode != ''">#{levelCode},</if>
 			<if test="levelName != null and levelName != ''">#{levelName},</if>
 			<if test="levelOrder != null">#{levelOrder},</if>
 			<if test="baseSalary != null">#{baseSalary},</if>
 			<if test="performanceSalary != null">#{performanceSalary},</if>
 			<if test="allowance != null">#{allowance},</if>
 			<if test="totalSalary != null">#{totalSalary},</if>
 			<if test="minExperience != null">#{minExperience},</if>
 			<if test="maxExperience != null">#{maxExperience},</if>
 			<if test="skillRequirements != null">#{skillRequirements},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>
	
	<delete id="deleteSysPostLevelByLevelId" parameterType="Long">
		delete from sys_post_level where level_id = #{levelId}
	</delete>
	
	<delete id="deleteSysPostLevelByLevelIds" parameterType="Long">
 		delete from sys_post_level where level_id in
 		<foreach collection="array" item="levelId" open="(" separator="," close=")">
 			#{levelId}
        </foreach> 
 	</delete>

	<delete id="deleteSysPostLevelByPostId" parameterType="Long">
		delete from sys_post_level where post_id = #{postId}
	</delete>

</mapper>