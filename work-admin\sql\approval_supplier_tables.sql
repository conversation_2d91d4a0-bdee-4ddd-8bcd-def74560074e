-- 流程配置表和供应商管理表

-- 审批流程模板表
CREATE TABLE `approval_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型 PROCUREMENT-采购',
  `procurement_type` varchar(20) COMMENT '采购类型过滤',
  `min_amount` decimal(12,2) DEFAULT NULL COMMENT '适用最小金额',
  `max_amount` decimal(12,2) DEFAULT NULL COMMENT '适用最大金额',
  `urgency_levels` varchar(20) COMMENT '适用紧急程度 1,2,3',
  `dept_ids` text COMMENT '适用部门IDs，JSON格式',
  `condition_expression` varchar(1000) COMMENT '条件表达式',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认模板',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `sort_order` int(5) DEFAULT 0 COMMENT '排序',
  `description` varchar(500) COMMENT '模板描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_business_type` (`business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批流程模板表';

-- 审批步骤模板表
CREATE TABLE `approval_step_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `step_no` int(5) NOT NULL COMMENT '步骤序号',
  `step_name` varchar(100) NOT NULL COMMENT '步骤名称',
  `step_type` varchar(20) NOT NULL COMMENT '步骤类型 APPROVAL-审批 NOTIFY-通知 CONDITION-条件',
  `approver_type` varchar(20) NOT NULL COMMENT '审批人类型 USER-用户 ROLE-角色 DEPT-部门 POSITION-职位 POLICY-策略',
  `approver_value` varchar(200) COMMENT '审批人值',
  `approver_policy` varchar(50) COMMENT '审批人策略 DEPT_MANAGER-部门主管 DIRECT_SUPERVISOR-直属上级',
  `approval_mode` varchar(20) DEFAULT 'SINGLE' COMMENT '审批模式 SINGLE-单人 ALL-全部 ANY-任一',
  `is_optional` tinyint(1) DEFAULT 0 COMMENT '是否可选步骤',
  `is_parallel` tinyint(1) DEFAULT 0 COMMENT '是否并行审批',
  `condition_expression` varchar(1000) COMMENT '条件表达式',
  `timeout_hours` int(5) DEFAULT 0 COMMENT '超时小时数，0表示不限制',
  `auto_approve_on_timeout` tinyint(1) DEFAULT 0 COMMENT '超时是否自动通过',
  `sort_order` int(5) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_step_no` (`step_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批步骤模板表';

-- 供应商信息表
CREATE TABLE `supplier_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(200) NOT NULL COMMENT '供应商名称',
  `supplier_type` varchar(20) NOT NULL COMMENT '供应商类型 MANUFACTURER-生产商 DISTRIBUTOR-经销商 SERVICE-服务商',
  `contact_person` varchar(50) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `address` varchar(500) COMMENT '地址',
  `business_license` varchar(100) COMMENT '营业执照号',
  `tax_number` varchar(50) COMMENT '税号',
  `bank_account` varchar(100) COMMENT '银行账号',
  `bank_name` varchar(200) COMMENT '开户银行',
  `credit_rating` varchar(10) COMMENT '信用等级 A+ A B C D',
  `cooperation_years` int(5) DEFAULT 0 COMMENT '合作年限',
  `total_amount` decimal(15,2) DEFAULT 0 COMMENT '累计采购金额',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-正常 2-暂停 3-黑名单',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_name` (`supplier_name`),
  KEY `idx_supplier_type` (`supplier_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商信息表';

-- 供应商商品表
CREATE TABLE `supplier_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_code` varchar(50) COMMENT '商品编码',
  `category` varchar(50) COMMENT '商品分类',
  `brand` varchar(100) COMMENT '品牌',
  `model` varchar(100) COMMENT '型号',
  `specification` text COMMENT '规格描述',
  `unit` varchar(20) COMMENT '单位',
  `unit_price` decimal(10,2) COMMENT '单价',
  `min_order_qty` decimal(10,2) DEFAULT 1 COMMENT '最小起订量',
  `delivery_days` int(5) DEFAULT 0 COMMENT '交货天数',
  `quality_guarantee` varchar(100) COMMENT '质量保证',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否有效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商商品表';

-- 菜单SQL
-- 1. 插入审批模板菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('审批模板', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '采购管理' LIMIT 1) AS temp), 2, 'template', 'procurement/template/index', NULL, 1, 0, 'C', '0', '0', 'system:template:list', 'tree-table', 'admin', NOW(), '', NULL, '审批模板菜单');

-- 2. 获取审批模板菜单ID
SET @template_menu_id = LAST_INSERT_ID();

-- 3. 插入审批模板功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('审批模板查询', @template_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'system:template:query', '#', 'admin', NOW(), '', NULL, ''),
('审批模板新增', @template_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'system:template:add', '#', 'admin', NOW(), '', NULL, ''),
('审批模板修改', @template_menu_id, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'system:template:edit', '#', 'admin', NOW(), '', NULL, ''),
('审批模板删除', @template_menu_id, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'system:template:remove', '#', 'admin', NOW(), '', NULL, ''),
('审批模板导出', @template_menu_id, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'system:template:export', '#', 'admin', NOW(), '', NULL, '');

-- 4. 插入供应商管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('供应商管理', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '采购管理' LIMIT 1) AS temp), 3, 'supplier', 'procurement/supplier/index', NULL, 1, 0, 'C', '0', '0', 'system:supplier:list', 'peoples', 'admin', NOW(), '', NULL, '供应商管理菜单');

-- 5. 获取供应商管理菜单ID
SET @supplier_menu_id = LAST_INSERT_ID();

-- 6. 插入供应商管理功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('供应商查询', @supplier_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:query', '#', 'admin', NOW(), '', NULL, ''),
('供应商新增', @supplier_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:add', '#', 'admin', NOW(), '', NULL, ''),
('供应商修改', @supplier_menu_id, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:edit', '#', 'admin', NOW(), '', NULL, ''),
('供应商删除', @supplier_menu_id, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:remove', '#', 'admin', NOW(), '', NULL, ''),
('供应商导出', @supplier_menu_id, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'system:supplier:export', '#', 'admin', NOW(), '', NULL, '');

