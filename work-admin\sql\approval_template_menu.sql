-- 审批模板菜单SQL - 单独版本
-- 只创建审批模板相关的菜单和权限

-- 1. 确保采购管理主菜单存在
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('采购管理', 0, 6, 'procurement', null, 1, 0, 'M', '0', '0', '', 'shopping', 'admin', NOW(), '', null, '采购管理目录');

-- 2. 创建审批模板管理菜单
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '审批模板', menu_id, 1, 'template', 'procurement/template/index', 1, 0, 'C', '0', '0', 'system:template:list', 'form', 'admin', NOW(), '', null, '审批模板管理菜单'
FROM sys_menu 
WHERE menu_name = '采购管理' AND parent_id = 0 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template')
LIMIT 1;

-- 3. 创建审批模板按钮权限
-- 模板查询权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板查询', menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'system:template:query', '#', 'admin', NOW(), '', null, ''
FROM sys_menu 
WHERE menu_name = '审批模板' AND path = 'template' 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:template:query')
LIMIT 1;

-- 模板新增权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板新增', menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'system:template:add', '#', 'admin', NOW(), '', null, ''
FROM sys_menu 
WHERE menu_name = '审批模板' AND path = 'template' 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:template:add')
LIMIT 1;

-- 模板修改权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板修改', menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'system:template:edit', '#', 'admin', NOW(), '', null, ''
FROM sys_menu 
WHERE menu_name = '审批模板' AND path = 'template' 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:template:edit')
LIMIT 1;

-- 模板删除权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板删除', menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'system:template:remove', '#', 'admin', NOW(), '', null, ''
FROM sys_menu 
WHERE menu_name = '审批模板' AND path = 'template' 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:template:remove')
LIMIT 1;

-- 模板导出权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板导出', menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'system:template:export', '#', 'admin', NOW(), '', null, ''
FROM sys_menu 
WHERE menu_name = '审批模板' AND path = 'template' 
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:template:export')
LIMIT 1;

-- 4. 验证创建结果
SELECT 
    m1.menu_id,
    m1.menu_name,
    m1.path,
    m1.component,
    m1.perms,
    m1.menu_type,
    m2.menu_name as parent_menu
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.parent_id = m2.menu_id
WHERE m1.menu_name IN ('采购管理', '审批模板') 
   OR m1.perms LIKE 'system:template:%'
ORDER BY m1.parent_id, m1.order_num;