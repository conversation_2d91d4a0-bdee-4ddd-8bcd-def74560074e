-- 审批模板表
DROP TABLE IF EXISTS `approval_template`;
CREATE TABLE `approval_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型(PROCUREMENT:采购)',
  `procurement_type` varchar(20) DEFAULT NULL COMMENT '采购类型(OFFICE:办公用品,IT:IT设备,MATERIAL:原材料,SERVICE:服务)',
  `min_amount` decimal(10,2) DEFAULT NULL COMMENT '最小金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '最大金额',
  `urgency_levels` varchar(50) DEFAULT NULL COMMENT '紧急程度(1:普通,2:紧急,3:特急)',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认(0:否,1:是)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活(0:停用,1:激活)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_procurement_type` (`procurement_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批模板表';

-- 审批步骤模板表
DROP TABLE IF EXISTS `approval_step_template`;
CREATE TABLE `approval_step_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '步骤ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `step_no` int(11) NOT NULL COMMENT '步骤序号',
  `step_name` varchar(100) NOT NULL COMMENT '步骤名称',
  `step_type` varchar(20) NOT NULL COMMENT '步骤类型(APPROVAL:审批,NOTIFY:通知)',
  `approver_type` varchar(20) NOT NULL COMMENT '审批人类型(USER:用户,ROLE:角色,DEPT:部门,POLICY:策略)',
  `approver_value` varchar(255) NOT NULL COMMENT '审批人值',
  `approver_policy` varchar(100) DEFAULT NULL COMMENT '审批策略',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_step_no` (`step_no`),
  CONSTRAINT `fk_step_template` FOREIGN KEY (`template_id`) REFERENCES `approval_template` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批步骤模板表';

-- 插入测试数据
INSERT INTO `approval_template` VALUES 
(1, '办公用品采购审批模板', 'OFFICE_APPROVAL', 'PROCUREMENT', 'OFFICE', 0.00, 5000.00, '1,2', 1, 1, 1, '办公用品采购审批流程模板', 'admin', NOW(), 'admin', NOW(), '适用于5000元以下办公用品采购'),
(2, 'IT设备采购审批模板', 'IT_APPROVAL', 'PROCUREMENT', 'IT', 5000.00, 50000.00, '1,2,3', 0, 1, 2, 'IT设备采购审批流程模板', 'admin', NOW(), 'admin', NOW(), '适用于5000-50000元IT设备采购'),
(3, '大额采购审批模板', 'HIGH_AMOUNT_APPROVAL', 'PROCUREMENT', NULL, 50000.00, NULL, '2,3', 0, 1, 3, '大额采购审批流程模板', 'admin', NOW(), 'admin', NOW(), '适用于50000元以上大额采购');

INSERT INTO `approval_step_template` VALUES 
-- 办公用品采购审批模板步骤
(1, 1, 1, '部门主管审批', 'APPROVAL', 'ROLE', 'dept_manager', '单人审批', 1, 'admin', NOW(), 'admin', NOW(), '部门主管审批'),
(2, 1, 2, '财务审核', 'APPROVAL', 'ROLE', 'finance', '单人审批', 2, 'admin', NOW(), 'admin', NOW(), '财务部门审核'),
(3, 1, 3, '采购执行', 'NOTIFY', 'ROLE', 'procurement', '通知', 3, 'admin', NOW(), 'admin', NOW(), '通知采购部门执行'),

-- IT设备采购审批模板步骤
(4, 2, 1, '部门主管审批', 'APPROVAL', 'ROLE', 'dept_manager', '单人审批', 1, 'admin', NOW(), 'admin', NOW(), '部门主管审批'),
(5, 2, 2, 'IT部门审核', 'APPROVAL', 'ROLE', 'it_manager', '单人审批', 2, 'admin', NOW(), 'admin', NOW(), 'IT部门技术审核'),
(6, 2, 3, '财务审核', 'APPROVAL', 'ROLE', 'finance', '单人审批', 3, 'admin', NOW(), 'admin', NOW(), '财务部门审核'),
(7, 2, 4, '总经理审批', 'APPROVAL', 'ROLE', 'general_manager', '单人审批', 4, 'admin', NOW(), 'admin', NOW(), '总经理最终审批'),
(8, 2, 5, '采购执行', 'NOTIFY', 'ROLE', 'procurement', '通知', 5, 'admin', NOW(), 'admin', NOW(), '通知采购部门执行'),

-- 大额采购审批模板步骤
(9, 3, 1, '部门主管审批', 'APPROVAL', 'ROLE', 'dept_manager', '单人审批', 1, 'admin', NOW(), 'admin', NOW(), '部门主管审批'),
(10, 3, 2, '财务总监审核', 'APPROVAL', 'ROLE', 'finance_director', '单人审批', 2, 'admin', NOW(), 'admin', NOW(), '财务总监审核'),
(11, 3, 3, '总经理审批', 'APPROVAL', 'ROLE', 'general_manager', '单人审批', 3, 'admin', NOW(), 'admin', NOW(), '总经理审批'),
(12, 3, 4, '董事长审批', 'APPROVAL', 'ROLE', 'chairman', '单人审批', 4, 'admin', NOW(), 'admin', NOW(), '董事长最终审批'),
(13, 3, 5, '采购执行', 'NOTIFY', 'ROLE', 'procurement', '通知', 5, 'admin', NOW(), 'admin', NOW(), '通知采购部门执行');