-- 考勤表
DROP TABLE IF EXISTS `sys_attendance`;
CREATE TABLE `sys_attendance` (
  `attendance_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '考勤ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `check_in_time` datetime DEFAULT NULL COMMENT '签到时间',
  `check_out_time` datetime DEFAULT NULL COMMENT '签退时间',
  `work_hours` decimal(4,2) DEFAULT NULL COMMENT '工作时长(小时)',
  `attendance_status` char(1) DEFAULT '0' COMMENT '考勤状态（0正常 1迟到 2早退 3旷工 4请假）',
  `late_minutes` int(11) DEFAULT 0 COMMENT '迟到分钟数',
  `early_minutes` int(11) DEFAULT 0 COMMENT '早退分钟数',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`attendance_id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `attendance_date`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_attendance_date` (`attendance_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='考勤记录表';

-- 考勤规则表
DROP TABLE IF EXISTS `sys_attendance_rule`;
CREATE TABLE `sys_attendance_rule` (
  `rule_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `rule_name` varchar(50) NOT NULL COMMENT '规则名称',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID（为空表示全公司）',
  `work_start_time` time NOT NULL COMMENT '上班时间',
  `work_end_time` time NOT NULL COMMENT '下班时间',
  `late_threshold` int(11) DEFAULT 15 COMMENT '迟到阈值(分钟)',
  `early_threshold` int(11) DEFAULT 15 COMMENT '早退阈值(分钟)',
  `work_days` varchar(20) DEFAULT '1,2,3,4,5' COMMENT '工作日(1-7表示周一到周日)',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='考勤规则表';

-- 插入默认考勤规则
INSERT INTO `sys_attendance_rule` VALUES 
(1, '默认规则', NULL, '09:00:00', '18:00:00', 15, 15, '1,2,3,4,5', '0', 'admin', sysdate(), '', NULL);

-- 考勤菜单
INSERT INTO `sys_menu` (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2100, '考勤管理', 0, 6, 'attendance', NULL, '', 1, 0, 'M', '0', '0', '', 'schedule', 'admin', sysdate(), '考勤管理目录'),
(2101, '考勤记录', 2100, 1, 'record', 'attendance/record/index', '', 1, 0, 'C', '0', '0', 'attendance:record:list', 'calendar', 'admin', sysdate(), '考勤记录菜单'),
(2102, '考勤规则', 2100, 2, 'rule', 'attendance/rule/index', '', 1, 0, 'C', '0', '0', 'attendance:rule:list', 'setting', 'admin', sysdate(), '考勤规则菜单');

-- 考勤记录操作权限
INSERT INTO `sys_menu` (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2103, '考勤记录查询', 2101, 1, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:query', '#', 'admin', sysdate(), ''),
(2104, '考勤记录新增', 2101, 2, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:add', '#', 'admin', sysdate(), ''),
(2105, '考勤记录修改', 2101, 3, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:edit', '#', 'admin', sysdate(), ''),
(2106, '考勤记录删除', 2101, 4, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:remove', '#', 'admin', sysdate(), ''),
(2107, '考勤记录导出', 2101, 5, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:export', '#', 'admin', sysdate(), ''),
(2108, '考勤打卡', 2101, 6, '', '', '', 1, 0, 'F', '0', '0', 'attendance:record:clock', '#', 'admin', sysdate(), '');

-- 考勤规则操作权限
INSERT INTO `sys_menu` (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2109, '考勤规则查询', 2102, 1, '', '', '', 1, 0, 'F', '0', '0', 'attendance:rule:query', '#', 'admin', sysdate(), ''),
(2110, '考勤规则新增', 2102, 2, '', '', '', 1, 0, 'F', '0', '0', 'attendance:rule:add', '#', 'admin', sysdate(), ''),
(2111, '考勤规则修改', 2102, 3, '', '', '', 1, 0, 'F', '0', '0', 'attendance:rule:edit', '#', 'admin', sysdate(), ''),
(2112, '考勤规则删除', 2102, 4, '', '', '', 1, 0, 'F', '0', '0', 'attendance:rule:remove', '#', 'admin', sysdate(), '');

-- 考勤状态字典
INSERT INTO `sys_dict_type` VALUES(100, '考勤状态', 'attendance_status', '0', 'admin', sysdate(), '', NULL, '考勤状态列表');
INSERT INTO `sys_dict_data` VALUES(100, 1, '正常', '0', 'attendance_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '正常考勤');
INSERT INTO `sys_dict_data` VALUES(101, 2, '迟到', '1', 'attendance_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '迟到');
INSERT INTO `sys_dict_data` VALUES(102, 3, '早退', '2', 'attendance_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '早退');
INSERT INTO `sys_dict_data` VALUES(103, 4, '旷工', '3', 'attendance_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '旷工');
INSERT INTO `sys_dict_data` VALUES(104, 5, '请假', '4', 'attendance_status', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '请假'); 