-- 清理重复菜单的SQL脚本
-- 注意：执行前请备份数据库！

-- 1. 查看重复的菜单项（先执行这个查看是否有重复）
SELECT menu_name, path, COUNT(*) as count 
FROM sys_menu 
WHERE menu_name IN ('审批模板', '供应商管理', '采购申请', '采购管理')
GROUP BY menu_name, path 
HAVING COUNT(*) > 1;

-- 2. 删除重复的审批模板相关菜单（保留ID最小的）
DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.path = m2.path
AND m1.menu_name = '审批模板';

-- 3. 删除重复的供应商管理相关菜单（保留ID最小的）
DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.path = m2.path
AND m1.menu_name = '供应商管理';

-- 4. 删除重复的采购申请相关菜单（保留ID最小的）
DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.path = m2.path
AND m1.menu_name = '采购申请';

-- 5. 删除重复的采购管理主菜单（保留ID最小的）
DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.path = m2.path
AND m1.menu_name = '采购管理';

-- 6. 删除重复的按钮权限菜单
DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.perms = m2.perms
AND m1.menu_type = 'F'
AND m1.perms LIKE 'system:template:%';

DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.perms = m2.perms
AND m1.menu_type = 'F'
AND m1.perms LIKE 'system:supplier:%';

DELETE m1 FROM sys_menu m1
INNER JOIN sys_menu m2 
WHERE m1.menu_id > m2.menu_id 
AND m1.menu_name = m2.menu_name 
AND m1.perms = m2.perms
AND m1.menu_type = 'F'
AND m1.perms LIKE 'system:application:%';

-- 7. 验证清理结果（执行完成后再次查看是否还有重复）
SELECT menu_name, path, COUNT(*) as count 
FROM sys_menu 
WHERE menu_name IN ('审批模板', '供应商管理', '采购申请', '采购管理')
GROUP BY menu_name, path 
HAVING COUNT(*) > 1;