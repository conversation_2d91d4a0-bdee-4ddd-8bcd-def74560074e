-- ----------------------------
-- 发票识别表
-- ----------------------------
DROP TABLE IF EXISTS `invoice_recognition`;
CREATE TABLE `invoice_recognition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `invoice_type` varchar(50) DEFAULT NULL COMMENT '发票类型',
  `invoice_code` varchar(50) DEFAULT NULL COMMENT '发票代码',
  `invoice_number` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `issue_date` date DEFAULT NULL COMMENT '开票日期',
  `check_code` varchar(20) DEFAULT NULL COMMENT '验证码',
  `machine_number` varchar(50) DEFAULT NULL COMMENT '机器编号',
  `tax_control_code` varchar(500) DEFAULT NULL COMMENT '密码区',
  `buyer_name` varchar(200) DEFAULT NULL COMMENT '购买方名称',
  `buyer_tax_id` varchar(50) DEFAULT NULL COMMENT '购买方纳税人识别号',
  `buyer_addr_tel` varchar(500) DEFAULT NULL COMMENT '购买方地址电话',
  `buyer_financial_account` varchar(500) DEFAULT NULL COMMENT '购买方开户行及账号',
  `seller_name` varchar(200) DEFAULT NULL COMMENT '销售方名称',
  `seller_tax_id` varchar(50) DEFAULT NULL COMMENT '销售方纳税人识别号',
  `seller_addr_tel` varchar(500) DEFAULT NULL COMMENT '销售方地址电话',
  `seller_financial_account` varchar(500) DEFAULT NULL COMMENT '销售方开户行及账号',
  `total_amount` decimal(15,2) DEFAULT NULL COMMENT '价税合计',
  `tax_amount` decimal(15,2) DEFAULT NULL COMMENT '税额',
  `amount_without_tax` decimal(15,2) DEFAULT NULL COMMENT '不含税金额',
  `invoice_clerk` varchar(50) DEFAULT NULL COMMENT '开票人',
  `payee` varchar(50) DEFAULT NULL COMMENT '收款人',
  `checker` varchar(50) DEFAULT NULL COMMENT '复核人',
  `note` varchar(1000) DEFAULT NULL COMMENT '备注',
  `invoice_title` varchar(200) DEFAULT NULL COMMENT '发票标题',
  `original_file_name` varchar(200) DEFAULT NULL COMMENT '原始文件名',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `status` char(1) DEFAULT '0' COMMENT '识别状态（0识别中 1识别成功 2识别失败）',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `goods_info_json` longtext COMMENT '货物或服务清单JSON',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_invoice_number` (`invoice_number`),
  KEY `idx_invoice_code` (`invoice_code`),
  KEY `idx_buyer_tax_id` (`buyer_tax_id`),
  KEY `idx_seller_tax_id` (`seller_tax_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='发票识别表';

-- ----------------------------
-- 菜单 SQL
-- ----------------------------
-- 父菜单
INSERT INTO `sys_menu` VALUES (2100, '发票管理', 0, 6, 'invoice', NULL, NULL, 1, 0, 'M', '0', '0', '', 'invoice', 'admin', sysdate(), '', NULL, '发票管理目录');

-- 发票识别菜单
INSERT INTO `sys_menu` VALUES (2101, '发票识别', 2100, 1, 'recognition', 'system/invoice/index', NULL, 1, 0, 'C', '0', '0', 'system:invoice:list', 'invoice', 'admin', sysdate(), '', NULL, '发票识别菜单');

-- 发票识别按钮
INSERT INTO `sys_menu` VALUES (2102, '发票识别查询', 2101, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:query', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2103, '发票识别新增', 2101, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:add', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2104, '发票识别修改', 2101, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:edit', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2105, '发票识别删除', 2101, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:remove', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2106, '发票识别导出', 2101, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:export', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2107, '发票文件上传', 2101, 6, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:upload', '#', 'admin', sysdate(), '', NULL, '');
INSERT INTO `sys_menu` VALUES (2108, '发票识别处理', 2101, 7, '', '', NULL, 1, 0, 'F', '0', '0', 'system:invoice:recognize', '#', 'admin', sysdate(), '', NULL, '');
