-- ============================================
-- 请假管理模块 SQL脚本
-- ============================================

-- ----------------------------
-- 1、假期类型表
-- ----------------------------
DROP TABLE IF EXISTS `leave_type`;
CREATE TABLE `leave_type` (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '假期类型ID',
  `type_code` varchar(50) NOT NULL COMMENT '假期类型代码',
  `type_name` varchar(100) NOT NULL COMMENT '假期类型名称',
  `is_paid` char(1) DEFAULT '1' COMMENT '是否带薪（0否 1是）',
  `max_days` int(11) DEFAULT NULL COMMENT '最大请假天数',
  `need_proof` char(1) DEFAULT '0' COMMENT '是否需要证明（0否 1是）',
  `advance_days` int(11) DEFAULT '1' COMMENT '提前申请天数',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`type_id`),
  UNIQUE KEY `uk_type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='假期类型表';

-- ----------------------------
-- 2、请假申请表
-- ----------------------------
DROP TABLE IF EXISTS `leave_application`;
CREATE TABLE `leave_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `application_no` varchar(50) NOT NULL COMMENT '申请单号',
  `user_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `user_name` varchar(30) NOT NULL COMMENT '申请人姓名',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `dept_name` varchar(50) DEFAULT NULL COMMENT '部门名称',
  `type_id` bigint(20) NOT NULL COMMENT '假期类型ID',
  `type_name` varchar(100) NOT NULL COMMENT '假期类型名称',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `total_days` decimal(3,1) NOT NULL COMMENT '请假天数',
  `reason` varchar(500) NOT NULL COMMENT '请假原因',
  `proof_file` varchar(255) DEFAULT NULL COMMENT '证明文件',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态（PENDING待审批，APPROVED已通过，REJECTED已拒绝，CANCELLED已撤销）',
  `approver_id` bigint(20) DEFAULT NULL COMMENT '审批人ID',
  `approver_name` varchar(30) DEFAULT NULL COMMENT '审批人姓名',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_remark` varchar(500) DEFAULT NULL COMMENT '审批备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`),
  UNIQUE KEY `uk_application_no` (`application_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='请假申请表';

-- ----------------------------
-- 3、初始化假期类型数据
-- ----------------------------
INSERT INTO `leave_type` VALUES 
(1, 'ANNUAL', '年假', '1', 15, '0', 3, '员工年度假期', '0', 'admin', sysdate(), '', NULL, ''),
(2, 'SICK', '病假', '1', 30, '1', 0, '因病请假', '0', 'admin', sysdate(), '', NULL, ''),
(3, 'PERSONAL', '事假', '0', 10, '0', 1, '个人事务请假', '0', 'admin', sysdate(), '', NULL, ''),
(4, 'MATERNITY', '产假', '1', 128, '1', 30, '女员工产假', '0', 'admin', sysdate(), '', NULL, ''),
(5, 'PATERNITY', '陪产假', '1', 15, '1', 7, '男员工陪产假', '0', 'admin', sysdate(), '', NULL, ''),
(6, 'MARRIAGE', '婚假', '1', 3, '1', 7, '员工结婚假期', '0', 'admin', sysdate(), '', NULL, ''),
(7, 'BEREAVEMENT', '丧假', '1', 7, '1', 0, '直系亲属丧假', '0', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 4、菜单权限SQL
-- ----------------------------

-- 请假管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假管理', '1', '6', 'leave', NULL, 1, 0, 'M', '0', '0', '', 'calendar', 'admin', sysdate(), '', NULL, '请假管理目录');

-- 获取请假管理菜单ID
SELECT @leaveMenuId := LAST_INSERT_ID();

-- 假期类型管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型', @leaveMenuId, '1', 'type', 'leave/type/index', 1, 0, 'C', '0', '0', 'leave:type:list', 'list', 'admin', sysdate(), '', NULL, '假期类型菜单');

-- 获取假期类型菜单ID
SELECT @typeMenuId := LAST_INSERT_ID();

-- 假期类型按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型查询', @typeMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'leave:type:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型新增', @typeMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'leave:type:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型修改', @typeMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'leave:type:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型删除', @typeMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'leave:type:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('假期类型导出', @typeMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'leave:type:export', '#', 'admin', sysdate(), '', NULL, '');

-- 请假申请管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请', @leaveMenuId, '2', 'application', 'leave/application/index', 1, 0, 'C', '0', '0', 'leave:application:list', 'form', 'admin', sysdate(), '', NULL, '请假申请菜单');

-- 获取请假申请菜单ID
SELECT @applicationMenuId := LAST_INSERT_ID();

-- 请假申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请查询', @applicationMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'leave:application:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请新增', @applicationMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'leave:application:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请修改', @applicationMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'leave:application:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请删除', @applicationMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'leave:application:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假申请导出', @applicationMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'leave:application:export', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假审批', @applicationMenuId, '6', '#', '', 1, 0, 'F', '0', '0', 'leave:application:approve', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('请假撤销', @applicationMenuId, '7', '#', '', 1, 0, 'F', '0', '0', 'leave:application:cancel', '#', 'admin', sysdate(), '', NULL, '');

-- ----------------------------
-- 5、字典数据SQL（请假状态）
-- ----------------------------

-- 删除已存在的字典类型
DELETE FROM sys_dict_type WHERE dict_type = 'leave_status';
DELETE FROM sys_dict_data WHERE dict_type = 'leave_status';

-- 插入字典类型
INSERT INTO sys_dict_type VALUES(NULL, '请假状态', 'leave_status', '0', 'admin', sysdate(), '', NULL, '请假申请状态列表');

-- 获取字典类型ID
SELECT @dictTypeId := LAST_INSERT_ID();

-- 插入字典数据
INSERT INTO sys_dict_data VALUES(NULL, 1, '待审批', 'PENDING', 'leave_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '待审批状态');
INSERT INTO sys_dict_data VALUES(NULL, 2, '已通过', 'APPROVED', 'leave_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已通过状态');
INSERT INTO sys_dict_data VALUES(NULL, 3, '已拒绝', 'REJECTED', 'leave_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '已拒绝状态');
INSERT INTO sys_dict_data VALUES(NULL, 4, '已撤销', 'CANCELLED', 'leave_status', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '已撤销状态');

-- ----------------------------
-- 说明：
-- 1. 执行此脚本前请确保数据库已创建
-- 2. 假期类型代码请根据企业实际情况调整
-- 3. 菜单父级ID(parent_id=1)对应系统管理，请根据实际情况调整
-- 4. 如需修改菜单顺序，请调整order_num字段
-- ---------------------------- 