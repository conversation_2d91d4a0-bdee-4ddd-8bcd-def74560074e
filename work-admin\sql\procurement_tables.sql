-- 采购模块核心业务表

-- 采购申请表
CREATE TABLE `procurement_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `title` varchar(200) NOT NULL COMMENT '申请标题',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '申请人姓名',
  `dept_id` bigint(20) NOT NULL COMMENT '申请部门ID',
  `dept_name` varchar(100) NOT NULL COMMENT '申请部门名称',
  `procurement_type` varchar(20) NOT NULL COMMENT '采购类型 OFFICE-办公用品 IT-IT设备 MATERIAL-原材料 SERVICE-服务',
  `total_amount` decimal(12,2) NOT NULL COMMENT '申请总金额',
  `urgency_level` tinyint(2) DEFAULT 1 COMMENT '紧急程度 1-普通 2-紧急 3-特急',
  `expected_date` date COMMENT '期望到货日期',
  `reason` text COMMENT '申请理由',
  `attachment_urls` text COMMENT '附件URLs，JSON格式',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-草稿 2-待审批 3-审批中 4-已通过 5-已拒绝 6-已采购 7-已入库 8-已报销 9-已关闭',
  `current_step` int(5) DEFAULT 0 COMMENT '当前审批步骤',
  `process_instance_id` varchar(100) COMMENT '流程实例ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`),
  KEY `idx_applicant_status` (`applicant_id`, `status`),
  KEY `idx_dept_status` (`dept_id`, `status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请表';

-- 采购明细表
CREATE TABLE `procurement_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` bigint(20) NOT NULL COMMENT '申请ID',
  `item_category` varchar(50) COMMENT '物品分类',
  `item_name` varchar(200) NOT NULL COMMENT '物品名称',
  `item_code` varchar(50) COMMENT '物品编码',
  `brand` varchar(100) COMMENT '品牌',
  `model` varchar(100) COMMENT '型号',
  `specification` text COMMENT '规格描述',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) COMMENT '预估单价',
  `total_price` decimal(12,2) COMMENT '预估总价',
  `actual_unit_price` decimal(10,2) COMMENT '实际单价',
  `actual_total_price` decimal(12,2) COMMENT '实际总价',
  `supplier_name` varchar(200) COMMENT '供应商名称',
  `purchase_url` varchar(1000) COMMENT '采购链接',
  `screenshot_url` varchar(500) COMMENT '截图附件',
  `remark` varchar(500) COMMENT '备注',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-待采购 2-已采购 3-已入库',
  `purchase_date` date COMMENT '采购日期',
  `receipt_date` date COMMENT '收货日期',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_by` varchar(50) DEFAULT NULL,
  `update_by` varchar(50) DEFAULT NULL,
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_item_category` (`item_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购明细表';

-- 菜单SQL
-- 1. 插入采购管理目录
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('采购管理', 0, 6, 'procurement', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'shopping', 'admin', NOW(), '', NULL, '采购管理目录');

-- 2. 获取采购管理目录ID
SET @procurement_menu_id = LAST_INSERT_ID();

-- 3. 插入采购申请菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('采购申请', @procurement_menu_id, 1, 'application', 'procurement/application/index', NULL, 1, 0, 'C', '0', '0', 'system:procurement:list', 'form', 'admin', NOW(), '', NULL, '采购申请菜单');

-- 4. 获取采购申请菜单ID
SET @application_menu_id = LAST_INSERT_ID();

-- 5. 插入采购申请功能按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('采购申请查询', @application_menu_id, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'system:procurement:query', '#', 'admin', NOW(), '', NULL, ''),
('采购申请新增', @application_menu_id, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'system:procurement:add', '#', 'admin', NOW(), '', NULL, ''),
('采购申请修改', @application_menu_id, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'system:procurement:edit', '#', 'admin', NOW(), '', NULL, ''),
('采购申请删除', @application_menu_id, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'system:procurement:remove', '#', 'admin', NOW(), '', NULL, ''),
('采购申请导出', @application_menu_id, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'system:procurement:export', '#', 'admin', NOW(), '', NULL, '');