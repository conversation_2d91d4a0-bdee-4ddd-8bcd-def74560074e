-- 采购管理菜单SQL
-- 注意：请按顺序执行以下SQL语句

-- 1. 创建采购管理主菜单
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('采购管理', 0, 6, 'procurement', null, 1, 0, 'M', '0', '0', '', 'shopping', 'admin', NOW(), '', null, '采购管理目录');

-- 2. 创建审批模板管理菜单 (请先查询采购管理的menu_id，然后替换下面的parent_id)
-- 假设采购管理的menu_id是2000，请根据实际情况修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('审批模板', 2000, 1, 'template', 'procurement/template/index', 1, 0, 'C', '0', '0', 'system:template:list', 'form', 'admin', NOW(), '', null, '审批模板管理菜单');

-- 3. 创建供应商管理菜单 (请先查询采购管理的menu_id，然后替换下面的parent_id)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('供应商管理', 2000, 2, 'supplier', 'procurement/supplier/index', 1, 0, 'C', '0', '0', 'system:supplier:list', 'user', 'admin', NOW(), '', null, '供应商管理菜单');

-- 4. 创建采购申请管理菜单 (请先查询采购管理的menu_id，然后替换下面的parent_id)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('采购申请', 2000, 3, 'application', 'procurement/application/index', 1, 0, 'C', '0', '0', 'system:application:list', 'edit', 'admin', NOW(), '', null, '采购申请管理菜单');

-- 5. 审批模板按钮权限 (请先查询审批模板的menu_id，然后替换下面的parent_id)
-- 假设审批模板的menu_id是2001，请根据实际情况修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('模板查询', 2001, 1, '', '', 1, 0, 'F', '0', '0', 'system:template:query', '#', 'admin', NOW(), '', null, ''),
('模板新增', 2001, 2, '', '', 1, 0, 'F', '0', '0', 'system:template:add', '#', 'admin', NOW(), '', null, ''),
('模板修改', 2001, 3, '', '', 1, 0, 'F', '0', '0', 'system:template:edit', '#', 'admin', NOW(), '', null, ''),
('模板删除', 2001, 4, '', '', 1, 0, 'F', '0', '0', 'system:template:remove', '#', 'admin', NOW(), '', null, ''),
('模板导出', 2001, 5, '', '', 1, 0, 'F', '0', '0', 'system:template:export', '#', 'admin', NOW(), '', null, '');

-- 6. 供应商管理按钮权限 (请先查询供应商管理的menu_id，然后替换下面的parent_id)
-- 假设供应商管理的menu_id是2002，请根据实际情况修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('供应商查询', 2002, 1, '', '', 1, 0, 'F', '0', '0', 'system:supplier:query', '#', 'admin', NOW(), '', null, ''),
('供应商新增', 2002, 2, '', '', 1, 0, 'F', '0', '0', 'system:supplier:add', '#', 'admin', NOW(), '', null, ''),
('供应商修改', 2002, 3, '', '', 1, 0, 'F', '0', '0', 'system:supplier:edit', '#', 'admin', NOW(), '', null, ''),
('供应商删除', 2002, 4, '', '', 1, 0, 'F', '0', '0', 'system:supplier:remove', '#', 'admin', NOW(), '', null, ''),
('供应商导出', 2002, 5, '', '', 1, 0, 'F', '0', '0', 'system:supplier:export', '#', 'admin', NOW(), '', null, '');

-- 7. 采购申请按钮权限 (请先查询采购申请的menu_id，然后替换下面的parent_id)
-- 假设采购申请的menu_id是2003，请根据实际情况修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('申请查询', 2003, 1, '', '', 1, 0, 'F', '0', '0', 'system:application:query', '#', 'admin', NOW(), '', null, ''),
('申请新增', 2003, 2, '', '', 1, 0, 'F', '0', '0', 'system:application:add', '#', 'admin', NOW(), '', null, ''),
('申请修改', 2003, 3, '', '', 1, 0, 'F', '0', '0', 'system:application:edit', '#', 'admin', NOW(), '', null, ''),
('申请删除', 2003, 4, '', '', 1, 0, 'F', '0', '0', 'system:application:remove', '#', 'admin', NOW(), '', null, ''),
('申请导出', 2003, 5, '', '', 1, 0, 'F', '0', '0', 'system:application:export', '#', 'admin', NOW(), '', null, ''),
('申请审批', 2003, 6, '', '', 1, 0, 'F', '0', '0', 'system:application:approve', '#', 'admin', NOW(), '', null, '');

-- 使用说明：
-- 1. 先执行第1步创建主菜单
-- 2. 查询采购管理的menu_id: SELECT menu_id FROM sys_menu WHERE menu_name = '采购管理' AND parent_id = 0;
-- 3. 将查询到的menu_id替换上面SQL中的2000
-- 4. 执行第2-4步创建子菜单
-- 5. 分别查询各子菜单的menu_id，替换第5-7步中的parent_id
-- 6. 执行第5-7步创建按钮权限