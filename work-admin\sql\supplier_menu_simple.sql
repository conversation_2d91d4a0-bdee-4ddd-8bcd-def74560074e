-- 采购管理菜单SQL - 简化版本
-- 可以直接执行，无需手动替换ID

-- 1. 创建采购管理主菜单
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('采购管理', 0, 6, 'procurement', null, 1, 0, 'M', '0', '0', '', 'shopping', 'admin', NOW(), '', null, '采购管理目录');

-- 2. 创建子菜单（使用固定的parent_id，请根据实际情况调整）
-- 审批模板管理
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '审批模板', menu_id, 1, 'template', 'procurement/template/index', 1, 0, 'C', '0', '0', 'system:template:list', 'form', 'admin', NOW(), '', null, '审批模板管理菜单'
FROM sys_menu WHERE menu_name = '采购管理' AND parent_id = 0 LIMIT 1
AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template');

-- 供应商管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商管理', menu_id, 2, 'supplier', 'procurement/supplier/index', 1, 0, 'C', '0', '0', 'system:supplier:list', 'user', 'admin', NOW(), '', null, '供应商管理菜单'
FROM sys_menu WHERE menu_name = '采购管理' AND parent_id = 0 LIMIT 1;

-- 采购申请
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '采购申请', menu_id, 3, 'application', 'procurement/application/index', 1, 0, 'C', '0', '0', 'system:application:list', 'edit', 'admin', NOW(), '', null, '采购申请管理菜单'
FROM sys_menu WHERE menu_name = '采购管理' AND parent_id = 0 LIMIT 1;

-- 3. 创建按钮权限
-- 审批模板按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板查询', menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'system:template:query', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板新增', menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'system:template:add', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板修改', menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'system:template:edit', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板删除', menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'system:template:remove', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '模板导出', menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'system:template:export', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '审批模板' AND path = 'template' LIMIT 1;

-- 供应商管理按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商查询', menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'system:supplier:query', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '供应商管理' AND path = 'supplier' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商新增', menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'system:supplier:add', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '供应商管理' AND path = 'supplier' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商修改', menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'system:supplier:edit', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '供应商管理' AND path = 'supplier' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商删除', menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'system:supplier:remove', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '供应商管理' AND path = 'supplier' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '供应商导出', menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'system:supplier:export', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '供应商管理' AND path = 'supplier' LIMIT 1;

-- 采购申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请查询', menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'system:application:query', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请新增', menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'system:application:add', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请修改', menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'system:application:edit', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请删除', menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'system:application:remove', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请导出', menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'system:application:export', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '申请审批', menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'system:application:approve', '#', 'admin', NOW(), '', null, ''
FROM sys_menu WHERE menu_name = '采购申请' AND path = 'application' LIMIT 1;