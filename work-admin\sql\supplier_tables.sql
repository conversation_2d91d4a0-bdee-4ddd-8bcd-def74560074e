-- 供应商表
DROP TABLE IF EXISTS `sys_supplier`;
CREATE TABLE `sys_supplier` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '供应商ID',
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_type` varchar(20) DEFAULT NULL COMMENT '供应商类型(MANUFACTURER:生产商,DISTRIBUTOR:经销商,SERVICE:服务商)',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `business_license` varchar(100) DEFAULT NULL COMMENT '营业执照号',
  `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '信用等级(A,B,C,D)',
  `cooperation_years` int(11) DEFAULT '0' COMMENT '合作年限',
  `payment_terms` varchar(50) DEFAULT NULL COMMENT '付款条件',
  `delivery_terms` varchar(50) DEFAULT NULL COMMENT '交货条件',
  `quality_certification` varchar(255) DEFAULT NULL COMMENT '质量认证',
  `status` char(1) DEFAULT '1' COMMENT '状态(0:停用,1:正常)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_name` (`supplier_name`),
  KEY `idx_supplier_type` (`supplier_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商信息表';

-- 供应商商品表
DROP TABLE IF EXISTS `sys_supplier_product`;
CREATE TABLE `sys_supplier_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
  `product_code` varchar(50) NOT NULL COMMENT '商品编码',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_category` varchar(50) DEFAULT NULL COMMENT '商品分类',
  `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
  `model` varchar(50) DEFAULT NULL COMMENT '型号',
  `specification` varchar(255) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `min_order_qty` int(11) DEFAULT '1' COMMENT '最小订购量',
  `delivery_days` int(11) DEFAULT '0' COMMENT '交货天数',
  `warranty_period` int(11) DEFAULT '0' COMMENT '保修期(月)',
  `quality_standard` varchar(100) DEFAULT NULL COMMENT '质量标准',
  `status` char(1) DEFAULT '1' COMMENT '状态(0:停用,1:正常)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_product` (`supplier_id`,`product_code`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_product_category` (`product_category`),
  CONSTRAINT `fk_supplier_product_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `sys_supplier` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商商品表';

-- 插入测试数据
INSERT INTO `sys_supplier` VALUES 
(1, 'SUP001', '北京科技有限公司', 'MANUFACTURER', '张经理', '13800138001', '<EMAIL>', '北京市海淀区中关村大街1号', '91110000123456789A', '110000123456789', '中国银行北京分行', '1234567890123456', 'A', 5, '月结30天', 'FOB北京', 'ISO9001,ISO14001', '1', '优质供应商', 'admin', NOW(), 'admin', NOW()),
(2, 'SUP002', '上海电子设备公司', 'DISTRIBUTOR', '李总监', '13900139002', '<EMAIL>', '上海市浦东新区张江高科技园区', '91310000234567890B', '310000234567890', '工商银行上海分行', '2345678901234567', 'B', 3, '现金', 'CIF上海', 'CE,FCC', '1', '电子设备专业供应商', 'admin', NOW(), 'admin', NOW()),
(3, 'SUP003', '广州办公用品批发中心', 'DISTRIBUTOR', '王主任', '13700137003', '<EMAIL>', '广州市天河区珠江新城', '91440000345678901C', '440000345678901', '建设银行广州分行', '3456789012345678', 'A', 8, '月结15天', 'EXW广州', 'ISO9001', '1', '办公用品一站式供应', 'admin', NOW(), 'admin', NOW());

INSERT INTO `sys_supplier_product` VALUES 
(1, 1, 'PROD001', '联想ThinkPad笔记本电脑', 'IT设备', '联想', 'T14', '14英寸/i5/8G/256G SSD', '台', 5999.00, 1, 7, 36, '国家3C认证', '1', '商务笔记本', 'admin', NOW(), 'admin', NOW()),
(2, 1, 'PROD002', '戴尔显示器', 'IT设备', '戴尔', 'U2419H', '24英寸/1920x1080/IPS', '台', 1299.00, 1, 5, 36, 'CE认证', '1', '专业显示器', 'admin', NOW(), 'admin', NOW()),
(3, 2, 'PROD003', '激光打印机', 'IT设备', '惠普', 'LaserJet Pro M404n', 'A4黑白激光/网络打印', '台', 1899.00, 1, 3, 12, '国家3C认证', '1', '办公打印机', 'admin', NOW(), 'admin', NOW()),
(4, 3, 'PROD004', 'A4复印纸', '办公用品', '得力', 'DL-A4-70G', '70g/500张/包', '包', 25.00, 10, 1, 0, '环保认证', '1', '办公用纸', 'admin', NOW(), 'admin', NOW()),
(5, 3, 'PROD005', '签字笔', '办公用品', '晨光', 'MG-666', '0.5mm黑色', '支', 2.50, 50, 1, 0, '无毒认证', '1', '办公文具', 'admin', NOW(), 'admin', NOW());