-- 岗位级别工资表结构SQL
-- 创建岗位级别工资表，用于记录每个岗位的不同级别对应的工资

-- 创建岗位级别工资表
DROP TABLE IF EXISTS `sys_post_level`;
CREATE TABLE `sys_post_level` (
  `level_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '级别ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `level_code` varchar(50) NOT NULL COMMENT '级别编码',
  `level_name` varchar(100) NOT NULL COMMENT '级别名称',
  `level_order` int(11) NOT NULL DEFAULT '1' COMMENT '级别排序(数字越小级别越高)',
  `base_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '基础工资',
  `performance_salary` decimal(10,2) DEFAULT '0.00' COMMENT '绩效工资',
  `allowance` decimal(10,2) DEFAULT '0.00' COMMENT '津贴补助',
  `total_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总工资',
  `min_experience` int(11) DEFAULT '0' COMMENT '最低工作经验要求(月)',
  `max_experience` int(11) DEFAULT NULL COMMENT '最高工作经验要求(月)',
  `skill_requirements` text COMMENT '技能要求',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`level_id`),
  UNIQUE KEY `uk_post_level` (`post_id`, `level_code`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_level_order` (`level_order`),
  CONSTRAINT `fk_post_level_post` FOREIGN KEY (`post_id`) REFERENCES `sys_post` (`post_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位级别工资表';

-- 插入测试数据
-- 假设岗位ID为1的是"软件工程师"
INSERT INTO `sys_post_level` VALUES 
(1, 1, 'JUNIOR', '初级工程师', 1, 8000.00, 2000.00, 500.00, 10500.00, 0, 24, 'Java基础,Spring框架', '0', 'admin', NOW(), 'admin', NOW(), '初级软件工程师'),
(2, 1, 'MIDDLE', '中级工程师', 2, 12000.00, 3000.00, 800.00, 15800.00, 24, 60, 'Java高级,微服务,数据库设计', '0', 'admin', NOW(), 'admin', NOW(), '中级软件工程师'),
(3, 1, 'SENIOR', '高级工程师', 3, 18000.00, 5000.00, 1200.00, 24200.00, 60, 120, '架构设计,团队管理,技术选型', '0', 'admin', NOW(), 'admin', NOW(), '高级软件工程师'),
(4, 1, 'EXPERT', '专家工程师', 4, 25000.00, 8000.00, 2000.00, 35000.00, 120, NULL, '技术专家,架构师,技术决策', '0', 'admin', NOW(), 'admin', NOW(), '专家级工程师'),
(5, 1, 'PRINCIPAL', '首席工程师', 5, 35000.00, 12000.00, 3000.00, 50000.00, 180, NULL, '技术领导,战略规划,创新研发', '0', 'admin', NOW(), 'admin', NOW(), '首席工程师');

-- 假设岗位ID为2的是"产品经理"
INSERT INTO `sys_post_level` VALUES 
(6, 2, 'JUNIOR', '初级产品经理', 1, 10000.00, 2500.00, 600.00, 13100.00, 0, 24, '产品设计,需求分析', '0', 'admin', NOW(), 'admin', NOW(), '初级产品经理'),
(7, 2, 'MIDDLE', '中级产品经理', 2, 15000.00, 4000.00, 1000.00, 20000.00, 24, 60, '产品规划,用户研究,数据分析', '0', 'admin', NOW(), 'admin', NOW(), '中级产品经理'),
(8, 2, 'SENIOR', '高级产品经理', 3, 22000.00, 6000.00, 1500.00, 29500.00, 60, 120, '产品战略,团队协作,商业分析', '0', 'admin', NOW(), 'admin', NOW(), '高级产品经理'),
(9, 2, 'EXPERT', '资深产品经理', 4, 30000.00, 10000.00, 2500.00, 42500.00, 120, NULL, '产品专家,业务洞察,创新能力', '0', 'admin', NOW(), 'admin', NOW(), '资深产品经理');

-- 查看表结构
DESC sys_post_level;

-- 查看测试数据
SELECT p.post_name, pl.level_name, pl.total_salary 
FROM sys_post p 
LEFT JOIN sys_post_level pl ON p.post_id = pl.post_id 
ORDER BY p.post_id, pl.level_order;