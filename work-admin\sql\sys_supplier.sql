-- ----------------------------
-- 供应商信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_supplier`;
CREATE TABLE `sys_supplier` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '供应商ID',
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(200) NOT NULL COMMENT '供应商名称',
  `supplier_short_name` varchar(100) DEFAULT NULL COMMENT '供应商简称',
  `supplier_type` varchar(20) NOT NULL COMMENT '供应商类型（MANUFACTURER=生产商,DISTRIBUTOR=经销商,SERVICE=服务商,TRADER=贸易商）',
  `supplier_level` varchar(10) DEFAULT NULL COMMENT '供应商等级（A=A级,B=B级,C=C级,D=D级）',
  `main_business` text COMMENT '主营业务',
  
  -- 联系信息
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `mobile_phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `fax_number` varchar(20) DEFAULT NULL COMMENT '传真号码',
  
  -- 地址信息
  `address` varchar(500) DEFAULT NULL COMMENT '详细地址',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
  
  -- 企业信息
  `business_license` varchar(100) DEFAULT NULL COMMENT '营业执照号',
  `tax_number` varchar(100) DEFAULT NULL COMMENT '税号',
  `registered_capital` decimal(15,2) DEFAULT NULL COMMENT '注册资本',
  `establish_date` date DEFAULT NULL COMMENT '成立日期',
  
  -- 财务信息
  `bank_account` varchar(100) DEFAULT NULL COMMENT '银行账号',
  `bank_name` varchar(200) DEFAULT NULL COMMENT '开户银行',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '信用等级（AAA=AAA级,AA=AA级,A=A级,BBB=BBB级,BB=BB级,B=B级,CCC=CCC级,CC=CC级,C=C级,D=D级）',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '付款方式（CASH=现金,TRANSFER=转账,CHECK=支票,CREDIT=赊账）',
  `payment_cycle` int(11) DEFAULT NULL COMMENT '付款周期（天）',
  `invoice_type` varchar(20) DEFAULT NULL COMMENT '发票类型（NORMAL=普通发票,SPECIAL=专用发票,ELECTRONIC=电子发票）',
  
  -- 合作信息
  `cooperation_start_date` date DEFAULT NULL COMMENT '合作开始日期',
  `cooperation_years` int(11) DEFAULT 0 COMMENT '合作年限',
  `total_amount` decimal(15,2) DEFAULT 0.00 COMMENT '累计采购金额',
  `yearly_amount` decimal(15,2) DEFAULT 0.00 COMMENT '年度采购金额',
  
  -- 认证信息
  `quality_certification` varchar(200) DEFAULT NULL COMMENT '质量认证',
  `environment_certification` varchar(200) DEFAULT NULL COMMENT '环保认证',
  
  -- 状态和基础字段
  `status` tinyint(1) DEFAULT 1 COMMENT '状态（1=正常,2=暂停,3=黑名单,4=待审核）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_name` (`supplier_name`),
  KEY `idx_supplier_type` (`supplier_type`),
  KEY `idx_supplier_level` (`supplier_level`),
  KEY `idx_credit_rating` (`credit_rating`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='供应商信息表';

-- ----------------------------
-- 初始化数据
-- ----------------------------
INSERT INTO `sys_supplier` VALUES 
(1, 'SUP000001', '北京科技有限公司', '北京科技', 'MANUFACTURER', 'A', '电子产品研发生产', '张经理', '010-12345678', '13800138001', '<EMAIL>', '010-12345679', '北京市海淀区中关村大街1号', '北京市', '北京市', '海淀区', '100080', '91110000123456789X', '110000123456789', 5000.00, '2010-01-15', '6222021234567890123', '中国银行北京分行', 'AA', 'TRANSFER', 30, 'SPECIAL', '2020-01-01', 4, 1500000.00, 500000.00, 'ISO9001', 'ISO14001', 1, NOW(), NOW(), 'admin', 'admin', '优质供应商'),

(2, 'SUP000002', '上海贸易股份有限公司', '上海贸易', 'DISTRIBUTOR', 'B', '电子元器件批发零售', '李总', '021-87654321', '13900139002', '<EMAIL>', '021-87654322', '上海市浦东新区陆家嘴环路1000号', '上海市', '上海市', '浦东新区', '200120', '91310000987654321Y', '310000987654321', 2000.00, '2015-03-20', '6222021234567890456', '工商银行上海分行', 'A', 'CREDIT', 45, 'NORMAL', '2021-06-01', 3, 800000.00, 300000.00, 'ISO9001', '', 1, NOW(), NOW(), 'admin', 'admin', '长期合作伙伴'),

(3, 'SUP000003', '深圳服务有限公司', '深圳服务', 'SERVICE', 'A', '技术服务与维护', '王工', '0755-11223344', '13700137003', '<EMAIL>', '0755-11223345', '深圳市南山区科技园南区R2-A栋', '广东省', '深圳市', '南山区', '518057', '91***************Z', '***************', 1000.00, '2018-08-10', '6222021234567890789', '招商银行深圳分行', 'AAA', 'TRANSFER', 15, 'ELECTRONIC', '2022-01-01', 2, 600000.00, 400000.00, 'ISO9001', 'ISO14001', 1, NOW(), NOW(), 'admin', 'admin', '专业服务商'),

(4, 'SUP000004', '广州贸易公司', '广州贸易', 'TRADER', 'C', '进出口贸易', '陈总监', '020-99887766', '13600136004', '<EMAIL>', '020-99887767', '广州市天河区珠江新城花城大道100号', '广东省', '广州市', '天河区', '510623', '91***************W', '***************', 3000.00, '2012-12-05', '6222021234567890012', '建设银行广州分行', 'B', 'CHECK', 60, 'NORMAL', '2023-03-01', 1, 200000.00, 150000.00, '', '', 2, NOW(), NOW(), 'admin', 'admin', '暂停合作中');

-- ----------------------------
-- 删除旧的供应商产品表（如果存在）
-- ----------------------------
DROP TABLE IF EXISTS `supplier_product`;
