# 企业综合管理系统技术方案

## 1. 项目概述

### 1.1 项目背景
基于现有若依框架，构建一体化企业管理系统，包含采购报销、人事考勤、工资计算、日程安排、财务收支等核心业务模块。

### 1.2 技术栈
- **后端框架**: Spring Boot 3.0.6 + MyBatis-Plus
- **前端框架**: Vue.js 2.x + Ant Design Vue
- **数据库**: MySQL 8.0+
- **配置中心**: Nacos (配置组: cl-parent-dev)
- **构建工具**: Maven
- **文件存储**: MinIO/阿里云OSS
- **消息队列**: RabbitMQ
- **缓存**: Redis
- **权限管理**: Spring Security + JWT
- **API文档**: Swagger3
- **定时任务**: Quartz
- **第三方集成**: 
  - 发票验真API (税务局接口)
  - OCR文字识别 (百度AI/腾讯云)
  - 微信企业号API
  - 邮件服务

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序    │    │   Web管理端     │    │   移动端H5      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Spring Boot    │────│  Nacos Config   │
│   (统一入口)    │    │   (业务处理)    │    │   (配置中心)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                  │
         ┌────────────────────────┼────────────────────────┐
         │                        │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │     Redis       │    │    RabbitMQ     │
│   (主数据库)    │    │    (缓存)       │    │   (消息队列)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块架构
```
enterprise-system/
├── common/           # 公共模块
├── procurement/      # 采购报销模块
├── hr/              # 人事管理模块
├── payroll/         # 工资计算模块
├── schedule/        # 日程安排模块
├── finance/         # 财务收支模块
├── workflow/        # 工作流引擎
├── integration/     # 第三方集成
└── gateway/         # API网关
```

## 3. 数据库设计

### 3.1 采购报销系统表结构

```sql
-- 采购申请表
CREATE TABLE `procurement_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `title` varchar(200) NOT NULL COMMENT '申请标题',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `dept_id` bigint(20) NOT NULL COMMENT '申请部门ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '申请总金额',
  `urgency_level` tinyint(2) DEFAULT 1 COMMENT '紧急程度 1-普通 2-紧急 3-特急',
  `reason` text COMMENT '申请理由',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-待审批 2-审批中 3-已通过 4-已拒绝 5-已采购 6-已报销',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`)
) COMMENT='采购申请表';

-- 采购明细表
CREATE TABLE `procurement_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` bigint(20) NOT NULL COMMENT '申请ID',
  `item_name` varchar(200) NOT NULL COMMENT '物品名称',
  `spec` varchar(500) COMMENT '规格型号',
  `unit` varchar(20) COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) COMMENT '单价',
  `total_price` decimal(10,2) COMMENT '总价',
  `purchase_url` varchar(1000) COMMENT '采购链接',
  `screenshot_url` varchar(500) COMMENT '截图附件',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`)
) COMMENT='采购明细表';

-- 审批流程表
CREATE TABLE `approval_process` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `process_name` varchar(100) NOT NULL COMMENT '流程名称',
  `current_step` int(5) DEFAULT 1 COMMENT '当前步骤',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-进行中 2-已完成 3-已终止',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_id`, `business_type`)
) COMMENT='审批流程表';

-- 审批步骤表
CREATE TABLE `approval_step` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `process_id` bigint(20) NOT NULL COMMENT '流程ID',
  `step_no` int(5) NOT NULL COMMENT '步骤序号',
  `step_name` varchar(100) NOT NULL COMMENT '步骤名称',
  `approver_id` bigint(20) COMMENT '审批人ID',
  `approver_type` varchar(20) COMMENT '审批人类型 user-用户 role-角色 dept-部门',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-待审批 2-已通过 3-已拒绝',
  `opinion` text COMMENT '审批意见',
  `approve_time` datetime COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_process_id` (`process_id`)
) COMMENT='审批步骤表';

-- 报销单表
CREATE TABLE `reimbursement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reimb_no` varchar(50) NOT NULL COMMENT '报销单号',
  `app_id` bigint(20) COMMENT '关联申请ID',
  `applicant_id` bigint(20) NOT NULL COMMENT '报销人ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '报销总金额',
  `invoice_count` int(5) DEFAULT 0 COMMENT '发票张数',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_reimb_no` (`reimb_no`)
) COMMENT='报销单表';

-- 发票信息表
CREATE TABLE `invoice_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reimb_id` bigint(20) NOT NULL COMMENT '报销单ID',
  `invoice_no` varchar(50) COMMENT '发票号码',
  `invoice_code` varchar(50) COMMENT '发票代码',
  `amount` decimal(10,2) NOT NULL COMMENT '发票金额',
  `tax_amount` decimal(10,2) COMMENT '税额',
  `seller_name` varchar(200) COMMENT '销售方名称',
  `invoice_date` date COMMENT '开票日期',
  `file_url` varchar(500) COMMENT '发票附件',
  `verify_status` tinyint(2) DEFAULT 0 COMMENT '验证状态 0-未验证 1-验证通过 2-验证失败',
  `verify_result` text COMMENT '验证结果',
  PRIMARY KEY (`id`),
  KEY `idx_reimb_id` (`reimb_id`)
) COMMENT='发票信息表';
```

### 3.2 人事考勤系统表结构

```sql
-- 员工信息表 (扩展现有sys_user表)
ALTER TABLE `sys_user` ADD COLUMN `employee_no` varchar(20) COMMENT '工号';
ALTER TABLE `sys_user` ADD COLUMN `hire_date` date COMMENT '入职日期';
ALTER TABLE `sys_user` ADD COLUMN `position_level` varchar(20) COMMENT '职级';
ALTER TABLE `sys_user` ADD COLUMN `work_location` varchar(100) COMMENT '工作地点';
ALTER TABLE `sys_user` ADD COLUMN `direct_supervisor` bigint(20) COMMENT '直属上级';
ALTER TABLE `sys_user` ADD COLUMN `salary_base` decimal(10,2) COMMENT '基本工资';
ALTER TABLE `sys_user` ADD COLUMN `social_security_no` varchar(50) COMMENT '社保号';

-- 假期类型表
CREATE TABLE `leave_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(50) NOT NULL COMMENT '假期名称',
  `type_code` varchar(20) NOT NULL COMMENT '假期代码',
  `max_days` decimal(5,1) COMMENT '最大天数',
  `is_paid` tinyint(1) DEFAULT 1 COMMENT '是否带薪',
  `need_proof` tinyint(1) DEFAULT 0 COMMENT '是否需要证明',
  `advance_days` int(5) DEFAULT 0 COMMENT '需提前申请天数',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`type_code`)
) COMMENT='假期类型表';

-- 请假申请表
CREATE TABLE `leave_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `user_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `leave_type_id` bigint(20) NOT NULL COMMENT '假期类型ID',
  `start_date` datetime NOT NULL COMMENT '开始时间',
  `end_date` datetime NOT NULL COMMENT '结束时间',
  `leave_days` decimal(5,1) NOT NULL COMMENT '请假天数',
  `reason` text COMMENT '请假原因',
  `proof_file` varchar(500) COMMENT '证明附件',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`)
) COMMENT='请假申请表';

-- 考勤记录表 (已存在，需扩展)
ALTER TABLE `sys_attendance` ADD COLUMN `leave_app_id` bigint(20) COMMENT '关联请假申请ID';
ALTER TABLE `sys_attendance` ADD COLUMN `overtime_hours` decimal(4,1) DEFAULT 0 COMMENT '加班小时数';
ALTER TABLE `sys_attendance` ADD COLUMN `location` varchar(200) COMMENT '打卡地点';
ALTER TABLE `sys_attendance` ADD COLUMN `device_info` varchar(200) COMMENT '设备信息';
ALTER TABLE `sys_attendance` ADD COLUMN `photo_url` varchar(500) COMMENT '打卡照片';

-- 加班申请表
CREATE TABLE `overtime_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `user_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `overtime_date` date NOT NULL COMMENT '加班日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `overtime_hours` decimal(4,1) NOT NULL COMMENT '加班小时数',
  `reason` text COMMENT '加班原因',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`)
) COMMENT='加班申请表';
```

### 3.3 工资计算系统表结构

```sql
-- 工资项目表
CREATE TABLE `salary_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(50) NOT NULL COMMENT '工资项目名称',
  `item_code` varchar(20) NOT NULL COMMENT '项目代码',
  `item_type` tinyint(2) NOT NULL COMMENT '项目类型 1-固定 2-浮动 3-扣除',
  `calculation_rule` text COMMENT '计算规则',
  `is_taxable` tinyint(1) DEFAULT 1 COMMENT '是否计税',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_code` (`item_code`)
) COMMENT='工资项目表';

-- 岗位工资标准表
CREATE TABLE `position_salary_standard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `position_level` varchar(20) NOT NULL COMMENT '职级',
  `dept_id` bigint(20) COMMENT '部门ID',
  `base_salary` decimal(10,2) NOT NULL COMMENT '基本工资',
  `performance_coeff` decimal(5,3) DEFAULT 1.000 COMMENT '绩效系数',
  `allowance_amount` decimal(10,2) DEFAULT 0 COMMENT '补贴金额',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`)
) COMMENT='岗位工资标准表';

-- 工资计算表
CREATE TABLE `salary_calculation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `calc_no` varchar(50) NOT NULL COMMENT '计算单号',
  `user_id` bigint(20) NOT NULL COMMENT '员工ID',
  `salary_month` varchar(7) NOT NULL COMMENT '工资月份 YYYY-MM',
  `work_days` decimal(5,1) NOT NULL COMMENT '应出勤天数',
  `actual_days` decimal(5,1) NOT NULL COMMENT '实际出勤天数',
  `leave_days` decimal(5,1) DEFAULT 0 COMMENT '请假天数',
  `overtime_hours` decimal(6,1) DEFAULT 0 COMMENT '加班小时数',
  `base_salary` decimal(10,2) NOT NULL COMMENT '基本工资',
  `overtime_pay` decimal(10,2) DEFAULT 0 COMMENT '加班费',
  `allowance` decimal(10,2) DEFAULT 0 COMMENT '补贴',
  `deduction` decimal(10,2) DEFAULT 0 COMMENT '扣除',
  `gross_salary` decimal(10,2) NOT NULL COMMENT '税前工资',
  `tax_amount` decimal(10,2) DEFAULT 0 COMMENT '个人所得税',
  `social_security` decimal(10,2) DEFAULT 0 COMMENT '社保扣除',
  `net_salary` decimal(10,2) NOT NULL COMMENT '实发工资',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-草稿 2-已确认 3-已发放',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_calc_no` (`calc_no`),
  KEY `idx_user_month` (`user_id`, `salary_month`)
) COMMENT='工资计算表';

-- 工资明细表
CREATE TABLE `salary_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `calc_id` bigint(20) NOT NULL COMMENT '工资计算ID',
  `item_id` bigint(20) NOT NULL COMMENT '工资项目ID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `remark` varchar(200) COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_calc_id` (`calc_id`)
) COMMENT='工资明细表';
```

### 3.4 日程安排系统表结构

```sql
-- 排班模板表
CREATE TABLE `schedule_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型',
  `work_start_time` time COMMENT '上班时间',
  `work_end_time` time COMMENT '下班时间',
  `break_duration` int(5) DEFAULT 60 COMMENT '休息时长(分钟)',
  `work_days` varchar(20) COMMENT '工作日设置',
  `dept_id` bigint(20) COMMENT '适用部门',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`)
) COMMENT='排班模板表';

-- 员工排班表
CREATE TABLE `employee_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '员工ID',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `template_id` bigint(20) COMMENT '模板ID',
  `shift_type` varchar(20) COMMENT '班次类型',
  `work_start_time` time COMMENT '上班时间',
  `work_end_time` time COMMENT '下班时间',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态 1-正常 2-调班 3-请假',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `schedule_date`)
) COMMENT='员工排班表';

-- 公章使用申请表
CREATE TABLE `seal_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `seal_type` varchar(20) NOT NULL COMMENT '印章类型',
  `usage_purpose` varchar(200) NOT NULL COMMENT '使用目的',
  `document_count` int(5) DEFAULT 1 COMMENT '用印份数',
  `urgency_level` tinyint(2) DEFAULT 1 COMMENT '紧急程度',
  `expected_time` datetime COMMENT '期望用印时间',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `keeper_id` bigint(20) COMMENT '印章保管员ID',
  `actual_time` datetime COMMENT '实际用印时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`)
) COMMENT='公章使用申请表';

-- 外出申请表
CREATE TABLE `outing_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_no` varchar(50) NOT NULL COMMENT '申请单号',
  `applicant_id` bigint(20) NOT NULL COMMENT '申请人ID',
  `outing_type` varchar(20) NOT NULL COMMENT '外出类型',
  `destination` varchar(200) NOT NULL COMMENT '目的地',
  `purpose` varchar(200) NOT NULL COMMENT '外出目的',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_no` (`app_no`)
) COMMENT='外出申请表';
```

### 3.5 财务收支系统表结构

```sql
-- 财务科目表
CREATE TABLE `finance_subject` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `subject_code` varchar(20) NOT NULL COMMENT '科目代码',
  `subject_name` varchar(100) NOT NULL COMMENT '科目名称',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父级科目ID',
  `subject_type` tinyint(2) NOT NULL COMMENT '科目类型 1-收入 2-支出',
  `level` int(5) DEFAULT 1 COMMENT '科目级别',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_subject_code` (`subject_code`)
) COMMENT='财务科目表';

-- 收支记录表
CREATE TABLE `finance_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_no` varchar(50) NOT NULL COMMENT '记录编号',
  `record_type` tinyint(2) NOT NULL COMMENT '记录类型 1-收入 2-支出',
  `subject_id` bigint(20) NOT NULL COMMENT '财务科目ID',
  `amount` decimal(12,2) NOT NULL COMMENT '金额',
  `record_date` date NOT NULL COMMENT '记录日期',
  `description` varchar(500) COMMENT '描述',
  `business_type` varchar(50) COMMENT '业务类型',
  `business_id` bigint(20) COMMENT '业务关联ID',
  `dept_id` bigint(20) COMMENT '部门ID',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `attachment_url` varchar(500) COMMENT '附件',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_no` (`record_no`),
  KEY `idx_date_type` (`record_date`, `record_type`)
) COMMENT='收支记录表';

-- 预算管理表
CREATE TABLE `budget_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `budget_year` int(10) NOT NULL COMMENT '预算年度',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `subject_id` bigint(20) NOT NULL COMMENT '科目ID',
  `budget_amount` decimal(12,2) NOT NULL COMMENT '预算金额',
  `used_amount` decimal(12,2) DEFAULT 0 COMMENT '已使用金额',
  `remaining_amount` decimal(12,2) COMMENT '剩余金额',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_budget` (`budget_year`, `dept_id`, `subject_id`)
) COMMENT='预算管理表';
```

## 4. 详细功能设计

### 4.1 采购报销系统

#### 4.1.1 核心功能
- **申请流程**: 申请 → 组长审批 → 预算审批 → 领导审批 → 采购执行 → 报销结算
- **智能识别**: 支持上传商品链接截图，OCR自动识别商品信息
- **批量操作**: 支持批量导出采购链接，生成采购清单
- **快捷报销**: 支持PDF发票批量识别，自动验证发票真伪
- **数据对账**: 自动与财务系统对账，生成支出报表

#### 4.1.2 技术实现
```java
@Service
public class ProcurementService {
    
    @Autowired
    private OcrService ocrService;
    
    @Autowired
    private InvoiceVerifyService invoiceVerifyService;
    
    /**
     * 智能识别商品信息
     */
    public ProcurementItem recognizeProductInfo(MultipartFile screenshot) {
        // OCR识别截图中的商品信息
        OcrResult ocrResult = ocrService.recognizeText(screenshot);
        
        // 智能解析商品名称、价格、规格等
        return parseProductInfo(ocrResult.getText());
    }
    
    /**
     * 发票验真
     */
    public InvoiceVerifyResult verifyInvoice(InvoiceInfo invoice) {
        // 调用税务局API验证发票
        return invoiceVerifyService.verify(
            invoice.getInvoiceCode(), 
            invoice.getInvoiceNo(), 
            invoice.getAmount()
        );
    }
}
```

### 4.2 人事考勤系统

#### 4.2.1 核心功能
- **多元化考勤**: 支持人脸识别、OCR文字识别、GPS定位等多种考勤方式
- **假期管理**: 自主设置各类假期类型（事假、病假、年假等）
- **人事档案**: 完整的员工信息管理，支持不同岗位级别配置
- **移动端集成**: 微信小程序查看个人信息、工资条，移动端信息修改
- **智能汇总**: 自动汇总考勤数据，支持多维度查询统计

#### 4.2.2 技术实现
```java
@RestController
@RequestMapping("/hr")
public class HRController {
    
    @Autowired
    private AttendanceService attendanceService;
    
    @Autowired
    private FaceRecognitionService faceService;
    
    /**
     * 人脸识别打卡
     */
    @PostMapping("/attendance/face-checkin")
    public AjaxResult faceCheckIn(@RequestParam MultipartFile facePhoto) {
        // 人脸识别验证
        FaceResult result = faceService.recognize(facePhoto);
        if (result.isSuccess()) {
            // 记录考勤
            attendanceService.checkIn(result.getUserId(), "FACE", result.getLocation());
            return AjaxResult.success("打卡成功");
        }
        return AjaxResult.error("人脸识别失败");
    }
    
    /**
     * 微信小程序接口 - 获取个人信息
     */
    @GetMapping("/wechat/user-info")
    public AjaxResult getWechatUserInfo(@RequestParam String openId) {
        SysUser user = userService.getByOpenId(openId);
        UserInfoVO userInfo = new UserInfoVO();
        // 数据脱敏处理
        BeanUtils.copyProperties(user, userInfo);
        return AjaxResult.success(userInfo);
    }
}
```

### 4.3 工资计算系统

#### 4.3.1 核心功能
- **自动计算**: 根据考勤数据自动计算基本工资、加班费、津贴等
- **灵活配置**: 支持自定义工资项目和计算规则
- **岗位匹配**: 根据员工岗位级别自动匹配工资标准
- **批量处理**: 支持批量生成工资单，批量发送邮件
- **数据导出**: 支持多种格式导出，满足财务需求

#### 4.3.2 技术实现
```java
@Service
public class SalaryCalculationService {
    
    /**
     * 批量计算月工资
     */
    @Transactional
    public void calculateMonthlySalary(String salaryMonth, List<Long> userIds) {
        for (Long userId : userIds) {
            SysUser user = userService.getById(userId);
            
            // 获取考勤数据
            AttendanceStatistics stats = attendanceService.getMonthlyStats(userId, salaryMonth);
            
            // 获取工资标准
            PositionSalaryStandard standard = getPositionStandard(user.getPositionLevel(), user.getDeptId());
            
            // 计算工资
            SalaryCalculation calculation = new SalaryCalculation();
            calculation.setUserId(userId);
            calculation.setSalaryMonth(salaryMonth);
            calculation.setWorkDays(stats.getWorkDays());
            calculation.setActualDays(stats.getActualDays());
            calculation.setOvertimeHours(stats.getOvertimeHours());
            
            // 基本工资计算
            BigDecimal baseSalary = standard.getBaseSalary()
                .multiply(new BigDecimal(stats.getActualDays()))
                .divide(new BigDecimal(stats.getWorkDays()), 2, RoundingMode.HALF_UP);
            
            // 加班费计算
            BigDecimal overtimePay = calculateOvertimePay(standard.getBaseSalary(), stats.getOvertimeHours());
            
            calculation.setBaseSalary(baseSalary);
            calculation.setOvertimePay(overtimePay);
            calculation.setGrossSalary(baseSalary.add(overtimePay));
            
            // 计算税费
            BigDecimal taxAmount = calculateTax(calculation.getGrossSalary());
            calculation.setTaxAmount(taxAmount);
            calculation.setNetSalary(calculation.getGrossSalary().subtract(taxAmount));
            
            salaryCalculationMapper.insert(calculation);
        }
    }
}
```

### 4.4 日程安排系统

#### 4.4.1 核心功能
- **排班看板**: 可视化日程安排，支持拖拽式排班
- **公章管理**: 公章使用申请、状态跟踪、使用记录
- **外出管理**: 外出申请审批、实时状态更新
- **智能提醒**: 自动提醒相关人员待办事项

#### 4.4.2 技术实现
```java
@RestController
@RequestMapping("/schedule")
public class ScheduleController {
    
    /**
     * 获取排班看板数据
     */
    @GetMapping("/dashboard")
    public AjaxResult getDashboard(@RequestParam String date) {
        ScheduleDashboardVO dashboard = new ScheduleDashboardVO();
        
        // 获取当日排班信息
        List<EmployeeSchedule> schedules = scheduleService.getDaySchedules(date);
        dashboard.setSchedules(schedules);
        
        // 获取公章状态
        List<SealApplicationVO> sealApps = sealService.getTodayApplications();
        dashboard.setSealApplications(sealApps);
        
        // 获取外出状态
        List<OutingApplicationVO> outingApps = outingService.getTodayApplications();
        dashboard.setOutingApplications(outingApps);
        
        return AjaxResult.success(dashboard);
    }
}
```

### 4.5 财务收支系统

#### 4.5.1 核心功能
- **自动收集**: 自动汇总采购报销、工资发放等各类支出数据
- **多维分析**: 按部门、科目、时间等多维度统计分析
- **预算管控**: 预算设置、使用监控、超预算预警
- **财务报表**: 自动生成各类财务报表和看板

## 5. 开发计划

### 5.1 第一阶段（1-2个月）- 基础功能
- [x] 现有考勤系统优化
- [ ] 采购申请流程开发
- [ ] 基础审批流程引擎
- [ ] 人事信息管理完善
- [ ] 工资项目配置功能

### 5.2 第二阶段（2-3个月）- 核心功能
- [ ] OCR文字识别集成
- [ ] 发票验真功能
- [ ] 人脸识别考勤
- [ ] 工资自动计算
- [ ] 微信小程序开发

### 5.3 第三阶段（1-2个月）- 高级功能
- [ ] 智能报销功能
- [ ] 财务数据自动对账
- [ ] 多维度数据看板
- [ ] 移动端完善
- [ ] 系统性能优化

### 5.4 第四阶段（1个月）- 部署上线
- [ ] 系统集成测试
- [ ] 用户培训
- [ ] 数据迁移
- [ ] 生产环境部署
- [ ] 运维监控配置

## 6. 技术难点与解决方案

### 6.1 OCR识别准确性
**难点**: 商品截图识别准确率
**解决方案**: 
- 采用百度AI/腾讯云OCR服务
- 结合自然语言处理优化识别结果
- 建立商品信息纠错机制

### 6.2 发票验真实时性
**难点**: 税务局API调用限制和响应速度
**解决方案**:
- 实现异步验证机制
- 建立发票信息缓存
- 批量验证优化

### 6.3 工作流引擎性能
**难点**: 复杂审批流程的性能问题
**解决方案**:
- 采用状态机模式设计
- Redis缓存优化
- 异步消息处理

### 6.4 微信集成安全性
**难点**: 微信小程序数据安全
**解决方案**:
- OAuth2.0授权机制
- 数据脱敏处理
- API接口加密

## 7. 部署架构

### 7.1 生产环境架构
```
负载均衡器 (Nginx)
    ↓
API网关 (Spring Cloud Gateway)
    ↓
┌─────────────┬─────────────┬─────────────┐
│ 应用服务器1  │ 应用服务器2  │ 应用服务器3  │
│ (Docker)    │ (Docker)    │ (Docker)    │
└─────────────┴─────────────┴─────────────┘
    ↓
┌─────────────┬─────────────┬─────────────┐
│   MySQL     │    Redis    │  RabbitMQ   │
│  (主从)     │   (集群)    │  (集群)     │
└─────────────┴─────────────┴─────────────┘
```

### 7.2 监控体系
- **应用监控**: Spring Boot Actuator + Micrometer
- **日志监控**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **性能监控**: SkyWalking APM
- **业务监控**: 自定义业务指标看板

## 8. 安全方案

### 8.1 数据安全
- 敏感数据加密存储
- 数据传输HTTPS加密
- 定期数据备份
- 访问日志审计

### 8.2 权限控制
- 基于RBAC的权限模型
- 接口级权限控制
- 数据行级权限
- 操作日志记录

### 8.3 系统安全
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 接口频率限制

## 9. 总结

本方案基于现有若依框架，充分利用已有的用户权限、部门管理等基础功能，在此基础上构建完整的企业综合管理系统。通过模块化设计和微服务架构，确保系统的可扩展性和可维护性。同时，通过引入AI技术（OCR、人脸识别）和第三方服务集成，提升用户体验和工作效率。

整个项目预计开发周期6-8个月，投入开发人员6-8人，能够满足中小型企业的综合管理需求，具有良好的商业价值和技术可行性。 