# 发票识别功能说明

## 功能概述

本功能基于腾讯云OCR API实现OFD格式发票的自动识别，支持增值税专用发票、增值税普通发票等多种发票类型的识别。

## 技术架构

- **框架**: 若依(RuoYi)框架
- **OCR服务**: 腾讯云OCR API
- **支持格式**: OFD格式发票文件
- **数据库**: MySQL

## 功能特性

1. **多种识别方式**
   - 文件上传识别
   - URL链接识别
   - Base64编码识别

2. **完整的发票信息提取**
   - 基本信息：发票类型、发票代码、发票号码、开票日期等
   - 购买方信息：名称、纳税人识别号、地址电话、开户行账号
   - 销售方信息：名称、纳税人识别号、地址电话、开户行账号
   - 金额信息：价税合计、税额、不含税金额
   - 人员信息：开票人、收款人、复核人
   - 货物清单：商品名称、规格型号、数量、单价、金额、税率等

3. **数据管理功能**
   - 识别结果查询
   - 识别历史管理
   - 数据导出功能
   - 重新识别功能

## 部署配置

### 1. 数据库配置

执行SQL脚本创建数据表：

```sql
-- 执行 sql/invoice_recognition.sql 文件
```

### 2. 腾讯云配置

在 `application.yml` 中配置腾讯云API密钥：

```yaml
tencent:
  cloud:
    # 腾讯云SecretId（请替换为实际的SecretId）
    secret-id: your-secret-id
    # 腾讯云SecretKey（请替换为实际的SecretKey）
    secret-key: your-secret-key
    # 腾讯云OCR服务地域
    region: ap-beijing
    # OCR服务端点
    endpoint: ocr.tencentcloudapi.com
```

### 3. 依赖配置

项目已自动添加腾讯云SDK依赖：

```xml
<!-- 腾讯云OCR SDK -->
<dependency>
    <groupId>com.tencentcloudapi</groupId>
    <artifactId>tencentcloud-sdk-java</artifactId>
    <version>3.1.1114</version>
</dependency>
```

## API接口说明

### 1. 文件上传识别

**接口地址**: `POST /system/invoice/upload`

**请求参数**:
- `file`: MultipartFile类型，OFD格式的发票文件

**响应示例**:
```json
{
  "code": 200,
  "msg": "发票识别成功",
  "data": {
    "id": 1,
    "invoiceType": "增值税专用发票",
    "invoiceCode": "144031909110",
    "invoiceNumber": "72738177",
    "issueDate": "2019-12-20",
    "totalAmount": 1380.00,
    "buyerName": "名称",
    "sellerName": "销售方名称",
    "status": "1"
  }
}
```

### 2. URL识别

**接口地址**: `POST /system/invoice/recognizeByUrl`

**请求参数**:
- `fileUrl`: String类型，OFD文件的URL地址

### 3. Base64识别

**接口地址**: `POST /system/invoice/recognizeByBase64`

**请求参数**:
- `fileBase64`: String类型，OFD文件的Base64编码
- `fileName`: String类型，文件名（可选）

### 4. 查询识别列表

**接口地址**: `GET /system/invoice/list`

**查询参数**:
- `invoiceNumber`: 发票号码（模糊查询）
- `buyerName`: 购买方名称（模糊查询）
- `sellerName`: 销售方名称（模糊查询）
- `status`: 识别状态（0-识别中，1-识别成功，2-识别失败）

### 5. 获取识别详情

**接口地址**: `GET /system/invoice/{id}`

### 6. 重新识别

**接口地址**: `POST /system/invoice/reRecognize/{id}`

## 权限配置

系统已预设以下权限：

- `system:invoice:list` - 发票识别查询
- `system:invoice:query` - 发票识别详情查询
- `system:invoice:add` - 发票识别新增
- `system:invoice:edit` - 发票识别修改
- `system:invoice:remove` - 发票识别删除
- `system:invoice:export` - 发票识别导出
- `system:invoice:upload` - 发票文件上传
- `system:invoice:recognize` - 发票识别处理

## 文件结构

```
work-admin/
├── ruoyi-common/
│   ├── src/main/java/com/ruoyi/common/
│   │   ├── config/TencentCloudConfig.java          # 腾讯云配置类
│   │   └── utils/ocr/TencentOcrUtils.java          # 腾讯云OCR工具类
├── ruoyi-system/
│   ├── src/main/java/com/ruoyi/system/
│   │   ├── domain/
│   │   │   ├── InvoiceRecognition.java             # 发票识别实体类
│   │   │   └── InvoiceGoodsInfo.java               # 发票商品信息实体类
│   │   ├── mapper/InvoiceRecognitionMapper.java    # 数据访问层接口
│   │   └── service/
│   │       ├── IInvoiceRecognitionService.java     # 业务层接口
│   │       └── impl/InvoiceRecognitionServiceImpl.java # 业务层实现
│   └── src/main/resources/mapper/system/
│       └── InvoiceRecognitionMapper.xml            # MyBatis映射文件
├── ruoyi-admin/
│   └── src/main/java/com/ruoyi/web/controller/system/
│       └── SysInvoiceRecognitionController.java    # 控制层
└── sql/
    └── invoice_recognition.sql                     # 数据库脚本
```

## 使用注意事项

1. **文件格式限制**: 目前仅支持OFD格式的发票文件
2. **文件大小限制**: 建议单个文件不超过10MB
3. **API调用限制**: 请注意腾讯云API的调用频率限制
4. **密钥安全**: 请妥善保管腾讯云API密钥，不要泄露给他人
5. **网络要求**: 需要服务器能够访问腾讯云API服务

## 错误处理

系统会记录识别过程中的错误信息，常见错误类型：

- 文件格式不正确
- 网络连接失败
- API密钥配置错误
- 文件损坏或无法解析
- API调用频率超限

## 扩展说明

如需支持其他格式的发票识别（如PDF、图片等），可以：

1. 在 `TencentOcrUtils` 中添加对应的识别方法
2. 在 `IInvoiceRecognitionService` 中添加新的接口方法
3. 在 `SysInvoiceRecognitionController` 中添加对应的API接口
4. 根据需要调整文件上传验证逻辑

## 技术支持

如有问题，请参考：
- [腾讯云OCR API文档](https://cloud.tencent.com/document/product/866)
- [若依框架官方文档](http://doc.ruoyi.vip/)
