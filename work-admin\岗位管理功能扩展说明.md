# 岗位管理功能扩展说明

## 📋 功能概述

本次扩展为岗位管理系统添加了岗位级别和级别工资管理功能，实现了一个岗位可以设置多个级别，每个级别对应不同的工资结构。

## 🏗️ 数据库设计

### 新增表结构

#### sys_post_level (岗位级别工资表)
```sql
CREATE TABLE `sys_post_level` (
  `level_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '级别ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `level_code` varchar(50) NOT NULL COMMENT '级别编码',
  `level_name` varchar(100) NOT NULL COMMENT '级别名称',
  `level_order` int(11) NOT NULL DEFAULT '1' COMMENT '级别排序',
  `base_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '基础工资',
  `performance_salary` decimal(10,2) DEFAULT '0.00' COMMENT '绩效工资',
  `allowance` decimal(10,2) DEFAULT '0.00' COMMENT '津贴补助',
  `total_salary` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总工资',
  `min_experience` int(11) DEFAULT '0' COMMENT '最低工作经验要求(月)',
  `max_experience` int(11) DEFAULT NULL COMMENT '最高工作经验要求(月)',
  `skill_requirements` text COMMENT '技能要求',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  -- 基础字段
  PRIMARY KEY (`level_id`),
  UNIQUE KEY `uk_post_level` (`post_id`, `level_code`),
  CONSTRAINT `fk_post_level_post` FOREIGN KEY (`post_id`) REFERENCES `sys_post` (`post_id`) ON DELETE CASCADE
);
```

### 表关系
- **一对多关系**: 一个岗位(sys_post)可以有多个级别(sys_post_level)
- **外键约束**: 级别表通过post_id关联岗位表
- **级联删除**: 删除岗位时自动删除对应的所有级别

## 📁 后端文件结构

按照若依框架规范，新增以下文件：

### Domain层
```
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/domain/
├── SysPost.java (修改 - 添加postLevelList属性)
└── SysPostLevel.java (新增)
```

### Mapper层
```
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/mapper/
├── SysPostMapper.java (无需修改)
└── SysPostLevelMapper.java (新增)

work-admin/ruoyi-system/src/main/resources/mapper/system/
├── SysPostMapper.xml (修改 - 移除错误字段)
└── SysPostLevelMapper.xml (新增)
```

### Service层
```
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/service/
├── ISysPostService.java (无需修改)
└── ISysPostLevelService.java (新增)

work-admin/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/
├── SysPostServiceImpl.java (无需修改)
└── SysPostLevelServiceImpl.java (新增)
```

### Controller层
```
work-admin/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/
├── SysPostController.java (无需修改)
└── SysPostLevelController.java (新增)
```

## 🎨 前端功能设计

### 岗位管理页面扩展
1. **级别管理按钮**: 在岗位列表操作列添加"级别管理"按钮
2. **级别管理弹窗**: 点击后打开级别管理弹窗，显示该岗位的所有级别
3. **级别CRUD操作**: 支持添加、修改、删除级别
4. **工资结构**: 每个级别包含基础工资、绩效工资、津贴补助和总工资

### 级别管理功能
- **级别信息**: 级别编码、级别名称、级别排序
- **工资结构**: 基础工资、绩效工资、津贴补助、总工资
- **经验要求**: 最低/最高工作经验要求
- **技能要求**: 该级别所需的技能描述
- **状态管理**: 正常/停用状态

## 🔧 API接口设计

### 岗位级别管理接口
```
GET    /system/post/level/list              - 查询级别列表
GET    /system/post/level/listByPostId/{id} - 根据岗位ID查询级别
GET    /system/post/level/{id}              - 查询级别详情
POST   /system/post/level                   - 新增级别
PUT    /system/post/level                   - 修改级别
DELETE /system/post/level/{ids}             - 删除级别
POST   /system/post/level/batchSave/{id}    - 批量保存级别
```

## 💡 核心特性

### 1. 灵活的级别配置
- 每个岗位可以设置多个级别（初级、中级、高级等）
- 级别可以自定义编码和名称
- 支持级别排序，便于管理

### 2. 完整的工资结构
- **基础工资**: 固定的基本薪资
- **绩效工资**: 根据绩效发放的浮动薪资
- **津贴补助**: 各种补贴和津贴
- **总工资**: 自动计算的总薪资

### 3. 经验和技能要求
- 设置每个级别的最低/最高经验要求
- 描述该级别所需的技能和能力
- 便于HR进行人员配置和晋升管理

### 4. 数据完整性
- 外键约束确保数据一致性
- 级联删除避免孤立数据
- 唯一约束防止重复级别编码

## 🚀 使用场景

### 1. 人力资源管理
- 为不同岗位设置清晰的级别体系
- 制定合理的薪资结构
- 规范员工晋升路径

### 2. 薪资管理
- 根据员工级别自动计算薪资
- 支持复杂的薪资结构
- 便于薪资调整和管理

### 3. 招聘管理
- 明确不同级别的要求
- 为招聘提供薪资参考
- 规范岗位描述

## 📊 数据示例

### 软件工程师岗位级别设置
```
初级工程师: 基础8000 + 绩效2000 + 津贴500 = 10500元
中级工程师: 基础12000 + 绩效3000 + 津贴800 = 15800元
高级工程师: 基础18000 + 绩效5000 + 津贴1200 = 24200元
专家工程师: 基础25000 + 绩效8000 + 津贴2000 = 35000元
首席工程师: 基础35000 + 绩效12000 + 津贴3000 = 50000元
```

## 🔒 权限控制

所有级别管理功能复用岗位管理的权限：
- `system:post:list` - 查询权限
- `system:post:add` - 新增权限
- `system:post:edit` - 修改权限
- `system:post:remove` - 删除权限
- `system:post:export` - 导出权限

## 📝 部署说明

1. **执行SQL脚本**: 运行 `work-admin/sql/sys_post_update.sql`
2. **重启后端服务**: 加载新的Java类和配置
3. **更新前端页面**: 部署新的Vue组件
4. **测试功能**: 验证级别管理功能正常

---

**注意**: 此扩展完全兼容现有的岗位管理功能，不会影响原有数据和功能。