# 考勤系统设计说明

## 功能概述

基于若依框架的考勤管理系统，为企业人员管理添加了完整的考勤功能，包括考勤打卡、记录管理、状态统计等核心功能。

## 系统架构

### 数据库设计

#### 1. 考勤记录表 (sys_attendance)
- `attendance_id`: 考勤ID (主键)
- `user_id`: 用户ID (外键关联sys_user)
- `attendance_date`: 考勤日期
- `check_in_time`: 签到时间
- `check_out_time`: 签退时间
- `work_hours`: 工作时长(小时)
- `attendance_status`: 考勤状态 (0正常 1迟到 2早退 3旷工 4请假)
- `late_minutes`: 迟到分钟数
- `early_minutes`: 早退分钟数
- `remark`: 备注
- 标准审计字段: create_by, create_time, update_by, update_time

#### 2. 考勤规则表 (sys_attendance_rule)
- `rule_id`: 规则ID (主键)
- `rule_name`: 规则名称
- `dept_id`: 部门ID (可空，空表示全公司)
- `work_start_time`: 上班时间
- `work_end_time`: 下班时间
- `late_threshold`: 迟到阈值(分钟)
- `early_threshold`: 早退阈值(分钟)
- `work_days`: 工作日 (1-7表示周一到周日)
- `status`: 状态 (0正常 1停用)

### 后端接口设计

#### 核心功能接口
1. **考勤记录管理**
   - `GET /system/attendance/list` - 查询考勤记录列表
   - `GET /system/attendance/{id}` - 获取考勤记录详情
   - `POST /system/attendance` - 新增考勤记录
   - `PUT /system/attendance` - 修改考勤记录
   - `DELETE /system/attendance/{ids}` - 删除考勤记录
   - `POST /system/attendance/export` - 导出考勤记录

2. **考勤打卡功能**
   - `GET /system/attendance/today` - 获取当前用户今日考勤记录
   - `POST /system/attendance/checkin` - 签到
   - `POST /system/attendance/checkout` - 签退

#### 业务逻辑特点
- **自动计算**: 根据签到签退时间自动计算工作时长
- **状态判断**: 根据考勤规则自动判断迟到、早退状态
- **防重复**: 同一用户同一天不能重复签到或签退
- **数据权限**: 支持按部门数据权限过滤

### 前端界面设计

#### 1. 考勤记录管理页面 (/attendance/record)
**主要功能区域:**
- **考勤打卡区**: 实时显示当前时间，显示今日考勤状态，提供签到签退按钮
- **查询区域**: 支持按用户名、考勤状态、日期范围查询
- **操作区域**: 新增、修改、删除、导出考勤记录
- **数据展示**: 表格展示考勤记录列表，支持分页

**界面特色:**
- 实时时钟显示
- 考勤状态卡片展示（签到时间、签退时间、工作时长）
- 状态标签美观展示（正常、迟到、早退等）
- 响应式设计，适配不同屏幕

#### 2. 用户管理页面增强
- 在用户列表操作栏添加"考勤记录"按钮
- 点击可直接跳转到该用户的考勤记录页面

## 权限控制

### 菜单权限
- `attendance:record:list` - 考勤记录查询
- `attendance:record:add` - 考勤记录新增
- `attendance:record:edit` - 考勤记录修改
- `attendance:record:remove` - 考勤记录删除
- `attendance:record:export` - 考勤记录导出
- `attendance:record:clock` - 考勤打卡

### 数据权限
- 支持按部门范围控制考勤数据访问
- 普通用户只能查看自己的考勤记录
- 管理员可以查看管辖范围内的考勤记录

## 技术特性

### 后端技术栈
- **Spring Boot 3.0.6** - 基础框架
- **MyBatis** - 数据持久层
- **Spring Security** - 安全框架
- **数据权限控制** - 基于注解的数据范围过滤

### 前端技术栈
- **Vue.js 2.x** - 前端框架
- **Ant Design Vue** - UI组件库
- **实时更新** - 定时器更新当前时间
- **表单验证** - 完整的表单验证规则

## 业务流程

### 日常考勤流程
1. **员工签到**: 
   - 点击签到按钮
   - 系统记录签到时间
   - 自动判断是否迟到（默认9:00为准）
   
2. **员工签退**:
   - 点击签退按钮
   - 系统记录签退时间
   - 自动计算工作时长
   - 自动判断是否早退（默认18:00为准）

### 管理员管理流程
1. **考勤记录查询**: 按条件查询员工考勤记录
2. **考勤记录编辑**: 手动调整异常考勤记录
3. **数据导出**: 导出考勤数据用于薪资计算

## 扩展功能建议

### 近期可扩展功能
1. **考勤规则管理**: 不同部门设置不同的上下班时间
2. **请假管理**: 集成请假申请和审批流程
3. **考勤统计**: 月度/年度考勤统计报表
4. **异常考勤处理**: 补签、调休等功能

### 长期规划功能
1. **地理位置打卡**: GPS定位限制打卡范围
2. **人脸识别打卡**: 防止代打卡
3. **移动端APP**: 手机端考勤应用
4. **钉钉/企微集成**: 与企业通讯工具集成

## 部署说明

### 数据库部署
1. 执行 `work-admin/sql/attendance.sql` 创建考勤相关表
2. 插入菜单权限和字典数据

### 应用部署
1. 后端代码已集成到若依系统中，重新编译部署即可
2. 前端代码添加到现有Vue项目中
3. 确保路由配置包含考勤模块

### 权限配置
1. 为管理员角色分配考勤相关权限
2. 为普通用户分配考勤打卡权限
3. 配置数据权限范围

## 注意事项

1. **时间同步**: 确保服务器时间准确，影响考勤计算
2. **数据备份**: 考勤数据关系到薪资计算，需要定期备份
3. **性能优化**: 大量考勤数据需要考虑分表或归档策略
4. **安全考虑**: 防止恶意刷考勤，可考虑添加频率限制

这个考勤系统设计完整，功能实用，集成度高，可以满足中小企业的考勤管理需求。 