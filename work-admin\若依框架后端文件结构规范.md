# 若依框架后端文件结构规范

## 📋 概述

若依框架采用多模块分层架构，每个模块有明确的职责分工。在开发新功能时，必须严格按照以下规范放置文件，确保项目结构清晰、维护性强。

## 🏗️ 模块架构

```
work-admin/
├── ruoyi-admin/          # Web控制层模块
├── ruoyi-system/         # 系统业务模块  
├── ruoyi-common/         # 通用工具模块
├── ruoyi-framework/      # 框架核心模块
├── ruoyi-generator/      # 代码生成模块
└── ruoyi-quartz/         # 定时任务模块
```

## 📁 文件放置规范

### 1. Controller 控制器
**位置**: `ruoyi-admin` 模块
**路径**: `work-admin/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/`

```java
// 示例文件名: SysSupplierController.java
package com.ruoyi.web.controller.system;

@RestController
@RequestMapping("/system/supplier")
public class SysSupplierController extends BaseController {
    // Controller 代码
}
```

**职责**: 
- 处理HTTP请求和响应
- 参数验证和转换
- 调用Service层业务逻辑
- 返回统一格式的响应数据

### 2. Domain 实体类
**位置**: `ruoyi-system` 模块
**路径**: `work-admin/ruoyi-system/src/main/java/com/ruoyi/system/domain/`

```java
// 示例文件名: Supplier.java
package com.ruoyi.system.domain;

public class Supplier extends BaseEntity {
    // 实体属性和方法
}
```

**职责**:
- 定义数据库表对应的实体类
- 包含字段属性、getter/setter方法
- 添加Excel导出注解等

### 3. Service 业务接口
**位置**: `ruoyi-system` 模块
**路径**: `work-admin/ruoyi-system/src/main/java/com/ruoyi/system/service/`

```java
// 示例文件名: ISupplierService.java
package com.ruoyi.system.service;

public interface ISupplierService {
    // 业务方法定义
}
```

**职责**:
- 定义业务逻辑接口
- 声明CRUD和业务相关方法

### 4. Service 业务实现类
**位置**: `ruoyi-system` 模块
**路径**: `work-admin/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/`

```java
// 示例文件名: SupplierServiceImpl.java
package com.ruoyi.system.service.impl;

@Service
public class SupplierServiceImpl implements ISupplierService {
    // 业务逻辑实现
}
```

**职责**:
- 实现业务接口
- 处理复杂业务逻辑
- 调用Mapper进行数据操作

### 5. Mapper 数据访问接口
**位置**: `ruoyi-system` 模块
**路径**: `work-admin/ruoyi-system/src/main/java/com/ruoyi/system/mapper/`

```java
// 示例文件名: SupplierMapper.java
package com.ruoyi.system.mapper;

public interface SupplierMapper {
    // 数据访问方法定义
}
```

**职责**:
- 定义数据库操作方法
- 与MyBatis XML文件对应

### 6. Mapper XML 文件
**位置**: `ruoyi-system` 模块
**路径**: `work-admin/ruoyi-system/src/main/resources/mapper/system/`

```xml
<!-- 示例文件名: SupplierMapper.xml -->
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SupplierMapper">
    <!-- SQL映射 -->
</mapper>
```

**职责**:
- 编写具体的SQL语句
- 结果集映射配置

## 📝 命名规范

### 文件命名
- **Controller**: `Sys{模块名}Controller.java`
- **Domain**: `{实体名}.java` (首字母大写)
- **Service接口**: `I{实体名}Service.java`
- **Service实现**: `{实体名}ServiceImpl.java`
- **Mapper接口**: `{实体名}Mapper.java`
- **Mapper XML**: `{实体名}Mapper.xml`

### 包命名
- Controller: `com.ruoyi.web.controller.system`
- Domain: `com.ruoyi.system.domain`
- Service: `com.ruoyi.system.service`
- Service实现: `com.ruoyi.system.service.impl`
- Mapper: `com.ruoyi.system.mapper`

## 🚫 常见错误

### ❌ 错误的文件放置
```
# 错误 - 不要把业务类放在ruoyi-admin模块下
work-admin/ruoyi-admin/src/main/java/com/ruoyi/system/domain/
work-admin/ruoyi-admin/src/main/java/com/ruoyi/system/service/
work-admin/ruoyi-admin/src/main/java/com/ruoyi/system/mapper/
```

### ✅ 正确的文件放置
```
# 正确 - Controller在ruoyi-admin模块
work-admin/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/

# 正确 - 业务类在ruoyi-system模块
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/domain/
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/service/
work-admin/ruoyi-system/src/main/java/com/ruoyi/system/mapper/
```

## 📋 开发检查清单

在创建新功能时，请按以下顺序检查：

- [ ] Controller 是否放在 `ruoyi-admin/web/controller/system/` 下
- [ ] Domain 实体是否放在 `ruoyi-system/domain/` 下
- [ ] Service 接口是否放在 `ruoyi-system/service/` 下
- [ ] Service 实现是否放在 `ruoyi-system/service/impl/` 下
- [ ] Mapper 接口是否放在 `ruoyi-system/mapper/` 下
- [ ] Mapper XML 是否放在 `ruoyi-system/resources/mapper/system/` 下
- [ ] 包名是否正确
- [ ] 文件命名是否符合规范

## 🔗 模块依赖关系

```
ruoyi-admin (Web层)
    ↓ 依赖
ruoyi-system (业务层)
    ↓ 依赖  
ruoyi-common (工具层)
```

- `ruoyi-admin` 可以调用 `ruoyi-system` 的Service
- `ruoyi-system` 可以调用 `ruoyi-common` 的工具类
- 不允许反向依赖

## 📚 参考示例

### 完整的供应商管理功能文件结构
```
work-admin/
├── ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/
│   └── SysSupplierController.java
└── ruoyi-system/src/main/java/com/ruoyi/system/
    ├── domain/
    │   ├── Supplier.java
    │   └── SupplierProduct.java
    ├── service/
    │   └── ISupplierService.java
    ├── service/impl/
    │   └── SupplierServiceImpl.java
    └── mapper/
        ├── SupplierMapper.java
        └── SupplierProductMapper.java
```

## 💡 最佳实践

1. **严格分层**: 每个模块只处理自己职责范围内的事情
2. **统一命名**: 遵循项目既定的命名规范
3. **合理依赖**: 避免循环依赖，保持单向依赖关系
4. **代码复用**: 公共功能放在 `ruoyi-common` 模块
5. **文档同步**: 及时更新相关文档和注释

---

**注意**: 严格遵循此规范可以确保项目结构清晰、便于维护和团队协作。