<h1 align="center">RuoYi-Antdv</h1>

> RuoYi-Antdv 是RuoYi-Vue和Ant Design Vue Pro的结合。

<p align="center">
 <a href="https://gitee.com/fuzui/RuoYi-Antdv" target="_blank"><img src="https://gitee.com/fuzui/RuoYi-Antdv/badge/star.svg?theme=dark" alt="Build Status"></a>&nbsp;
 <a href="https://github.com/fuzui/RuoYi-Antdv" target="_blank"><img src="https://img.shields.io/github/stars/fuzui/RuoYi-Antdv.svg?style=social" alt="Build Status"></a>&nbsp;
 <a href="https://gitee.com/y_project/RuoYi-Vue" target="_blank"><img src="https://img.shields.io/badge/RuoYi Vue-latest-brightgreen" alt="Build Status"></a>&nbsp;
 <a href="https://github.com/vueComponent/ant-design-vue" target="_blank"><img src="https://img.shields.io/badge/Ant Design Vue-1.7.8-brightgreen" alt="Build Status"></a>
</p>


------------------------------

## 简介

**RuoYi-Antdv**，使用[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue)作为后端，改其原有Element Ui为Ant Design Vue。将会持续完全适配RuoYi-Vue。

> RuoYi-Vue是基于SpringBoot，Spring Security，JWT，Vue 的前后端分离权限管理系统。
>
> 拥有用户管理、部门管理、岗位管理、菜单管理、角色管理、字典管理、参数管理、通知公告、操作日志、登录日志、在线用户、定时任务、代码生成、系统接口、服务监控、在线构建器、连接池监视等功能。

* 预览：[https://ruoyi.setworld.net/](https://ruoyi.setworld.net/)

* 文档：[https://docs.geekera.cn/RuoYi-Antdv/](https://docs.geekera.cn/RuoYi-Antdv/)

* RuoYi-Vue文档：[https://doc.ruoyi.vip/ruoyi-vue/](https://doc.ruoyi.vip/ruoyi-vue/)

* Ant Design Vue文档：[https://www.antdv.com/docs/vue/introduce-cn/](https://www.antdv.com/docs/vue/introduce-cn/)



## 开始使用

1. 环境准备
   * 运行启动RuoYi-Vue，参考[RuoYi文档](https://doc.ruoyi.vip/ruoyi-vue/)
   * 安装[node](http://nodejs.org/)和[git](https://git-scm.com/)

2. 安装

   ```shell
   <NAME_EMAIL>:fuzui/RuoYi-Antdv.git
   ```

   或

   ```shell
   <NAME_EMAIL>:fuzui/RuoYi-Antdv.git
   ```

3. 本地开发

   进入项目根目录

   ```shell
   npm install
   ```

   > 若耗时太长可使用`npm install --registry=https://registry.npm.taobao.org`

   ```shell
   npm run serve
   ```

   > 打开浏览器访问 [http://localhost:8000](http://localhost:8080/)

4. 部署

   [RuoYi-Antdv部署](https://docs.geekera.cn/RuoYi-Antdv/use.html)

   

## 注意事项

* 若需使用代码生成，请按照文档描述修改：
  [代码生成前端适配](./docs/gen/) 



## 致谢

* [RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue) 基于SpringBoot，Spring Security，JWT，Vue 的前后端分离权限管理系统
* [Ant Design Vue](https://github.com/vueComponent/ant-design-vue/) An enterprise-class UI components based on Ant Design and Vue
* [Ant Design Vue Pro](https://github.com/vueComponent/ant-design-vue-pro) Use Ant Design Vue like a Pro
* [wangEditor](https://github.com/wangeditor-team/wangEditor) 轻量级web富文本框



## 推荐

* [Form-Generator-Antdv](https://github.com/fuzui/form-generator-antdv) Ant Design Vue表单设计及代码生成器

* [RuoYi-Cloud-Ant-Design](https://gitee.com/xuezipeng/ruoyi-cloud-ant-design) RuoYi-Cloud的Ant Design(Antdv)版前端

* [RuoYi-Cloud](https://gitee.com/y_project/RuoYi-Cloud) 基于Spring Boot、Spring Cloud & Alibaba的分布式微服务架构权限管理系统

## 联系

如果您发现了什么bug，或者有什么界面建议或意见，

欢迎 [issue](https://github.com/fuzui/RuoYi-Antdv/issues)

`RuoYi-Antdv(SpringBoot/SpringCloud)`群：

 [![加入QQ群](https://img.shields.io/badge/Q群-1038609759-blue.svg)](https://jq.qq.com/?_wv=1027&k=Cq8fZnrj)

如果讨论后端，推荐添加若依前后端分离交流群（官方）：

 [![加入QQ群](https://img.shields.io/badge/Q群-160110482-blue.svg)](https://jq.qq.com/?_wv=1027&k=0fsNiYZt)



## 演示图

<table>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022024.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102022136.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022247.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102022534.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oss.fuzui.net/img/20210102022749.png"/></td>
        <td><img src="https://oss.fuzui.net/img/20210102023153.png"/></td>
    </tr>
</table>



