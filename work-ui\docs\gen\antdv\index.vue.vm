<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
#set($isInsert=0)
#set($isEdit=0)
#set($queryCount=0)
#set($querySpace="")
#foreach($column in $columns)
#if($column.insert)
#set($isInsert=1)
#end
#if($column.edit)
#set($isEdit=1)
#end
#if($column.query)
#set($queryCount=$queryCount+1)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($queryCount == 3)
            <template v-if="advanced">
#set($querySpace="  ")
#end
#if($column.htmlType == "input")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-input v-model="queryParam.${column.javaField}" placeholder="请输入${comment}" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-select placeholder="请选择${comment}" v-model="queryParam.${column.javaField}" style="width: 100%" allow-clear>
                  ${querySpace}<a-select-option v-for="(d, index) in dict.type.${dictType}" :key="index" :value="d.value">{{ d.label }}</a-select-option>
                ${querySpace}</a-select>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-select placeholder="请选择${comment}" v-model="queryParam.${column.javaField}" style="width: 100%" allow-clear>
                  ${querySpace}<a-select-option>请选择字典生成</a-select-option>
                ${querySpace}</a-select>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}" prop="${column.javaField}">
                ${querySpace}<a-date-picker style="width: 100%" v-model="queryParam.${column.javaField}" format="YYYY-MM-DD HH:mm:ss" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
            ${querySpace}<a-col :md="8" :sm="24">
              ${querySpace}<a-form-item label="${comment}">
                ${querySpace}<a-range-picker style="width: 100%" v-model="daterange${AttrName}" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" allow-clear/>
              ${querySpace}</a-form-item>
            ${querySpace}</a-col>
#end
#end
#end
#if($queryCount > 2)
            </template>
#end
            <a-col :md="!advanced && 8 || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
#if($queryCount > 2)
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
#end
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!-- 操作 -->
      <div class="table-operations">
#if($isInsert == 1)
        <a-button type="primary" @click="$refs.createForm.handleAdd()" v-hasPermi="['${moduleName}:${businessName}:add']">
          <a-icon type="plus" />新增
        </a-button>
#end
#if($isEdit == 1)
        <a-button type="primary" :disabled="single" @click="$refs.createForm.handleUpdate(undefined, ids)" v-hasPermi="['${moduleName}:${businessName}:edit']">
          <a-icon type="edit" />修改
        </a-button>
#end
        <a-button type="danger" :disabled="multiple" @click="handleDelete" v-hasPermi="['${moduleName}:${businessName}:remove']">
          <a-icon type="delete" />删除
        </a-button>
        <a-button type="primary" @click="handleExport" v-hasPermi="['${moduleName}:${businessName}:export']">
          <a-icon type="download" />导出
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>
#if($isInsert == 1 || $isEdit == 1)
      <!-- 增加修改 -->
      <create-form
        ref="createForm"
#foreach ($column in $columns)
#if(${column.dictType} != '')
        :${column.javaField}Options="dict.type.${column.dictType}"
#end
#end
#if($tplCategory == 'tree')
        :${businessName}Options="${businessName}Options"
        @select-tree="getTreeselect"
#end
        @ok="getList"
      />
#end
      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="${pkColumn.javaField}"
        :columns="columns"
        :data-source="list"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered"
      >
#foreach($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.list && $column.htmlType == "datetime")
        <span slot="${javaField}" slot-scope="text, record">
          {{ parseTime(record.${javaField}) }}
        </span>
#elseif($column.list && "" != $column.dictType)
        <span slot="${javaField}" slot-scope="text, record">
          <dict-tag :options="dict.type['${column.dictType}']" :value="record.${javaField}"/>
        </span>
#end
#end
        <span slot="operation" slot-scope="text, record">
#if($isEdit == 1)
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:edit']" />
          <a @click="$refs.createForm.handleUpdate(record, undefined)" v-hasPermi="['${moduleName}:${businessName}:edit']">
            <a-icon type="edit" />修改
          </a>
#end
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:add']" />
          <a @click="$refs.createForm.handleAdd(record)" v-hasPermi="['${moduleName}:${businessName}:add']">
            <a-icon type="plus" />新增
          </a>
#end
          <a-divider type="vertical" v-hasPermi="['${moduleName}:${businessName}:remove']" />
          <a @click="handleDelete(record)" v-hasPermi="['${moduleName}:${businessName}:remove']">
            <a-icon type="delete" />删除
          </a>
        </span>
      </a-table>
#if($tplCategory != 'tree')
      <!-- 分页 -->
      <a-pagination
        class="ant-table-pagination"
        show-size-changer
        show-quick-jumper
        :current="queryParam.pageNum"
        :total="total"
        :page-size="queryParam.pageSize"
        :showTotal="total => `共 ${total} 条`"
        @showSizeChange="onShowSizeChange"
        @change="changeSize"
      />
#end
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { list${BusinessName}, del${BusinessName} } from '@/api/${moduleName}/${businessName}'
import CreateForm from './modules/CreateForm'
import { tableMixin } from '@/store/table-mixin'

export default {
  name: '${BusinessName}',
  components: {
#if($isInsert == 1 || $isEdit == 1)
    CreateForm
#end
  },
  mixins: [tableMixin],
#if(${dicts} != '')
  dicts: [${dicts}],
#end
  data () {
    return {
      list: [],
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      loading: false,
      total: 0,
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
      ${businessName}Options: [],
#end
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
      // $comment时间范围
      daterange${AttrName}: [],
#end
#end
      // 查询参数
      queryParam: {
#foreach ($column in $columns)
#if($column.query)
        $column.javaField: null,
#end
#end
        pageNum: 1,
        pageSize: 10
      },
      columns: [
#foreach($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk)
        {
          title: '${comment}',
          dataIndex: '${javaField}',
          ellipsis: true,
          align: 'center'
        },
#elseif($column.list && $column.htmlType == "datetime" || $column.list && "" != $column.dictType || $column.list && "" != $column.dictType)
        {
          title: '${comment}',
          dataIndex: '${javaField}',
          scopedSlots: { customRender: '${javaField}' },
          ellipsis: true,
          align: 'center'
        },
#elseif($column.list && "" != $javaField)
        {
          title: '${comment}',
          dataIndex: '${javaField}',
          ellipsis: true,
          align: 'center'
        },
#end
#end
        {
          title: '操作',
          dataIndex: 'operation',
          width: '18%',
          scopedSlots: { customRender: 'operation' },
          align: 'center'
        }
      ]
    }
  },
  filters: {
  },
  created () {
    this.getList()
  },
  computed: {
  },
  watch: {
  },
  methods: {
    /** 查询${functionName}列表 */
    getList () {
      this.loading = true
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
      this.queryParam.params = {}
#break
#end
#end
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
      if (this.daterange${AttrName} !== null && this.daterange${AttrName} !== '' && this.daterange${AttrName}.length !== 0) {
        this.queryParam.params['begin${AttrName}'] = this.daterange${AttrName}[0]
        this.queryParam.params['end${AttrName}'] = this.daterange${AttrName}[1]
      }
#end
#end
      list${BusinessName}(this.queryParam).then(response => {
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
        this.list = this.handleTree(response.data, '${treeCode}', '${treeParentCode}')
#else
        this.list = response.rows
#end
        this.total = response.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
      this.daterange${AttrName} = []
#end
#end
      this.queryParam = {
#foreach($column in $columns)
#if($column.query)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if(!($column.htmlType == "datetime" && $column.queryType == "BETWEEN"))
        ${column.javaField}: undefined,
#end
#end
#end
        pageNum: 1,
        pageSize: 10
      }
      this.handleQuery()
    },
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.${pkColumn.javaField})
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
#if($tplCategory == 'tree' && ($isInsert == 1 || $isEdit == 1))
    /** 查询菜单下拉树结构 */
    getTreeselect () {
      list${BusinessName}().then(response => {
        this.${businessName}Options = []
        const data = { ${treeCode}: 0, ${treeName}: '主目录', children: [] }
        data.children = this.handleTree(response.data, '${treeCode}', '${treeParentCode}')
        this.${businessName}Options.push(data)
      })
    },
#end
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      const ${pkColumn.javaField}s = row.${pkColumn.javaField} || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        content: '当前选中编号为' + ${pkColumn.javaField}s + '的数据',
        onOk () {
          return del${BusinessName}(${pkColumn.javaField}s)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          that.download('${moduleName}/${businessName}/export', {
            ...that.queryParam
          }, `${businessName}_#[[${new Date().getTime()}]]#.xlsx`)
        },
        onCancel () {}
      })
    }
  }
}
</script>
