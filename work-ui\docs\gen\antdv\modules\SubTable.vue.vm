<template>
  <a-modal
    ref="subTableModel"
    :title="'${subTable.functionName}'"
    width="80%"
    :visible="visible"
    @cancel="close"
  >
    <template slot="footer">
      <a-button type="dashed" @click="close">
        关闭
      </a-button>
    </template>
    <a-card :bordered="false">
      <!-- 操作 -->
      <div class="table-operations">
        <a-button type="primary" @click="$refs.createSubForm.handleAdd()">
          <a-icon type="plus" />新增
        </a-button>
        <a-button type="danger" :disabled="multiple" @click="handleDelete">
          <a-icon type="delete" />删除
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>
      <!-- 增加修改 -->
      <create-sub-form
        ref="createSubForm"
        @add="handleAdd${subClassName}"
      />
      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="${subTableFkclassName}"
        :columns="columns"
        :data-source="list"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered"
      >
        <span slot="operation" slot-scope="text, record">
          <a-divider type="vertical" />
          <a @click="handleDelete(record)">
            <a-icon type="delete" />删除
          </a>
        </span>
      </a-table>
    </a-card>
  </a-modal>
</template>

<script>
import CreateSubForm from './CreateSubForm'
import { tableMixin } from '@/store/table-mixin'

export default {
  name: 'SubTable',
  components: {
    CreateSubForm
  },
  props: {
    list: {
      type: Array,
      required: true
    }
  },
  mixins: [tableMixin],
  data () {
    return {
      visible: false,
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      loading: false,
      columns: [
#foreach($column in $subTable.columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk || $javaField == ${subTableFkclassName})
#elseif($column.list && "" != $javaField)
        {
          title: '${comment}',
          dataIndex: '${javaField}',
          ellipsis: true,
          align: 'center'
        },
#end
#end
        {
          title: '操作',
          dataIndex: 'operation',
          width: '18%',
          scopedSlots: { customRender: 'operation' },
          align: 'center'
        }
      ]
    }
  },
  filters: {
  },
  created () {
  },
  computed: {
  },
  watch: {
  },
  methods: {
    // 关闭模态框
    close () {
      this.visible = false
      this.selectedRowKeys = []
      this.selectedRows = []
    },
    // 打开抽屉(由外面的组件调用)
    show () {
      this.visible = true
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = selectedRowKeys.map(item => item - 1)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      if (row) {
        var index = (this.list || []).findIndex((item) => item === row)
      }
      const ids = index !== undefined ? index : this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        content: '当前选中序号为' + ids + '的数据',
        onOk () {
          that.$emit('delete', index, ids)
        },
        onCancel () {}
      })
    },
    handleAdd${subClassName} (row) {
      this.$emit('add', row)
    }
  }
}
</script>
