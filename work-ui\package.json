{"name": "ruoyi-antdv", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "build:preview": "vue-cli-service build --mode preview", "lint:nofix": "vue-cli-service lint --no-fix"}, "dependencies": {"ant-design-vue": "^1.7.8", "axios": "^0.24.0", "core-js": "^3.19.2", "echarts": "^5.2.2", "file-saver": "^2.0.5", "highlight.js": "9.18.5", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.3.0", "moment": "^2.29.1", "nprogress": "^0.2.0", "qs": "^6.10.1", "screenfull": "5.0.2", "store": "^2.0.12", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-container-query": "^0.1.0", "vue-copy-to-clipboard": "^1.0.3", "vue-cropper": "0.5.8", "vue-i18n": "^8.24.2", "vue-router": "^3.5.1", "vue-svg-component-runtime": "^1.0.1", "vuex": "^3.6.2", "wangeditor": "^4.7.10"}, "devDependencies": {"@ant-design/colors": "^6.0.0", "@vue/cli-plugin-babel": "^4.0.4", "@vue/cli-plugin-eslint": "^4.0.4", "@vue/cli-plugin-router": "^4.0.4", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-plugin-vuex": "^4.0.4", "@vue/cli-service": "^4.0.4", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.3.0", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.12.2", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^5.16.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^5.2.3", "git-revision-webpack-plugin": "^3.0.6", "less": "^3.0.4", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.6.12", "webpack": "^4.46.0", "webpack-theme-color-replacer": "^1.3.12"}}