import request from '@/utils/request'

// 查询考勤记录列表
export function listAttendance (query) {
  return request({
    url: '/system/attendance/list',
    method: 'get',
    params: query
  })
}

// 查询考勤记录详细
export function getAttendance (attendanceId) {
  return request({
    url: '/system/attendance/' + attendanceId,
    method: 'get'
  })
}

// 新增考勤记录
export function addAttendance (data) {
  return request({
    url: '/system/attendance',
    method: 'post',
    data: data
  })
}

// 修改考勤记录
export function updateAttendance (data) {
  return request({
    url: '/system/attendance',
    method: 'put',
    data: data
  })
}

// 删除考勤记录
export function delAttendance (attendanceId) {
  return request({
    url: '/system/attendance/' + attendanceId,
    method: 'delete'
  })
}

// 导出考勤记录
export function exportAttendance (query) {
  return request({
    url: '/system/attendance/export',
    method: 'post',
    params: query
  })
}

// 获取当前用户今日考勤记录
export function getTodayAttendance () {
  return request({
    url: '/system/attendance/today',
    method: 'get'
  })
}

// 签到
export function checkIn () {
  return request({
    url: '/system/attendance/checkin',
    method: 'post'
  })
}

// 签退
export function checkOut () {
  return request({
    url: '/system/attendance/checkout',
    method: 'post'
  })
}
