import request from '@/utils/request'

// 查询请假申请列表
export function listLeaveApplication(query) {
  return request({
    url: '/system/leaveApplication/list',
    method: 'get',
    params: query
  })
}

// 查询我的请假申请列表
export function myListLeaveApplication(query) {
  return request({
    url: '/system/leaveApplication/myList',
    method: 'get',
    params: query
  })
}

// 查询待审批的请假申请列表
export function pendingListLeaveApplication(query) {
  return request({
    url: '/system/leaveApplication/pendingList',
    method: 'get',
    params: query
  })
}

// 查询请假申请详细
export function getLeaveApplication(id) {
  return request({
    url: '/system/leaveApplication/' + id,
    method: 'get'
  })
}

// 新增请假申请
export function addLeaveApplication(data) {
  return request({
    url: '/system/leaveApplication',
    method: 'post',
    data: data
  })
}

// 修改请假申请
export function updateLeaveApplication(data) {
  return request({
    url: '/system/leaveApplication',
    method: 'put',
    data: data
  })
}

// 删除请假申请
export function delLeaveApplication(id) {
  return request({
    url: '/system/leaveApplication/' + id,
    method: 'delete'
  })
}

// 校验申请单号是否唯一
export function checkAppNoUnique(data) {
  return request({
    url: '/system/leaveApplication/checkAppNoUnique',
    method: 'post',
    data: data
  })
}

// 生成申请单号
export function generateAppNo() {
  return request({
    url: '/system/leaveApplication/generateAppNo',
    method: 'get'
  })
}

// 审批请假申请
export function approveLeaveApplication(id, status, remark) {
  return request({
    url: '/system/leaveApplication/approve/' + id,
    method: 'post',
    params: {
      status: status,
      remark: remark
    }
  })
}

// 撤销请假申请
export function cancelLeaveApplication(id) {
  return request({
    url: '/system/leaveApplication/cancel/' + id,
    method: 'post'
  })
}

// 根据用户ID和时间范围查询请假申请
export function getLeaveApplicationByUserAndDate(userId, startDate, endDate) {
  return request({
    url: '/system/leaveApplication/getByUserAndDate',
    method: 'get',
    params: {
      userId: userId,
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 检查是否可以删除
export function checkCanDelete(data) {
  return request({
    url: '/system/leaveApplication/checkCanDelete',
    method: 'post',
    data: data
  })
}

// 检查是否可以修改
export function checkCanEdit(data) {
  return request({
    url: '/system/leaveApplication/checkCanEdit',
    method: 'post',
    data: data
  })
} 