import request from '@/utils/request'

// 查询假期类型列表
export function listLeaveType(query) {
  return request({
    url: '/system/leaveType/list',
    method: 'get',
    params: query
  })
}

// 查询假期类型详细
export function getLeaveType(id) {
  return request({
    url: '/system/leaveType/' + id,
    method: 'get'
  })
}

// 查询所有假期类型选项
export function optionSelectLeaveType() {
  return request({
    url: '/system/leaveType/optionselect',
    method: 'get'
  })
}

// 新增假期类型
export function addLeaveType(data) {
  return request({
    url: '/system/leaveType',
    method: 'post',
    data: data
  })
}

// 修改假期类型
export function updateLeaveType(data) {
  return request({
    url: '/system/leaveType',
    method: 'put',
    data: data
  })
}

// 删除假期类型
export function delLeaveType(id) {
  return request({
    url: '/system/leaveType/' + id,
    method: 'delete'
  })
}

// 校验假期代码是否唯一
export function checkTypeCodeUnique(data) {
  return request({
    url: '/system/leaveType/checkTypeCodeUnique',
    method: 'post',
    data: data
  })
} 