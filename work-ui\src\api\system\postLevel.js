import request from '@/utils/request'

// 查询岗位级别工资列表
export function listPostLevel(query) {
  return request({
    url: '/system/post/level/list',
    method: 'get',
    params: query
  })
}

// 根据岗位ID查询级别工资列表
export function listPostLevelByPostId(postId) {
  return request({
    url: '/system/post/level/listByPostId/' + postId,
    method: 'get'
  })
}

// 查询岗位级别工资详细
export function getPostLevel(levelId) {
  return request({
    url: '/system/post/level/' + levelId,
    method: 'get'
  })
}

// 新增岗位级别工资
export function addPostLevel(data) {
  return request({
    url: '/system/post/level',
    method: 'post',
    data: data
  })
}

// 修改岗位级别工资
export function updatePostLevel(data) {
  return request({
    url: '/system/post/level',
    method: 'put',
    data: data
  })
}

// 删除岗位级别工资
export function delPostLevel(levelIds) {
  return request({
    url: '/system/post/level/' + levelIds,
    method: 'delete'
  })
}

// 批量保存岗位级别工资
export function batchSavePostLevels(postId, data) {
  return request({
    url: '/system/post/level/batchSave/' + postId,
    method: 'post',
    data: data
  })
}

// 导出岗位级别工资
export function exportPostLevel(query) {
  return request({
    url: '/system/post/level/export',
    method: 'post',
    params: query
  })
}