import request from '@/utils/request'

// 查询采购申请列表
export function listProcurement(query) {
  return request({
    url: '/system/procurement/list',
    method: 'get',
    params: query
  })
}

// 查询采购申请详细
export function getProcurement(id) {
  return request({
    url: '/system/procurement/' + id,
    method: 'get'
  })
}

// 新增采购申请
export function addProcurement(data) {
  return request({
    url: '/system/procurement',
    method: 'post',
    data: data
  })
}

// 修改采购申请
export function updateProcurement(data) {
  return request({
    url: '/system/procurement',
    method: 'put',
    data: data
  })
}

// 删除采购申请
export function delProcurement(id) {
  return request({
    url: '/system/procurement/' + id,
    method: 'delete'
  })
}

// 生成申请单号
export function generateAppNo() {
  return request({
    url: '/system/procurement/generateAppNo',
    method: 'get'
  })
}

// 提交采购申请
export function submitProcurement(data) {
  return request({
    url: '/system/procurement/submit',
    method: 'post',
    data: data
  })
}

// 根据申请单号查询采购申请
export function getProcurementByAppNo(appNo) {
  return request({
    url: '/system/procurement/getByAppNo/' + appNo,
    method: 'get'
  })
}