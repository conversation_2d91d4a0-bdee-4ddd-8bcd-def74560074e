import request from '@/utils/request'

// 查询供应商信息列表
export function listSupplier(query) {
  return request({
    url: '/system/supplier/list',
    method: 'get',
    params: query
  })
}

// 查询供应商信息详细
export function getSupplier(id) {
  return request({
    url: '/system/supplier/' + id,
    method: 'get'
  })
}

// 新增供应商信息
export function addSupplier(data) {
  return request({
    url: '/system/supplier',
    method: 'post',
    data: data
  })
}

// 修改供应商信息
export function updateSupplier(data) {
  return request({
    url: '/system/supplier',
    method: 'put',
    data: data
  })
}

// 删除供应商信息
export function delSupplier(ids) {
  return request({
    url: '/system/supplier/' + ids,
    method: 'delete'
  })
}

// 导出供应商信息
export function exportSupplier(query) {
  return request({
    url: '/system/supplier/export',
    method: 'post',
    params: query
  })
}

// 生成供应商编码
export function generateSupplierCode() {
  return request({
    url: '/system/supplier/generateCode',
    method: 'get'
  })
}

// 查询供应商选项列表（用于下拉选择）
export function getSupplierOptions() {
  return request({
    url: '/system/supplier/options',
    method: 'get'
  })
}