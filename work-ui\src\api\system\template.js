import request from '@/utils/request'

// 查询审批模板列表
export function listTemplate(query) {
  return request({
    url: '/system/template/list',
    method: 'get',
    params: query
  })
}

// 查询审批模板详细
export function getTemplate(id) {
  return request({
    url: '/system/template/' + id,
    method: 'get'
  })
}

// 新增审批模板
export function addTemplate(data) {
  return request({
    url: '/system/template',
    method: 'post',
    data: data
  })
}

// 修改审批模板
export function updateTemplate(data) {
  return request({
    url: '/system/template',
    method: 'put',
    data: data
  })
}

// 删除审批模板
export function delTemplate(ids) {
  return request({
    url: '/system/template/' + ids,
    method: 'delete'
  })
}

// 导出审批模板
export function exportTemplate(query) {
  return request({
    url: '/system/template/export',
    method: 'post',
    params: query
  })
}

// 根据业务类型查询激活的审批模板
export function getActiveTemplatesByBusinessType(businessType) {
  return request({
    url: '/system/template/active/' + businessType,
    method: 'get'
  })
}

// 设置默认模板
export function setDefaultTemplate(id) {
  return request({
    url: '/system/template/setDefault/' + id,
    method: 'put'
  })
}

// 激活/停用模板
export function toggleTemplateStatus(id, isActive) {
  return request({
    url: '/system/template/toggleStatus/' + id,
    method: 'put',
    params: { isActive }
  })
}

// 校验模板代码唯一性
export function checkTemplateCodeUnique(templateCode, id) {
  return request({
    url: '/system/template/checkCode',
    method: 'get',
    params: { templateCode, id }
  })
}