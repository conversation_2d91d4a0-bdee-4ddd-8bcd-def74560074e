<template>
  <div class="clearfix">
    <a-upload
      :action="uploadImgUrl"
      :list-type="type == 'image' ? 'picture-card' : 'picture'"
      :show-upload-list="showUploadList"
      :file-list="fileList"
      :headers="headers"
      :before-upload="beforeUpload"
      @change="handleChange"
      @remove="handleRemove"
    >
      <img v-if="value && type == 'image' && !showUploadList" :src="value" alt="avatar" />
      <div v-if="type == 'file' && !showUploadList">
        <a-button> <a-icon type="upload" /> 上传 </a-button>
      </div>
      <div v-if="(!value || value == '' || value == null) && !showUploadList">
        <span v-if="type == 'image'">
          <a-icon :type="loading ? 'loading' : 'plus'" />
          <div class="ant-upload-text">
            上传
          </div>
        </span>
      </div>
      <!-- 使用文件列表显示时的上传按钮 -->
      <div v-if="showUploadList && fileList.length < limit">
        <a-icon :type="loading ? 'loading' : 'plus'" />
        <div class="ant-upload-text">上传</div>
      </div>
    </a-upload>
    <span v-if="value && type == 'file' && !showUploadList">
      <br>
      <a :href="value" target="_blank" >{{ value }}</a>
    </span>
  </div>
</template>

<script>
import storage from 'store'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
  name: 'ImageUpload',
  props: {
    value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'image'
    },
    count: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 1
    },
    showUploadList: {
      type: Boolean,
      default: true
    }
  },
  components: {
  },
  data () {
    return {
      loading: false,
      open: false,
      fileList: [],
      uploadImgUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      headers: {
        Authorization: 'Bearer ' + storage.get(ACCESS_TOKEN)
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && this.showUploadList) {
          // 当有value值时，构造fileList用于回显
          this.fileList = [{
            uid: '-1',
            name: this.getFileName(newVal),
            status: 'done',
            url: newVal
          }]
        } else if (!newVal) {
          this.fileList = []
        }
      },
      immediate: true
    }
  },
  mounted () {
  },
  methods: {
    handleCancel () {
      this.previewVisible = false
    },
    async handleChange (info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-this.limit) // 限制文件数量
      fileList = fileList.map(file => {
        if (file.response) {
          file.url = file.response.url
        }
        return file
      })
      this.fileList = fileList

      if (info.file.status === 'uploading') {
        this.loading = true
        return
      }
      if (info.file.status === 'done') {
        this.loading = false
        if (info.file.response && info.file.response.code !== 200) {
          this.$message.error('上传失败: ' + (info.file.response.msg || '未知错误'))
          this.$emit('input', '') // 上传失败清空value
          // 移除失败的文件
          this.fileList = this.fileList.filter(f => f.uid !== info.file.uid)
          return
        }
        // 上传成功
        const url = info.file.response ? info.file.response.url : info.file.url
        this.$emit('input', url)
      } else if (info.file.status === 'error') {
        this.loading = false
        this.$message.error('上传失败')
        this.$emit('input', '') // 上传失败清空value
        // 移除失败的文件
        this.fileList = this.fileList.filter(f => f.uid !== info.file.uid)
      }
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
      this.$emit('input', '') // 移除文件时清空value
    },
    beforeUpload (file) {
      // 文件类型(file.type)、大小限制(file.size)
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('图片大小限制 10MB!')
      }
      return isLt2M
    },
    // 清空文件列表
    clearFiles() {
      this.fileList = []
      this.$emit('input', '')
    },
    // 从URL中获取文件名
    getFileName(url) {
      if (!url) return ''
      const parts = url.split('/')
      return parts[parts.length - 1]
    },
    // 设置文件值（用于修改模式下的回显）
    setValue(url) {
      if (url && this.showUploadList) {
        this.fileList = [{
          uid: '-1',
          name: this.getFileName(url),
          status: 'done',
          url: url
        }]
      } else {
        this.fileList = []
      }
      this.loading = false // 确保loading状态为false
      this.$emit('input', url || '')
    }
  }
}
</script>
<style lang="less" scoped>
img {
  width: 128px;
  height: 128px;
}

</style>
