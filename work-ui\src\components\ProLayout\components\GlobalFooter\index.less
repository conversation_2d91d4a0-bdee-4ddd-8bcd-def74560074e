@import "~ant-design-vue/es/style/themes/default";

@global-footer-prefix-cls: ~'@{ant-prefix}-pro-global-footer';

.@{global-footer-prefix-cls} {
  margin: 48px 0 24px 0;
  padding: 0 16px;
  text-align: center;

  &-links {
    margin-bottom: 8px;

    a {
      color: @text-color-secondary;
      transition: all 0.3s;

      &:not(:last-child) {
        margin-right: 40px;
      }

      &:hover {
        color: @text-color;
      }
    }
  }

  &-copyright {
    color: @text-color-secondary;
    font-size: @font-size-base;
  }
}
