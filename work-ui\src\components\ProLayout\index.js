export { default, BasicLayoutProps } from './BasicLayout'
export { default as BlockLayout } from './BlockLayout'
export { default as PageHeaderWrapper } from './components/PageHeaderWrapper'
export { default as SiderMenuWrapper } from './components/SiderMenu'
export { default as GlobalFooter } from './components/GlobalFooter'
export { default as SettingDrawer } from './components/SettingDrawer'
export { default as DocumentTitle } from './components/DocumentTitle'
export { default as BaseMenu } from './components/RouteMenu'
// func
export { updateTheme, updateColorWeak } from './utils/dynamicTheme'
