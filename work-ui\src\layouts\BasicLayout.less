@import "~ant-design-vue/es/style/themes/default.less";

.ant-pro-global-header-index-right {
  margin-right: 8px;

  &.ant-pro-global-header-index-dark {
    .ant-pro-global-header-index-action {
      color: hsla(0, 0%, 100%, .85);

      &:hover {
        background: #1890ff;
      }
    }
  }

  .ant-pro-account-avatar {
    .antd-pro-global-header-index-avatar {
      margin: ~'calc((@{layout-header-height} - 24px) / 2)' 0;
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      background: rgba(255, 255, 255, 0.85);
    }
  }

  .menu {
    .anticon {
      margin-right: 8px;
    }

    .ant-dropdown-menu-item {
      min-width: 100px;
    }
  }
}
.ant-layout-sider-dark .ant-layout-sider-children {
  ::-webkit-scrollbar-track {
    background: hsla(0,0%,100%,.15);
    border-radius: 3px;
    box-shadow: inset 0 0 5px rgb(37 37 37 / 5%);
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsla(0,0%,100%,.2);
    border-radius: 3px;
    box-shadow: inset 0 0 5px hsl(0deg 0% 100% / 5%);
  }

  // ie滚动条样式
  scrollbar-arrow-color: #001528;
  scrollbar-face-color: #000d17;
  scrollbar-highlight-color: #001528;
  scrollbar-shadow-color: #001528;
  scrollbar-track-color: #001528;
}
.ant-layout-sider-light .ant-layout-sider-children {
  ::-webkit-scrollbar-track {
    background: rgba(0,0,0,.06);
    box-shadow: inset 0 0 5px rgb(0 21 41 / 5%);
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,.12);
    box-shadow: inset 0 0 5px rgb(0 21 41 / 5%);
  }

  // ie滚动条样式
  scrollbar-arrow-color: #ffffff;
  scrollbar-face-color: #f0f0f0;
  scrollbar-highlight-color: #ffffff;
  scrollbar-shadow-color: #ffffff;
  scrollbar-track-color: #ffffff;
}
