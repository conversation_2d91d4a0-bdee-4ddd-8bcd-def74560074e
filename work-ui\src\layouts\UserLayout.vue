<template>
  <div id="userLayout" :class="['user-layout-wrapper', isMobile && 'mobile']">
    <div class="layout-container">
      <!-- 左侧登录区域 -->
      <div class="login-section">
        <div class="login-content">
          <div class="top">
            <div class="header">
              <a href="/" class="brand-link">
                <div class="logo-container">
                  <img src="~@/assets/logo.png" class="logo" alt="logo">
                </div>
                <span class="title">企业办公系统</span>
              </a>
            </div>
            <!--<div class="desc">-->
            <!--  基于SpringBoot，Spring Security，JWT，Vue 的前后端分离权限管理系统-->
            <!--</div>-->
          </div>

          <div class="main">
            <div class="login-form-wrapper">
              <router-view />
            </div>
          </div>

          <div class="footer">
            <!--<div class="links">-->
            <!--  <a href="https://github.com/fuzui/RuoYi-Antdv" target="_blank">帮助</a>-->
            <!--  <a href="https://github.com/fuzui/RuoYi-Antdv" target="_blank">隐私</a>-->
            <!--  <a href="https://github.com/fuzui/RuoYi-Antdv" target="_blank">条款</a>-->
            <!--</div>-->
            <!--<div class="copyright">-->
            <!--  Copyright &copy; 2022 <a href="https://github.com/fuzui/RuoYi-Antdv" target="_blank">RuoYi-Antdv</a><br/>-->
            <!--</div>-->
            <!--<a-space size="large">-->
            <!--  <a href="https://beian.miit.gov.cn/" target="_blank">-->
            <!--    <img src="https://cdn.fuzui.net/icon/beian.png?x-oss-process=style/ys" style="width:18px">-->
            <!--    宁ICP备 2020001732号-1-->
            <!--  </a>-->
            <!--  <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11011202003445" target="_blank">-->
            <!--    <img src="https://oss.fuzui.net/img/202202010105048.png" style="width:18px">-->
            <!--    京公网安备 11011202003445号-->
            <!--  </a>-->
            <!--</a-space>-->
          </div>
        </div>
      </div>

      <!-- 右侧图片区域 -->
      <div class="image-section">
        <!-- 浮动几何装饰 -->
        <div class="floating-decorations">
          <div class="decoration-dot dot-1"></div>
          <div class="decoration-dot dot-2"></div>
          <div class="decoration-dot dot-3"></div>
          <div class="decoration-dot dot-4"></div>
          <div class="decoration-shape shape-1"></div>
          <div class="decoration-shape shape-2"></div>
          <div class="decoration-triangle triangle-1"></div>
          <div class="decoration-triangle triangle-2"></div>
        </div>

        <!-- 背景网格 -->
        <div class="background-grid"></div>

        <!-- 图片容器 -->
        <div class="image-container">
          <div class="image-glow-bg"></div>
          <img src="~@/assets/index-logo.png" alt="Logo" class="main-image">
          <div class="image-reflection"></div>
        </div>

        <!-- 光束效果 -->
        <div class="light-beams">
          <div class="light-beam beam-1"></div>
          <div class="light-beam beam-2"></div>
          <div class="light-beam beam-3"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { deviceMixin } from '@/store/device-mixin'

export default {
  name: 'UserLayout',
  mixins: [deviceMixin],
  mounted () {
    document.body.classList.add('userLayout')
  },
  beforeDestroy () {
    document.body.classList.remove('userLayout')
  }
}
</script>

<style lang="less" scoped>
// 页面主容器
#userLayout.user-layout-wrapper {
  height: 100vh;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 响应式
#userLayout.user-layout-wrapper.mobile .layout-container {
  flex-direction: column;
}

#userLayout.user-layout-wrapper.mobile .login-section {
  width: 100%;
  min-height: 60vh;
}

#userLayout.user-layout-wrapper.mobile .image-section {
  width: 100%;
  height: 40vh;
}

// 左右布局容器
.layout-container {
  display: flex;
  height: 100%;
  width: 100%;
}

// 左侧登录区域
.login-section {
  flex: 0 0 45%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 200px rgba(255, 255, 255, 0.1);
}

// 添加3D背景装饰
.login-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  animation: float3D 15s ease-in-out infinite;
  z-index: 1;
}

.login-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url(~@/assets/background.svg) no-repeat center;
  background-size: cover;
  opacity: 0.05;
  z-index: 1;
}

// 登录内容容器
.login-content {
  max-width: 420px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 10;
  transform-style: preserve-3d;
}

// 顶部区域
.top {
  text-align: center;
  margin-bottom: 50px;
  transform: translateZ(20px);
  perspective: 1000px;
}

// 头部区域
.header {
  margin-bottom: 24px;
  transform: translateZ(30px);
}

// 品牌链接
.brand-link {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

.brand-link:hover {
  transform: translateY(-8px) translateZ(20px) rotateX(5deg);
  filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.3));
}

// Logo容器
.logo-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(255, 255, 255, 0.2) inset,
    0 -4px 16px rgba(0, 0, 0, 0.1) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.logo-container:hover {
  transform: rotateY(15deg) rotateX(10deg) translateZ(10px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(255, 255, 255, 0.3) inset,
    0 -8px 24px rgba(0, 0, 0, 0.15) inset;
}

// Logo图片
.logo {
  width: 32px;
  height: 32px;
  border-style: none;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.1) rotateZ(5deg);
}

// 标题
.title {
  font-size: 32px;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 700;
  letter-spacing: -0.5px;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.2),
    0 8px 16px rgba(0, 0, 0, 0.1);
  background: linear-gradient(145deg, #ffffff, #e0e6ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.title:hover {
  transform: scale(1.05);
  text-shadow:
    0 4px 8px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.3),
    0 16px 32px rgba(0, 0, 0, 0.2);
}

// 描述文字
.desc {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 380px;
  margin: 0 auto;
  font-weight: 400;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateZ(15px);
  transition: all 0.3s ease;
}

.desc:hover {
  transform: translateZ(20px) scale(1.02);
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

// 主要内容区域
.main {
  margin-bottom: 40px;
  transform: translateZ(25px);
  perspective: 1000px;
}

// 登录表单包装器
.login-form-wrapper {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(30px);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.2),
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(255, 255, 255, 0.3) inset,
    0 -4px 16px rgba(0, 0, 0, 0.05) inset;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  position: relative;
}

.login-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(0, 0, 0, 0.02) 100%);
  border-radius: 24px;
  z-index: -1;
}

.login-form-wrapper:hover {
  transform: translateY(-12px) translateZ(30px) rotateX(2deg);
  box-shadow:
    0 40px 120px rgba(0, 0, 0, 0.25),
    0 20px 48px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(255, 255, 255, 0.4) inset,
    0 -8px 24px rgba(0, 0, 0, 0.08) inset;
}

// 底部区域
.footer {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  transform: translateZ(15px);
  transition: all 0.3s ease;
}

.footer:hover {
  transform: translateZ(20px) scale(1.02);
}

// 链接区域
.links {
  margin-bottom: 20px;
  font-size: 14px;
  perspective: 800px;
}

.links a {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 10px 18px;
  border-radius: 12px;
  display: inline-block;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform-style: preserve-3d;
  position: relative;
}

.links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 12px;
  z-index: -1;
  transition: all 0.3s ease;
}

.links a:hover {
  color: #ffffff;
  transform: translateY(-4px) translateZ(15px) rotateX(10deg);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.links a:hover::before {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
}

.links a:not(:last-child) {
  margin-right: 12px;
}

// 版权信息
.copyright {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin-bottom: 20px;
  line-height: 1.5;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.copyright:hover {
  transform: scale(1.02);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.copyright a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 2px 6px;
  border-radius: 6px;
}

.copyright a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateZ(5px);
}

// 备案信息
.ant-space a {
  color: rgba(255, 255, 255, 0.75);
  text-decoration: none;
  transition: all 0.4s ease;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform-style: preserve-3d;
}

.ant-space a:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px) translateZ(10px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

// 3D浮动动画
@keyframes float3D {
  0%, 100% {
    transform: translateX(0) translateY(0) rotateZ(0deg);
  }
  25% {
    transform: translateX(1%) translateY(-0.5%) rotateZ(0.5deg);
  }
  50% {
    transform: translateX(-0.5%) translateY(-1%) rotateZ(-0.3deg);
  }
  75% {
    transform: translateX(-1%) translateY(0.5%) rotateZ(0.2deg);
  }
}

// 右侧图片区域
.image-section {
  flex: 1;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 40%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
  z-index: -2;
  animation: backgroundPulse 20s ease-in-out infinite;
}

// 浮动几何装饰
.floating-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.decoration-dot {
  position: absolute;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  opacity: 0.5;
  animation: floatDot 10s ease-in-out infinite;
}

.dot-1 {
  top: 10%;
  left: 20%;
  animation-delay: -2s;
}
.dot-2 {
  top: 70%;
  left: 80%;
  animation-delay: -4s;
}
.dot-3 {
  top: 40%;
  left: 40%;
  animation-delay: -6s;
}
.dot-4 {
  top: 90%;
  left: 60%;
  animation-delay: -8s;
}

.decoration-shape {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  opacity: 0.4;
  animation: floatShape 15s ease-in-out infinite;
}

.shape-1 {
  top: 30%;
  left: 50%;
  animation-delay: -3s;
}
.shape-2 {
  top: 60%;
  left: 20%;
  animation-delay: -7s;
}

.decoration-triangle {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid rgba(255, 255, 255, 0.3);
  opacity: 0.5;
  animation: floatTriangle 12s ease-in-out infinite;
}

.triangle-1 {
  top: 10%;
  left: 70%;
  animation-delay: -1s;
}
.triangle-2 {
  top: 80%;
  left: 30%;
  animation-delay: -5s;
}

// 背景网格
.background-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to right,
    rgba(255, 255, 255, 0.05) 0px,
    rgba(255, 255, 255, 0.05) 1px,
    transparent 1px,
    transparent 2px
  );
  opacity: 0.1;
  z-index: -1;
}

// 图片容器
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-glow-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1), transparent 70%);
  opacity: 0.5;
  z-index: -1;
}

.main-image {
  max-width: 90%;
  max-height: 80vh;
  height: auto;
  width: auto;
  object-fit: contain;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 20px 40px rgba(99, 102, 241, 0.15));
  animation: imageFloat 6s ease-in-out infinite;
  z-index: 10;
  position: relative;
}

.main-image:hover {
  transform: scale(1.08) translateY(-5px);
  filter: drop-shadow(0 30px 60px rgba(99, 102, 241, 0.25));
}

.image-reflection {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
  z-index: 1;
}

// 光束效果
.light-beams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.light-beam {
  position: absolute;
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0.5;
  animation: floatBeam 10s ease-in-out infinite;
}

.beam-1 {
  left: 20%;
  animation-delay: -2s;
}
.beam-2 {
  left: 50%;
  animation-delay: -4s;
}
.beam-3 {
  left: 80%;
  animation-delay: -6s;
}

// 全局表单样式
:deep(.ant-form) .ant-form-item {
  margin-bottom: 24px;
}

:deep(.ant-form) .ant-input,
:deep(.ant-form) .ant-input-password {
  height: 48px;
  border-radius: 12px;
  border: 2px solid #e8ecf4;
  font-size: 16px;
  transition: all 0.3s ease;
}

:deep(.ant-form) .ant-input:hover,
:deep(.ant-form) .ant-input-password:hover {
  border-color: #667eea;
}

:deep(.ant-form) .ant-input:focus,
:deep(.ant-form) .ant-input-password:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

// 验证码相关样式
:deep(.ant-form) .ant-input-group {
  display: flex;
  align-items: stretch; // 改为stretch，让子元素高度一致
}

:deep(.ant-form) .ant-input-group .ant-input {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  height: 48px;
  line-height: 1.5;
  padding: 11px 15px; // 明确设置内边距
}

:deep(.ant-form) .ant-input-group-addon {
  padding: 0;
  border: 2px solid #e8ecf4;
  border-left: none;
  border-radius: 0 12px 12px 0;
  background: transparent;
  height: 48px;
  position: relative;
  box-sizing: border-box;
  width: 120px; // 固定宽度
}

// 验证码图片样式
:deep(.ant-form) .getCaptcha {
  height: 48px !important;
  width: 100% !important;
  object-fit: contain;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e8ecf4;
  background: #ffffff;
}

:deep(.ant-form) .getCaptcha:hover {
  border-color: #667eea;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

:deep(.ant-form) .ant-input-group:hover .ant-input-group-addon {
  border-color: #667eea;
}

:deep(.ant-form) .ant-input-group .ant-input:focus + .ant-input-group-addon {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

// 确保验证码输入框组合的整体对齐
:deep(.ant-form) .ant-form-item-control-input {
  display: flex;
  align-items: center;
}

:deep(.ant-form) .ant-input-group-wrapper {
  display: flex;
  width: 100%;
}

:deep(.ant-form) .ant-btn-primary {
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

:deep(.ant-form) .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

:deep(.ant-form) .ant-checkbox-wrapper {
  color: #666;
  font-size: 14px;
}

// 浮动动画
@keyframes floatDot {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) translateX(5px) rotate(5deg);
  }
  50% {
    transform: translateY(10px) translateX(-5px) rotate(-5deg);
  }
  75% {
    transform: translateY(-5px) translateX(10px) rotate(2deg);
  }
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(10deg);
  }
  50% {
    transform: translateY(15px) translateX(-10px) rotate(-10deg);
  }
  75% {
    transform: translateY(-10px) translateX(15px) rotate(5deg);
  }
}

@keyframes floatTriangle {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) translateX(10px) rotate(15deg);
  }
  50% {
    transform: translateY(10px) translateX(-10px) rotate(-15deg);
  }
  75% {
    transform: translateY(-10px) translateX(10px) rotate(10deg);
  }
}

@keyframes floatBeam {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(20px) translateX(-10px);
  }
  75% {
    transform: translateY(-10px) translateX(20px);
  }
}

// 动画定义
@keyframes backgroundPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes imageFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(0.5deg);
  }
  50% {
    transform: translateY(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-4px) rotate(-0.5deg);
  }
}
</style>
