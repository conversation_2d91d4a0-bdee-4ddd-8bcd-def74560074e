<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- 考勤打卡区域 -->
      <div class="clock-section" style="margin-bottom: 24px; text-align: center; padding: 24px; background: #f5f5f5; border-radius: 6px;">
        <div style="margin-bottom: 16px;">
          <a-icon type="clock-circle" style="font-size: 32px; color: #1890ff;" />
          <h2 style="margin: 8px 0; color: #333;">考勤打卡</h2>
          <p style="color: #666; margin-bottom: 16px;">当前时间：{{ currentTime }}</p>
        </div>
        <div v-if="todayAttendance">
          <a-row :gutter="24" type="flex" justify="center">
            <a-col :span="8">
              <a-card size="small">
                <p style="margin: 0; color: #666;">签到时间</p>
                <h3 style="margin: 4px 0; color: #52c41a;">{{ todayAttendance.checkInTime | parseTime }}</h3>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small">
                <p style="margin: 0; color: #666;">签退时间</p>
                <h3 style="margin: 4px 0; color: #1890ff;">{{ todayAttendance.checkOutTime | parseTime || '未签退' }}</h3>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small">
                <p style="margin: 0; color: #666;">工作时长</p>
                <h3 style="margin: 4px 0; color: #722ed1;">{{ todayAttendance.workHours || 0 }}小时</h3>
              </a-card>
            </a-col>
          </a-row>
        </div>
        <div style="margin-top: 16px;">
          <a-button
            type="primary"
            size="large"
            :disabled="todayAttendance && todayAttendance.checkInTime"
            @click="handleCheckIn"
            style="margin-right: 16px;">
            <a-icon type="login" />
            签到
          </a-button>
          <a-button
            type="primary"
            size="large"
            :disabled="!todayAttendance || !todayAttendance.checkInTime || todayAttendance.checkOutTime"
            @click="handleCheckOut">
            <a-icon type="logout" />
            签退
          </a-button>
        </div>
      </div>

      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="用户名称">
                <a-input v-model="queryParam.userName" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="考勤状态">
                <a-select placeholder="请选择状态" style="width: 100%" allow-clear v-model="queryParam.attendanceStatus">
                  <a-select-option v-for="(d, index) in dict.type['attendance_status']" :key="index" :value="d.value">{{ d.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="考勤日期">
                  <a-range-picker style="width: 100%" v-model="dateRange" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" allow-clear />
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="!advanced && 8 || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作 -->
      <div class="table-operations">
        <a-button type="primary" @click="$refs.createForm.handleAdd()" v-hasPermi="['attendance:record:add']">
          <a-icon type="plus" />新增
        </a-button>
        <a-button type="primary" :disabled="single" @click="$refs.createForm.handleUpdate(undefined,ids)" v-hasPermi="['attendance:record:edit']">
          <a-icon type="edit" />修改
        </a-button>
        <a-button type="danger" :disabled="multiple" @click="handleDelete" v-hasPermi="['attendance:record:remove']">
          <a-icon type="delete" />删除
        </a-button>
        <a-button type="primary" @click="handleExport" v-hasPermi="['attendance:record:export']">
          <a-icon type="download" />导出
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>

      <!-- 创建/编辑考勤记录 -->
      <create-form
        ref="createForm"
        :statusOptions="dict.type['attendance_status']"
        @ok="getList"
      />

      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="attendanceId"
        :columns="columns"
        :data-source="list"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered">
        <span slot="attendanceStatus" slot-scope="text, record">
          <dict-tag :options="dict.type['attendance_status']" :value="record.attendanceStatus"/>
        </span>
        <span slot="checkInTime" slot-scope="text, record">
          {{ parseTime(record.checkInTime) }}
        </span>
        <span slot="checkOutTime" slot-scope="text, record">
          {{ parseTime(record.checkOutTime) }}
        </span>
        <span slot="operation" slot-scope="text, record">
          <a @click="$refs.createForm.handleUpdate(record,undefined)" v-hasPermi="['attendance:record:edit']">
            <a-icon type="edit" />
            修改
          </a>
          <a-divider type="vertical" v-hasPermi="['attendance:record:remove']" />
          <a @click="handleDelete(record)" v-hasPermi="['attendance:record:remove']">
            <a-icon type="delete" />
            删除
          </a>
        </span>
      </a-table>
      <!-- 分页 -->
      <a-pagination
        class="ant-table-pagination"
        show-size-changer
        show-quick-jumper
        :current="queryParam.pageNum"
        :total="total"
        :page-size="queryParam.pageSize"
        :showTotal="total => `共 ${total} 条`"
        @showSizeChange="onShowSizeChange"
        @change="changeSize"
      />
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { listAttendance, delAttendance, getTodayAttendance, checkIn, checkOut } from '@/api/system/attendance'
import CreateForm from './modules/CreateForm'
import { tableMixin } from '@/store/table-mixin'

export default {
  name: 'AttendanceRecord',
  components: {
    CreateForm
  },
  mixins: [tableMixin],
  dicts: ['attendance_status'],
  data () {
    return {
      list: [],
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      loading: false,
      total: 0,
      // 当前时间
      currentTime: '',
      // 今日考勤记录
      todayAttendance: null,
      // 日期范围
      dateRange: [],
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        attendanceStatus: undefined
      },
      columns: [
        {
          title: '考勤编号',
          dataIndex: 'attendanceId',
          align: 'center'
        },
        {
          title: '用户名',
          dataIndex: 'userName',
          align: 'center'
        },
        {
          title: '用户昵称',
          dataIndex: 'nickName',
          align: 'center'
        },
        {
          title: '部门',
          dataIndex: 'deptName',
          align: 'center'
        },
        {
          title: '考勤日期',
          dataIndex: 'attendanceDate',
          align: 'center'
        },
        {
          title: '签到时间',
          dataIndex: 'checkInTime',
          scopedSlots: { customRender: 'checkInTime' },
          align: 'center'
        },
        {
          title: '签退时间',
          dataIndex: 'checkOutTime',
          scopedSlots: { customRender: 'checkOutTime' },
          align: 'center'
        },
        {
          title: '工作时长(时)',
          dataIndex: 'workHours',
          align: 'center'
        },
        {
          title: '考勤状态',
          dataIndex: 'attendanceStatus',
          scopedSlots: { customRender: 'attendanceStatus' },
          align: 'center'
        },
        {
          title: '迟到(分)',
          dataIndex: 'lateMinutes',
          align: 'center'
        },
        {
          title: '早退(分)',
          dataIndex: 'earlyMinutes',
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          align: 'center'
        }
      ]
    }
  },
  filters: {
  },
  created () {
    this.getList()
    this.getTodayAttendanceData()
    this.updateCurrentTime()
    // 每秒更新一次时间
    setInterval(() => {
      this.updateCurrentTime()
    }, 1000)
  },
  computed: {
  },
  watch: {
  },
  methods: {
    /** 查询考勤记录列表 */
    getList () {
      this.loading = true
      listAttendance(this.addDateRange(this.queryParam, this.dateRange)).then(response => {
          this.list = response.rows
          this.total = response.total
          this.loading = false
        }
      )
    },
    /** 获取今日考勤记录 */
    getTodayAttendanceData () {
      getTodayAttendance().then(response => {
        this.todayAttendance = response.data
      })
    },
    /** 更新当前时间 */
    updateCurrentTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    /** 签到 */
    handleCheckIn () {
      checkIn().then(response => {
        this.$message.success(response.msg)
        this.getTodayAttendanceData()
        this.getList()
      }).catch(() => {
        this.$message.error('签到失败')
      })
    },
    /** 签退 */
    handleCheckOut () {
      checkOut().then(response => {
        this.$message.success(response.msg)
        this.getTodayAttendanceData()
        this.getList()
      }).catch(() => {
        this.$message.error('签退失败')
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.dateRange = []
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        attendanceStatus: undefined
      }
      this.handleQuery()
    },
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.attendanceId)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      const attendanceIds = row.attendanceId || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        content: '当前选中编号为' + attendanceIds + '的数据',
        onOk () {
          return delAttendance(attendanceIds)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          that.download('system/attendance/export', {
            ...that.queryParam
          }, `attendance_${new Date().getTime()}.xlsx`)
        },
        onCancel () {}
      })
    }
  }
}
</script>
<style lang="less" scoped>
.clock-section {
  .ant-card {
    text-align: center;
    border: 1px solid #f0f0f0;
  }
}
</style>
