<template>
  <a-modal
    :title="formTitle"
    :width="640"
    :visible="open"
    :confirmLoading="confirmLoading"
    @ok="submitForm"
    @cancel="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="用户" prop="userId">
        <a-tree-select
          v-model="form.userId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="userOptions"
          placeholder="请选择用户"
          :replaceFields="{ children: 'children', title: 'title', key: 'key', value: 'key' }"
          tree-default-expand-all
          @change="handleUserChange"
        />
      </a-form-model-item>
      <a-form-model-item label="考勤日期" prop="attendanceDate">
        <a-date-picker
          v-model="form.attendanceDate"
          style="width: 100%"
          value-format="YYYY-MM-DD"
          placeholder="请选择考勤日期"
        />
      </a-form-model-item>
      <a-form-model-item label="签到时间" prop="checkInTime">
        <a-date-picker
          v-model="form.checkInTime"
          style="width: 100%"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择签到时间"
        />
      </a-form-model-item>
      <a-form-model-item label="签退时间" prop="checkOutTime">
        <a-date-picker
          v-model="form.checkOutTime"
          style="width: 100%"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择签退时间"
        />
      </a-form-model-item>
      <a-form-model-item label="工作时长" prop="workHours">
        <a-input-number
          v-model="form.workHours"
          style="width: 100%"
          :precision="2"
          :min="0"
          :max="24"
          placeholder="请输入工作时长(小时)"
        />
      </a-form-model-item>
      <a-form-model-item label="考勤状态" prop="attendanceStatus">
        <a-radio-group v-model="form.attendanceStatus">
          <a-radio
            v-for="(d, index) in statusOptions"
            :key="index"
            :value="d.value"
          >{{ d.label }}</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="迟到分钟" prop="lateMinutes">
        <a-input-number
          v-model="form.lateMinutes"
          style="width: 100%"
          :min="0"
          placeholder="请输入迟到分钟数"
        />
      </a-form-model-item>
      <a-form-model-item label="早退分钟" prop="earlyMinutes">
        <a-input-number
          v-model="form.earlyMinutes"
          style="width: 100%"
          :min="0"
          placeholder="请输入早退分钟数"
        />
      </a-form-model-item>
      <a-form-model-item label="备注" prop="remark">
        <a-textarea v-model="form.remark" placeholder="请输入备注" :auto-size="{ minRows: 3, maxRows: 6 }"/>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { getAttendance, addAttendance, updateAttendance } from '@/api/system/attendance'
import { listUser } from '@/api/system/user'

export default {
  name: 'CreateForm',
  props: {
    statusOptions: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      confirmLoading: false,
      formTitle: '',
      // 表单参数
      form: {
        attendanceId: undefined,
        userId: undefined,
        attendanceDate: undefined,
        checkInTime: undefined,
        checkOutTime: undefined,
        workHours: undefined,
        attendanceStatus: '0',
        lateMinutes: 0,
        earlyMinutes: 0,
        remark: undefined
      },
      open: false,
      rules: {
        userId: [{ required: true, message: '用户不能为空', trigger: 'change' }],
        attendanceDate: [{ required: true, message: '考勤日期不能为空', trigger: 'change' }],
        checkInTime: [{ required: true, message: '签到时间不能为空', trigger: 'change' }],
        attendanceStatus: [{ required: true, message: '考勤状态不能为空', trigger: 'change' }]
      },
      userOptions: []
    }
  },
  created () {
    this.getUserList()
  },
  methods: {
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        attendanceId: undefined,
        userId: undefined,
        attendanceDate: undefined,
        checkInTime: undefined,
        checkOutTime: undefined,
        workHours: undefined,
        attendanceStatus: '0',
        lateMinutes: 0,
        earlyMinutes: 0,
        remark: undefined
      }
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset()
      this.open = true
      this.formTitle = '添加考勤记录'
    },
    /** 修改按钮操作 */
    handleUpdate (row, ids) {
      this.reset()
      const attendanceId = row ? row.attendanceId : ids
      getAttendance(attendanceId).then(response => {
        this.form = response.data
        this.open = true
        this.formTitle = '修改考勤记录'
      })
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          if (this.form.attendanceId !== undefined) {
            updateAttendance(this.form).then(response => {
              this.$message.success(
                '修改成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.confirmLoading = false
            })
          } else {
            addAttendance(this.form).then(response => {
              this.$message.success(
                '新增成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.confirmLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    /** 获取用户列表 */
    getUserList () {
      listUser().then(response => {
        this.userOptions = this.buildUserTreeOptions(response.rows)
      })
    },
    /** 构建用户树选项 */
    buildUserTreeOptions (users) {
      const userOptions = []
      if (users) {
        users.forEach(user => {
          const option = {
            key: user.userId,
            value: user.userId,
            title: `${user.userName}(${user.nickName})`,
            isLeaf: true
          }
          userOptions.push(option)
        })
      }
      return userOptions
    },
    /** 用户选择改变 */
    handleUserChange (value) {
      this.form.userId = value
    }
  }
}
</script>
