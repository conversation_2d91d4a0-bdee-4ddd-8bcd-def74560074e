<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="规则名称">
                <a-input v-model="queryParam.ruleName" placeholder="请输入规则名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select placeholder="请选择状态" style="width: 100%" allow-clear v-model="queryParam.status">
                  <a-select-option value="0">正常</a-select-option>
                  <a-select-option value="1">停用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="部门">
                  <a-tree-select
                    v-model="queryParam.deptId"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="deptOptions"
                    placeholder="请选择部门"
                    :replaceFields="{ children: 'children', title: 'title', key: 'key', value: 'key' }"
                    tree-default-expand-all
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="!advanced && 8 || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作 -->
      <div class="table-operations">
        <a-button type="primary" @click="$refs.createForm.handleAdd()" v-hasPermi="['attendance:rule:add']">
          <a-icon type="plus" />新增
        </a-button>
        <a-button type="primary" :disabled="single" @click="$refs.createForm.handleUpdate(undefined,ids)" v-hasPermi="['attendance:rule:edit']">
          <a-icon type="edit" />修改
        </a-button>
        <a-button type="danger" :disabled="multiple" @click="handleDelete" v-hasPermi="['attendance:rule:remove']">
          <a-icon type="delete" />删除
        </a-button>
        <a-button type="primary" @click="handleExport" v-hasPermi="['attendance:rule:export']">
          <a-icon type="download" />导出
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>

      <!-- 创建/编辑考勤规则 -->
      <create-form
        ref="createForm"
        :deptOptions="deptOptions"
        @ok="getList"
      />

      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="ruleId"
        :columns="columns"
        :data-source="list"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered">
        <span slot="deptName" slot-scope="text, record">
          {{ record.deptName || '全公司' }}
        </span>
        <span slot="workTime" slot-scope="text, record">
          {{ record.workStartTime }} - {{ record.workEndTime }}
        </span>
        <span slot="workDays" slot-scope="text, record">
          {{ formatWorkDays(record.workDays) }}
        </span>
        <span slot="status" slot-scope="text, record">
          <a-switch
            checked-children="启用"
            un-checked-children="停用"
            :checked="record.status == '0'"
            @change="handleStatusChange(record)"
          />
        </span>
        <span slot="operation" slot-scope="text, record">
          <a @click="$refs.createForm.handleUpdate(record,undefined)" v-hasPermi="['attendance:rule:edit']">
            <a-icon type="edit" />
            修改
          </a>
          <a-divider type="vertical" v-hasPermi="['attendance:rule:remove']" />
          <a @click="handleDelete(record)" v-hasPermi="['attendance:rule:remove']">
            <a-icon type="delete" />
            删除
          </a>
        </span>
      </a-table>
      <!-- 分页 -->
      <a-pagination
        class="ant-table-pagination"
        show-size-changer
        show-quick-jumper
        :current="queryParam.pageNum"
        :total="total"
        :page-size="queryParam.pageSize"
        :showTotal="total => `共 ${total} 条`"
        @showSizeChange="onShowSizeChange"
        @change="changeSize"
      />
    </a-card>
  </page-header-wrapper>
</template>

<script>
import { listAttendanceRule, delAttendanceRule, updateAttendanceRule } from '@/api/system/attendanceRule'
import { listDept } from '@/api/system/dept'
import CreateForm from './modules/CreateForm'
import { tableMixin } from '@/store/table-mixin'

export default {
  name: 'AttendanceRule',
  components: {
    CreateForm
  },
  mixins: [tableMixin],
  data () {
    return {
      list: [],
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      loading: false,
      total: 0,
      // 部门选项
      deptOptions: [],
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        ruleName: undefined,
        status: undefined,
        deptId: undefined
      },
      columns: [
        {
          title: '规则编号',
          dataIndex: 'ruleId',
          align: 'center'
        },
        {
          title: '规则名称',
          dataIndex: 'ruleName',
          align: 'center'
        },
        {
          title: '适用部门',
          dataIndex: 'deptName',
          scopedSlots: { customRender: 'deptName' },
          align: 'center'
        },
        {
          title: '工作时间',
          dataIndex: 'workTime',
          scopedSlots: { customRender: 'workTime' },
          align: 'center'
        },
        {
          title: '迟到阈值(分)',
          dataIndex: 'lateThreshold',
          align: 'center'
        },
        {
          title: '早退阈值(分)',
          dataIndex: 'earlyThreshold',
          align: 'center'
        },
        {
          title: '工作日',
          dataIndex: 'workDays',
          scopedSlots: { customRender: 'workDays' },
          align: 'center'
        },
        {
          title: '状态',
          dataIndex: 'status',
          scopedSlots: { customRender: 'status' },
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          scopedSlots: { customRender: 'operation' },
          align: 'center'
        }
      ]
    }
  },
  created () {
    this.getList()
    this.getDeptList()
  },
  methods: {
    /** 查询考勤规则列表 */
    getList () {
      this.loading = true
      listAttendanceRule(this.queryParam).then(response => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取部门列表 */
    getDeptList () {
      listDept().then(response => {
        this.deptOptions = this.buildDeptTreeOptions(response.data)
      })
    },
    /** 构建部门树选项 */
    buildDeptTreeOptions (depts) {
      const deptOptions = []
      if (depts) {
        depts.forEach(dept => {
          const option = {
            key: dept.deptId,
            value: dept.deptId,
            title: dept.deptName,
            children: dept.children ? this.buildDeptTreeOptions(dept.children) : []
          }
          deptOptions.push(option)
        })
      }
      return deptOptions
    },
    /** 格式化工作日 */
    formatWorkDays (workDays) {
      if (!workDays) return '-'
      const dayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
      const days = workDays.split(',').map(day => dayNames[parseInt(day)])
      return days.join('、')
    },
    /** 状态切换 */
    handleStatusChange (record) {
      const status = record.status === '0' ? '1' : '0'
      const text = status === '0' ? '启用' : '停用'
      this.$confirm({
        title: '确认' + text + '吗?',
        content: '确认要' + text + record.ruleName + '吗？',
        onOk: () => {
          updateAttendanceRule({ ...record, status: status }).then(() => {
            record.status = status
            this.$message.success(text + '成功')
          }).catch(() => {
            this.$message.error(text + '失败')
          })
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        ruleName: undefined,
        status: undefined,
        deptId: undefined
      }
      this.handleQuery()
    },
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.ruleId)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      const ruleIds = row.ruleId || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        content: '当前选中编号为' + ruleIds + '的数据',
        onOk () {
          return delAttendanceRule(ruleIds)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
            })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          that.download('system/attendanceRule/export', {
            ...that.queryParam
          }, `attendance_rule_${new Date().getTime()}.xlsx`)
        },
        onCancel () {}
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>