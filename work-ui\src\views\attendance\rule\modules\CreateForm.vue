<template>
  <a-modal
    :title="formTitle"
    :width="640"
    :visible="open"
    :confirmLoading="confirmLoading"
    @ok="submitForm"
    @cancel="cancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-model-item label="规则名称" prop="ruleName">
        <a-input v-model="form.ruleName" placeholder="请输入规则名称" />
      </a-form-model-item>
      <a-form-model-item label="适用部门" prop="deptId">
        <a-tree-select
          v-model="form.deptId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="deptOptions"
          placeholder="请选择部门(不选择表示全公司)"
          :replaceFields="{ children: 'children', title: 'title', key: 'key', value: 'key' }"
          tree-default-expand-all
          allow-clear
        />
      </a-form-model-item>
      <a-form-model-item label="上班时间" prop="workStartTime">
        <a-time-picker
          v-model="form.workStartTime"
          style="width: 100%"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="请选择上班时间"
        />
      </a-form-model-item>
      <a-form-model-item label="下班时间" prop="workEndTime">
        <a-time-picker
          v-model="form.workEndTime"
          style="width: 100%"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="请选择下班时间"
        />
      </a-form-model-item>
      <a-form-model-item label="迟到阈值" prop="lateThreshold">
        <a-input-number
          v-model="form.lateThreshold"
          style="width: 100%"
          :min="0"
          :max="120"
          placeholder="请输入迟到阈值(分钟)"
        />
      </a-form-model-item>
      <a-form-model-item label="早退阈值" prop="earlyThreshold">
        <a-input-number
          v-model="form.earlyThreshold"
          style="width: 100%"
          :min="0"
          :max="120"
          placeholder="请输入早退阈值(分钟)"
        />
      </a-form-model-item>
      <a-form-model-item label="工作日" prop="workDays">
        <a-checkbox-group v-model="form.workDaysArray" style="width: 100%">
          <a-row>
            <a-col :span="8">
              <a-checkbox :value="1">周一</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="2">周二</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="3">周三</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="4">周四</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="5">周五</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="6">周六</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="7">周日</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-form-model-item>
      <a-form-model-item label="状态" prop="status">
        <a-radio-group v-model="form.status">
          <a-radio value="0">正常</a-radio>
          <a-radio value="1">停用</a-radio>
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { getAttendanceRule, addAttendanceRule, updateAttendanceRule } from '@/api/system/attendanceRule'
import moment from 'moment'

export default {
  name: 'CreateForm',
  props: {
    deptOptions: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      confirmLoading: false,
      formTitle: '',
      // 表单参数
      form: {
        ruleId: undefined,
        ruleName: undefined,
        deptId: undefined,
        workStartTime: undefined,
        workEndTime: undefined,
        lateThreshold: 15,
        earlyThreshold: 15,
        workDays: undefined,
        workDaysArray: [1, 2, 3, 4, 5],
        status: '0'
      },
      open: false,
      rules: {
        ruleName: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
        workStartTime: [{ required: true, message: '上班时间不能为空', trigger: 'change' }],
        workEndTime: [{ required: true, message: '下班时间不能为空', trigger: 'change' }],
        lateThreshold: [{ required: true, message: '迟到阈值不能为空', trigger: 'blur' }],
        earlyThreshold: [{ required: true, message: '早退阈值不能为空', trigger: 'blur' }],
        workDaysArray: [{ required: true, message: '工作日不能为空', trigger: 'change' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
      }
    }
  },
  watch: {
    'form.workDaysArray': {
      handler (val) {
        this.form.workDays = val.join(',')
      },
      deep: true
    }
  },
  methods: {
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        ruleId: undefined,
        ruleName: undefined,
        deptId: undefined,
        workStartTime: undefined,
        workEndTime: undefined,
        lateThreshold: 15,
        earlyThreshold: 15,
        workDays: undefined,
        workDaysArray: [1, 2, 3, 4, 5],
        status: '0'
      }
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset()
      this.open = true
      this.formTitle = '添加考勤规则'
    },
    /** 修改按钮操作 */
    handleUpdate (row, ids) {
      this.reset()
      const ruleId = row ? row.ruleId : ids
      getAttendanceRule(ruleId).then(response => {
        this.form = response.data
        // 处理时间格式
        if (this.form.workStartTime) {
          this.form.workStartTime = moment(this.form.workStartTime, 'HH:mm:ss')
        }
        if (this.form.workEndTime) {
          this.form.workEndTime = moment(this.form.workEndTime, 'HH:mm:ss')
        }
        // 处理工作日数组
        if (this.form.workDays) {
          this.form.workDaysArray = this.form.workDays.split(',').map(day => parseInt(day))
        }
        this.open = true
        this.formTitle = '修改考勤规则'
      })
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          // 处理时间格式
          const formData = { ...this.form }
          if (formData.workStartTime) {
            formData.workStartTime = moment(formData.workStartTime).format('HH:mm:ss')
          }
          if (formData.workEndTime) {
            formData.workEndTime = moment(formData.workEndTime).format('HH:mm:ss')
          }
          // 处理工作日
          formData.workDays = formData.workDaysArray.join(',')
          delete formData.workDaysArray

          if (this.form.ruleId !== undefined) {
            updateAttendanceRule(formData).then(response => {
              this.$message.success(
                '修改成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.confirmLoading = false
            })
          } else {
            addAttendanceRule(formData).then(response => {
              this.$message.success(
                '新增成功',
                3
              )
              this.open = false
              this.$emit('ok')
            }).finally(() => {
              this.confirmLoading = false
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script> 