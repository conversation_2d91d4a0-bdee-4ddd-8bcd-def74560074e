<template>
  <a-modal
    title="请假申请详情"
    :visible="visible"
    :width="800"
    @ok="handleClose"
    @cancel="handleClose"
  >
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="申请单号">
        {{ detail.appNo }}
      </a-descriptions-item>
      <a-descriptions-item label="申请人">
        {{ detail.userName }}
      </a-descriptions-item>
      <a-descriptions-item label="部门">
        {{ detail.deptName }}
      </a-descriptions-item>
      <a-descriptions-item label="假期类型">
        {{ detail.leaveTypeName }}
      </a-descriptions-item>
      <a-descriptions-item label="开始日期">
        {{ detail.startDate }}
      </a-descriptions-item>
      <a-descriptions-item label="结束日期">
        {{ detail.endDate }}
      </a-descriptions-item>
      <a-descriptions-item label="请假天数">
        {{ detail.leaveDays }} 天
      </a-descriptions-item>
      <a-descriptions-item label="申请状态">
        <a-tag :color="getStatusColor(detail.status)">
          {{ getStatusText(detail.status) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="申请时间" :span="2">
        {{ detail.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="请假原因" :span="2">
        {{ detail.reason }}
      </a-descriptions-item>
      <a-descriptions-item label="证明文件" :span="2" v-if="detail.proofFile">
        <div class="proof-file-container">
          <a-button 
            type="link" 
            icon="file-text" 
            @click="previewFile(detail.proofFile)"
            style="padding: 0; margin-right: 8px;"
          >
            {{ getFileName(detail.proofFile) }}
          </a-button>
          <a-button 
            type="link" 
            icon="download" 
            @click="downloadFile(detail.proofFile)"
            size="small"
            style="padding: 0;"
          >
            下载
          </a-button>
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="审批人" v-if="detail.approverName">
        {{ detail.approverName }}
      </a-descriptions-item>
      <a-descriptions-item label="审批时间" v-if="detail.approveTime">
        {{ detail.approveTime }}
      </a-descriptions-item>
      <a-descriptions-item label="审批备注" :span="2" v-if="detail.approveRemark">
        {{ detail.approveRemark }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" :span="2" v-if="detail.remark">
        {{ detail.remark }}
      </a-descriptions-item>
    </a-descriptions>

    <template slot="footer">
      <a-button type="primary" @click="handleClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { getLeaveApplication } from '@/api/system/leaveApplication'

export default {
  name: 'ApplicationDetail',
  data() {
    return {
      visible: false,
      detail: {}
    }
  },
  methods: {
    /** 显示详情 */
    async show(id) {
      this.visible = true
      try {
        const response = await getLeaveApplication(id)
        this.detail = response.data
      } catch (error) {
        this.$message.error('获取详情失败')
        console.error('获取详情失败:', error)
      }
    },
    handleClose() {
      this.visible = false
      this.$emit('close')
    },
    /** 获取状态颜色 */
    getStatusColor(status) {
      const colorMap = {
        1: 'orange',   // 待审批
        2: 'green',    // 已通过
        3: 'red',      // 已拒绝
        4: 'gray'      // 已撤销
      }
      return colorMap[status] || 'default'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        1: '待审批',
        2: '已通过',
        3: '已拒绝',
        4: '已撤销'
      }
      return textMap[status] || '未知'
    },
    /** 获取文件名 */
    getFileName(filePath) {
      if (!filePath) return ''
      const fileName = filePath.split('/').pop() || filePath.split('\\').pop()
      // 如果文件名太长，截取显示
      if (fileName.length > 30) {
        const ext = fileName.split('.').pop()
        const name = fileName.substring(0, fileName.lastIndexOf('.'))
        return name.substring(0, 25) + '...' + (ext ? '.' + ext : '')
      }
      return fileName
    },
    /** 预览文件 */
    previewFile(filePath) {
      if (!filePath) return
      
      // 获取文件扩展名
      const ext = filePath.split('.').pop().toLowerCase()
      
      // 图片文件直接在新窗口打开
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
        window.open(filePath, '_blank')
      }
      // PDF文件在新窗口打开
      else if (ext === 'pdf') {
        window.open(filePath, '_blank')
      }
      // 其他文件类型提示下载
      else {
        this.$message.info('该文件类型不支持预览，请下载后查看')
        this.downloadFile(filePath)
      }
    },
    /** 下载文件 */
    downloadFile(filePath) {
      if (!filePath) return
      
      try {
        // 创建一个隐藏的下载链接
        const link = document.createElement('a')
        link.href = filePath
        link.download = this.getFileName(filePath)
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$message.success('文件下载已开始')
      } catch (error) {
        console.error('下载文件失败:', error)
        this.$message.error('文件下载失败')
      }
    }
  }
}
</script>

<style scoped>
.proof-file-container {
  display: flex;
  align-items: center;
}

.proof-file-container .ant-btn-link {
  color: #1890ff;
  text-decoration: none;
}

.proof-file-container .ant-btn-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}
</style>