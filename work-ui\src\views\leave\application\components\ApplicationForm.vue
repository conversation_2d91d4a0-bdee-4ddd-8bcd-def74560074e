<template>
  <a-modal
    :title="title"
    :visible="visible"
    :width="800"
    @ok="handleSubmit"
    @cancel="handleClose"
    :confirmLoading="loading"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-model-item label="假期类型" prop="leaveTypeId">
        <a-select
          v-model="form.leaveTypeId"
          placeholder="请选择假期类型"
          @change="handleTypeChange"
        >
          <a-select-option
            v-for="item in leaveTypeList"
            :key="item.id"
            :value="item.id"
          >
            {{ item.typeName }}
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item label="开始日期" prop="startDate">
        <a-date-picker
          v-model="form.startDate"
          placeholder="请选择开始日期"
          style="width: 100%"
          @change="calculateDays"
        />
      </a-form-model-item>

      <a-form-model-item label="结束日期" prop="endDate">
        <a-date-picker
          v-model="form.endDate"
          placeholder="请选择结束日期"
          style="width: 100%"
          @change="calculateDays"
        />
      </a-form-model-item>

      <a-form-model-item label="请假天数">
        <a-input-number
          v-model="form.leaveDays"
          :min="0.5"
          :max="selectedType ? selectedType.maxDays : 999"
          :step="0.5"
          :precision="1"
          style="width: 100%"
          placeholder="请假天数"
          disabled
        />
      </a-form-model-item>

      <a-form-model-item label="请假原因" prop="reason">
        <a-textarea
          v-model="form.reason"
          placeholder="请输入请假原因"
          :rows="4"
          :maxlength="500"
          show-count
        />
      </a-form-model-item>

      <a-form-model-item label="证明文件" v-if="selectedType && selectedType.needProof === 1">
        <file-upload
          ref="upload"
          :value="form.proofFile"
          :limit="1"
          :show-upload-list="true"
          @input="handleFileUpload"
        />
        <div style="color: #999; font-size: 12px; margin-top: 5px;">
          此假期类型需要上传证明文件
        </div>
      </a-form-model-item>

      <a-form-model-item label="备注">
        <a-textarea
          v-model="form.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-model-item>
    </a-form-model>

    <template slot="footer">
      <a-button @click="handleClose">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        {{ isEdit ? '修改' : '提交申请' }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getLeaveApplication, addLeaveApplication, updateLeaveApplication } from '@/api/system/leaveApplication'
import { listLeaveType } from '@/api/system/leaveType'
import moment from 'moment'

export default {
  name: 'ApplicationForm',
  data() {
    return {
      visible: false,
      isEdit: false,
      applicationId: null,
      loading: false,
      leaveTypeList: [],
      selectedType: null,
      form: {
        id: null,
        leaveTypeId: null,
        startDate: null,
        endDate: null,
        leaveDays: 0,
        reason: '',
        proofFile: '',
        remark: ''
      },
      rules: {
        leaveTypeId: [
          { required: true, message: '请选择假期类型', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入请假原因', trigger: 'blur' },
          { min: 10, max: 500, message: '请假原因长度在 10 到 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    title() {
      return this.isEdit ? '修改请假申请' : '新增请假申请'
    }
  },
  watch: {
    async visible(val) {
      if (val) {
        await this.getLeaveTypeList()
        if (this.isEdit && this.applicationId) {
          this.getApplicationDetail()
        } else {
          this.resetForm()
        }
      }
    }
  },
  methods: {
    /** 显示表单 */
    show(row = null) {
      this.visible = true
      if (row) {
        this.isEdit = true
        this.applicationId = row.id
      } else {
        this.isEdit = false
        this.applicationId = null
      }
    },
    // 获取假期类型列表
    async getLeaveTypeList() {
      try {
        const response = await listLeaveType({ status: 1 })
        this.leaveTypeList = response.rows || []
      } catch (error) {
        console.error('获取假期类型失败:', error)
      }
    },

    // 获取申请详情
    async getApplicationDetail() {
      try {
        this.loading = true
        const response = await getLeaveApplication(this.applicationId)
        const data = response.data
        this.form = {
          id: data.id,
          leaveTypeId: data.leaveTypeId,
          startDate: data.startDate ? moment(data.startDate) : null,
          endDate: data.endDate ? moment(data.endDate) : null,
          leaveDays: data.leaveDays,
          reason: data.reason,
          proofFile: data.proofFile || '',
          remark: data.remark || ''
        }
        // 设置选中的假期类型
        this.selectedType = this.leaveTypeList.find(item => item.id === data.leaveTypeId)
        
        // 如果有证明文件，需要设置到文件上传组件中
        this.$nextTick(() => {
          if (this.$refs.upload) {
            if (data.proofFile) {
              this.$refs.upload.setValue(data.proofFile)
            } else {
              this.$refs.upload.clearFiles()
            }
          }
        })
      } catch (error) {
        this.$message.error('获取申请详情失败')
      } finally {
        this.loading = false
      }
    },

    // 假期类型变化
    handleTypeChange(leaveTypeId) {
      this.selectedType = this.leaveTypeList.find(item => item.id === leaveTypeId)
      this.calculateDays()
    },

    // 计算请假天数
    calculateDays() {
      if (this.form.startDate && this.form.endDate) {
        const startDate = moment(this.form.startDate)
        const endDate = moment(this.form.endDate)
        
        if (endDate.isBefore(startDate)) {
          this.$message.warning('结束日期不能早于开始日期')
          this.form.endDate = null
          this.form.leaveDays = 0
          return
        }

        // 计算天数（包含开始和结束日期）
        const days = endDate.diff(startDate, 'days') + 1
        this.form.leaveDays = days

        // 检查是否超过最大天数
        if (this.selectedType && this.selectedType.maxDays && days > this.selectedType.maxDays) {
          this.$message.warning(`该假期类型最多只能请${this.selectedType.maxDays}天`)
        }

        // 检查提前申请天数
        if (this.selectedType && this.selectedType.advanceDays) {
          const advanceDays = moment().add(this.selectedType.advanceDays, 'days')
          if (startDate.isBefore(advanceDays)) {
            this.$message.warning(`该假期类型需要提前${this.selectedType.advanceDays}天申请`)
          }
        }
      } else {
        this.form.leaveDays = 0
      }
    },

    // 文件上传
    handleFileUpload(url) {
      this.form.proofFile = url || ''
      // 确保上传组件状态正确更新
      if (this.$refs.upload) {
        this.$refs.upload.loading = false
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return

        // 额外验证
        if (this.selectedType) {
          if (this.selectedType.needProof === 1 && !this.form.proofFile) {
            this.$message.error('该假期类型需要上传证明文件')
            return
          }
          if (this.selectedType.maxDays && this.form.leaveDays > this.selectedType.maxDays) {
            this.$message.error(`该假期类型最多只能请${this.selectedType.maxDays}天`)
            return
          }
        }

        try {
          this.loading = true
          const formData = {
            ...this.form,
            startDate: this.form.startDate ? this.form.startDate.format('YYYY-MM-DD 00:00:00') : null,
            endDate: this.form.endDate ? this.form.endDate.format('YYYY-MM-DD 23:59:59') : null
          }

          if (this.isEdit) {
            await updateLeaveApplication(formData)
            this.$message.success('修改成功')
          } else {
            await addLeaveApplication(formData)
            this.$message.success('申请提交成功')
          }

          this.$emit('success')
          this.handleClose()
        } catch (error) {
          console.error('提交失败:', error)
        } finally {
          this.loading = false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.form = {
        id: null,
        leaveTypeId: null,
        startDate: null,
        endDate: null,
        leaveDays: 0,
        reason: '',
        proofFile: '',
        remark: ''
      }
      this.selectedType = null
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.resetForm()
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
