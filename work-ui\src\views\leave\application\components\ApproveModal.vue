<template>
  <a-modal
    title="请假申请审批"
    :visible="visible"
    :width="700"
    @ok="handleSubmit"
    @cancel="handleClose"
    :confirmLoading="loading"
  >
    <!-- 申请信息展示 -->
    <a-descriptions title="申请信息" :column="2" bordered>
      <a-descriptions-item label="申请单号">
        {{ application.appNo }}
      </a-descriptions-item>
      <a-descriptions-item label="申请人">
        {{ application.userName }}
      </a-descriptions-item>
      <a-descriptions-item label="部门">
        {{ application.deptName }}
      </a-descriptions-item>
      <a-descriptions-item label="假期类型">
        {{ application.leaveTypeName }}
      </a-descriptions-item>
      <a-descriptions-item label="开始日期">
        {{ application.startDate }}
      </a-descriptions-item>
      <a-descriptions-item label="结束日期">
        {{ application.endDate }}
      </a-descriptions-item>
      <a-descriptions-item label="请假天数">
        {{ application.leaveDays }} 天
      </a-descriptions-item>
      <a-descriptions-item label="申请时间">
        {{ application.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="请假原因" :span="2">
        {{ application.reason }}
      </a-descriptions-item>
      <a-descriptions-item label="证明文件" :span="2" v-if="application.proofFile">
        <a :href="application.proofFile" target="_blank">
          <a-icon type="file" /> 查看证明文件
        </a>
      </a-descriptions-item>
    </a-descriptions>

    <a-divider />

    <!-- 审批表单 -->
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-model-item label="审批结果" prop="approveResult">
        <a-radio-group v-model="form.approveResult">
          <a-radio value="2">
            <a-icon type="check-circle" style="color: #52c41a" />
            通过
          </a-radio>
          <a-radio value="3">
            <a-icon type="close-circle" style="color: #ff4d4f" />
            拒绝
          </a-radio>
        </a-radio-group>
      </a-form-model-item>

      <a-form-model-item 
        label="审批意见" 
        prop="approveRemark"
        :rules="form.approveResult === '3' ? rules.approveRemark : []"
      >
        <a-textarea
          v-model="form.approveRemark"
          :placeholder="form.approveResult === '3' ? '拒绝原因（必填）' : '审批意见（可选）'"
          :rows="4"
          :maxlength="500"
          show-count
        />
      </a-form-model-item>
    </a-form-model>

    <template slot="footer">
      <a-button @click="handleClose">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        提交审批
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { approveLeaveApplication } from '@/api/system/leaveApplication'

export default {
  name: 'ApproveModal',
  data() {
    return {
      visible: false,
      loading: false,
      application: {},
      form: {
        approveResult: '2',
        approveRemark: ''
      },
      rules: {
        approveResult: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        approveRemark: [
          { required: true, message: '拒绝时必须填写拒绝原因', trigger: 'blur' },
          { min: 5, max: 500, message: '拒绝原因长度在 5 到 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
      }
    },
    'form.approveResult'(val) {
      // 当审批结果改为通过时，清除审批意见的验证错误
      if (val === '2' && this.$refs.form) {
        this.$refs.form.clearValidate(['approveRemark'])
      }
    }
  },
  methods: {
    /** 显示审批弹窗 */
    show(row) {
      this.visible = true
      this.application = row
    },
    // 提交审批
    handleSubmit() {
      // 动态验证规则
      const validateFields = ['approveResult']
      if (this.form.approveResult === '3') {
        validateFields.push('approveRemark')
      }

      this.$refs.form.validateField(validateFields, async (errorMessage) => {
        if (errorMessage) return

        try {
          this.loading = true
          await approveLeaveApplication(
            this.application.id,
            this.form.approveResult,
            this.form.approveRemark
          )

          const resultText = this.form.approveResult === '2' ? '通过' : '拒绝'
          this.$message.success(`审批${resultText}成功`)
          
          this.$emit('success')
          this.handleClose()
        } catch (error) {
          this.$message.error('审批失败，请重试')
          console.error('审批失败:', error)
        } finally {
          this.loading = false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.form = {
        approveResult: '2',
        approveRemark: ''
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.resetForm()
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.ant-descriptions {
  margin-bottom: 0;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-radio-wrapper {
  display: flex;
  align-items: center;
  margin-right: 24px;
  margin-bottom: 8px;
}

.ant-radio-wrapper .anticon {
  margin-right: 4px;
}
</style> 