<template>
  <div>
    <!-- 条件搜索 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="8" :sm="24">
            <a-form-item label="申请单号">
              <a-input 
                v-model="queryParams.appNo" 
                placeholder="请输入申请单号" 
                allow-clear 
                @pressEnter="handleQuery"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24" v-if="listType === 'all'">
            <a-form-item label="申请人">
              <a-input 
                v-model="queryParams.userName" 
                placeholder="请输入申请人姓名" 
                allow-clear 
                @pressEnter="handleQuery"
              />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="24">
            <a-form-item label="假期类型">
              <a-select 
                v-model="queryParams.leaveTypeId" 
                placeholder="请选择假期类型" 
                allow-clear 
                style="width: 100%"
              >
                <a-select-option 
                  v-for="type in leaveTypeOptions" 
                  :key="type.id" 
                  :value="type.id"
                >
                  {{ type.typeName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <template v-if="advanced">
            <a-col :md="8" :sm="24" v-if="listType !== 'pending'">
              <a-form-item label="状态">
                <a-select 
                  v-model="queryParams.status" 
                  placeholder="请选择状态" 
                  allow-clear 
                  style="width: 100%"
                >
                  <a-select-option :value="1">待审批</a-select-option>
                  <a-select-option :value="2">已通过</a-select-option>
                  <a-select-option :value="3">已拒绝</a-select-option>
                  <a-select-option :value="4">已撤销</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="申请时间">
                <a-range-picker 
                  v-model="dateRange" 
                  style="width: 100%"
                  @change="handleDateRangeChange"
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="!advanced && 8 || 24" :sm="24">
            <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
              <a-button type="primary" @click="handleQuery">
                <a-icon type="search" />查询
              </a-button>
              <a-button style="margin-left: 8px" @click="resetQuery">
                <a-icon type="redo" />重置
              </a-button>
              <a @click="toggleAdvanced" style="margin-left: 8px">
                {{ advanced ? '收起' : '展开' }}
                <a-icon :type="advanced ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作 -->
    <div class="table-operations">
      <a-button 
        type="primary" 
        @click="handleAdd"
        v-hasPermi="['system:leaveApplication:add']"
        v-if="listType === 'my'"
      >
        <a-icon type="plus" />新增申请
      </a-button>
      <a-button 
        type="danger" 
        :disabled="multiple" 
        @click="handleDelete"
        v-hasPermi="['system:leaveApplication:remove']"
        v-if="listType !== 'pending'"
      >
        <a-icon type="delete" />删除
      </a-button>
      <a-button 
        type="primary" 
        @click="handleExport"
        v-hasPermi="['system:leaveApplication:export']"
      >
        <a-icon type="download" />导出
      </a-button>
      <table-setting
        :style="{float: 'right'}"
        :table-size.sync="tableSize"
        v-model="columns"
        :refresh-loading="loading"
        @refresh="getList" />
    </div>

    <!-- 数据展示 -->
    <a-table
      :loading="loading"
      :size="tableSize"
      rowKey="id"
      :columns="columns"
      :data-source="applicationList"
      :row-selection="rowSelection"
      :pagination="false"
      :bordered="tableBordered">
      <span slot="status" slot-scope="text, record">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </span>
              <span slot="action" slot-scope="text, record">
          <a @click="handleView(record)">查看</a>
          <a-divider type="vertical" />
          
          <!-- 修改按钮 -->
          <template v-if="canEdit(record)">
            <a @click="handleUpdate(record)" v-hasPermi="['system:leaveApplication:edit']">修改</a>
            <a-divider type="vertical" />
          </template>
          
          <!-- 撤销按钮 -->
          <template v-if="canCancel(record)">
            <a @click="handleCancel(record)" v-hasPermi="['system:leaveApplication:cancel']">撤销</a>
            <a-divider type="vertical" />
          </template>
          
          <!-- 审批按钮 -->
          <template v-if="canApprove(record)">
            <a @click="handleApprove(record)" v-hasPermi="['system:leaveApplication:approve']">审批</a>
            <a-divider type="vertical" />
          </template>
          
          <!-- 删除按钮 -->
          <template v-if="canDelete(record)">
            <a @click="handleDelete(record)" v-hasPermi="['system:leaveApplication:remove']">删除</a>
          </template>
        </span>
    </a-table>
    <!-- 分页 -->
    <a-pagination
      class="ant-table-pagination"
      show-size-changer
      show-quick-jumper
      :current="queryParams.pageNum"
      :total="total"
      :page-size="queryParams.pageSize"
      :showTotal="total => `共 ${total} 条`"
      @showSizeChange="onShowSizeChange"
      @change="changeSize"
    />

    <!-- 申请表单弹窗 -->
    <application-form
      ref="applicationForm"
      @success="getList"
    />

    <!-- 详情查看弹窗 -->
    <application-detail
      ref="applicationDetail"
    />

    <!-- 审批弹窗 -->
    <approve-modal
      ref="approveModal"
      @success="getList"
    />
  </div>
</template>

<script>
import { 
  listLeaveApplication, 
  myListLeaveApplication,
  pendingListLeaveApplication,
  delLeaveApplication,
  cancelLeaveApplication,
  checkCanDelete,
  checkCanEdit
} from '@/api/system/leaveApplication'
import { optionSelectLeaveType } from '@/api/system/leaveType'
import { tableMixin } from '@/store/table-mixin'
import ApplicationForm from './ApplicationForm.vue'
import ApplicationDetail from './ApplicationDetail.vue'
import ApproveModal from './ApproveModal.vue'

export default {
  name: 'LeaveApplicationList',
  components: {
    ApplicationForm,
    ApplicationDetail,
    ApproveModal
  },
  mixins: [tableMixin],
  props: {
    listType: {
      type: String,
      default: 'all', // all, my, pending
      required: true
    }
  },
  emits: ['refresh'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      total: 0,
      // 申请表格数据
      applicationList: [],
      // 假期类型选项
      leaveTypeOptions: [],
      // 日期范围
      dateRange: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appNo: undefined,
        userName: undefined,
        leaveTypeId: undefined,
        status: undefined
      },
      // 表格列定义
      columns: [
        {
          title: '申请单号',
          dataIndex: 'appNo',
          key: 'appNo',
          width: 150,
          align: 'center'
        },
        {
          title: '假期类型',
          dataIndex: 'leaveTypeName',
          key: 'leaveTypeName',
          width: 100,
          align: 'center'
        },
        {
          title: '开始时间',
          dataIndex: 'startDate',
          key: 'startDate',
          width: 160,
          align: 'center'
        },
        {
          title: '结束时间',
          dataIndex: 'endDate',
          key: 'endDate',
          width: 160,
          align: 'center'
        },
        {
          title: '请假天数',
          dataIndex: 'leaveDays',
          key: 'leaveDays',
          width: 100,
          align: 'center'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          align: 'center'
        },
        {
          title: '申请时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          width: 200,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          align: 'center'
        }
      ]
    }
  },
  computed: {
    rowSelection() {
      if (this.listType === 'pending') {
        return null // 待审批页面不显示多选
      }
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange
      }
    }
  },
  created() {
    this.getList()
    this.getLeaveTypeOptions()
    this.updateColumns()
  },
  methods: {
    /** 显示组件 */
    show() {
      this.getList()
      this.getLeaveTypeOptions()
    },
    /** 更新表格列 */
    updateColumns() {
      // 根据列表类型添加申请人列
      if (this.listType === 'all' || this.listType === 'pending') {
        this.columns.splice(1, 0, {
          title: '申请人',
          dataIndex: 'userName',
          key: 'userName',
          width: 100,
          align: 'center'
        })
        this.columns.splice(2, 0, {
          title: '部门',
          dataIndex: 'deptName',
          key: 'deptName',
          width: 120,
          align: 'center'
        })
      }
    },
    /** 查询申请列表 */
    getList() {
      this.loading = true
      let apiCall
      switch (this.listType) {
        case 'all':
          apiCall = listLeaveApplication
          break
        case 'my':
          apiCall = myListLeaveApplication
          break
        case 'pending':
          apiCall = pendingListLeaveApplication
          break
        default:
          apiCall = listLeaveApplication
      }

      apiCall(this.queryParams).then(response => {
        this.applicationList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取假期类型选项 */
    getLeaveTypeOptions() {
      optionSelectLeaveType().then(response => {
        this.leaveTypeOptions = response.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        appNo: undefined,
        userName: undefined,
        leaveTypeId: undefined,
        status: undefined
      }
      this.dateRange = null
      this.handleQuery()
    },
    /** 多选框选中数据 */
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.id)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.applicationForm.show()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.applicationForm.show(row)
    },
    /** 查看详情 */
    handleView(row) {
      this.$refs.applicationDetail.show(row.id)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.selectedRowKeys
      this.$confirm({
        title: '警告',
        content: '是否确认删除选中的申请记录？',
        onOk: () => {
          delLeaveApplication(ids).then(() => {
            this.getList()
            this.$message.success('删除成功')
          })
        }
      })
    },
    /** 撤销申请 */
    handleCancel(row) {
      this.$confirm({
        title: '确认',
        content: '是否确认撤销该申请？',
        onOk: () => {
          cancelLeaveApplication(row.id).then(() => {
            this.getList()
            this.$message.success('撤销成功')
            this.$emit('refresh')
          })
        }
      })
    },
    /** 审批申请 */
    handleApprove(row) {
      this.$refs.approveModal.show(row)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk: () => {
          this.download('system/leaveApplication/export', {
            ...this.queryParams
          }, `请假申请_${new Date().getTime()}.xlsx`)
        }
      })
    },
    /** 分页大小改变 */
    onShowSizeChange(current, pageSize) {
      this.queryParams.pageSize = pageSize
      this.getList()
    },
    /** 分页改变 */
    changeSize(current, pageSize) {
      this.queryParams.pageNum = current
      this.queryParams.pageSize = pageSize
      this.getList()
    },
    /** 日期范围变化 */
    handleDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.params = {
          beginTime: dates[0].format('YYYY-MM-DD'),
          endTime: dates[1].format('YYYY-MM-DD')
        }
      } else {
        this.queryParams.params = {}
      }
    },
    /** 获取状态颜色 */
    getStatusColor(status) {
      const colorMap = {
        1: 'orange',   // 待审批
        2: 'green',    // 已通过
        3: 'red',      // 已拒绝
        4: 'gray'      // 已撤销
      }
      return colorMap[status] || 'default'
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        1: '待审批',
        2: '已通过',
        3: '已拒绝',
        4: '已撤销'
      }
      return textMap[status] || '未知'
    },
    /** 是否可以编辑 */
    canEdit(record) {
      return this.listType === 'my' && record.status === 1
    },
    /** 是否可以撤销 */
    canCancel(record) {
      return this.listType === 'my' && record.status === 1
    },
    /** 是否可以审批 */
    canApprove(record) {
      return this.listType === 'pending' && record.status === 1
    },
    /** 是否可以删除 */
    canDelete(record) {
      return this.listType === 'my' && (record.status === 1 || record.status === 4)
    },
    /** 权限检查方法 */
    hasPermi(permissions) {
      const auth = require('@/plugins/auth').default
      return auth.hasPermi(permissions)
    }
  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 16px;
}
</style>
