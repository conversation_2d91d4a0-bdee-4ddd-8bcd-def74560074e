<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="all" tab="全部申请" v-if="hasPermi(['system:leaveApplication:list'])">
          <leave-application-list 
            ref="allList"
            :list-type="'all'"
            @refresh="refreshCurrentTab"
          />
        </a-tab-pane>
        <a-tab-pane key="my" tab="我的申请">
          <leave-application-list 
            ref="myList"
            :list-type="'my'"
            @refresh="refreshCurrentTab"
          />
        </a-tab-pane>
        <a-tab-pane key="pending" tab="待我审批" v-if="hasPermi(['system:leaveApplication:approve'])">
          <leave-application-list 
            ref="pendingList"
            :list-type="'pending'"
            @refresh="refreshCurrentTab"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </page-header-wrapper>
</template>

<script>
import LeaveApplicationList from './components/LeaveApplicationList.vue'
import auth from '@/plugins/auth'

export default {
  name: 'LeaveApplication',
  components: {
    LeaveApplicationList
  },
  data() {
    return {
      activeTab: 'my'
    }
  },
  mounted() {
    // 根据权限设置默认tab
    if (this.hasPermi(['system:leaveApplication:list'])) {
      this.activeTab = 'all'
    } else {
      this.activeTab = 'my'
    }
  },
  methods: {
    hasPermi(permissions) {
      return auth.hasPermi(permissions)
    },
    handleTabChange(key) {
      this.activeTab = key
      this.refreshCurrentTab()
    },
    refreshCurrentTab() {
      this.$nextTick(() => {
        const currentRef = this.$refs[this.activeTab + 'List']
        if (currentRef) {
          currentRef.getList()
        }
      })
    }
  }
}
</script>

<style scoped>
</style> 