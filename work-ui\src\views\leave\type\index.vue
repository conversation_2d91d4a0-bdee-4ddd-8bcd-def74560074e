<template>
  <page-header-wrapper>
    <a-card :bordered="false">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="假期名称">
                <a-input
                  v-model="queryParams.typeName"
                  placeholder="请输入假期名称"
                  allow-clear
                  @pressEnter="handleQuery"
                />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="假期代码">
                <a-input
                  v-model="queryParams.typeCode"
                  placeholder="请输入假期代码"
                  allow-clear
                  @pressEnter="handleQuery"
                />
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="状态">
                  <a-select
                    v-model="queryParams.status"
                    placeholder="请选择状态"
                    allow-clear
                    style="width: 100%"
                  >
                    <a-select-option :value="1">正常</a-select-option>
                    <a-select-option :value="0">停用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="!advanced && 8 || 24" :sm="24">
              <span class="table-page-search-submitButtons" :style="advanced && { float: 'right', overflow: 'hidden' } || {} ">
                <a-button type="primary" @click="handleQuery">
                  <a-icon type="search" />查询
                </a-button>
                <a-button style="margin-left: 8px" @click="resetQuery">
                  <a-icon type="redo" />重置
                </a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作 -->
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd" v-hasPermi="['system:leaveType:add']">
          <a-icon type="plus" />新增
        </a-button>
        <a-button type="primary" :disabled="single" @click="handleUpdate(null,ids)" v-hasPermi="['system:leaveType:edit']">
          <a-icon type="edit" />修改
        </a-button>
        <a-button type="danger" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:leaveType:remove']">
          <a-icon type="delete" />删除
        </a-button>
        <a-button type="primary" @click="handleExport" v-hasPermi="['system:leaveType:export']">
          <a-icon type="download" />导出
        </a-button>
        <table-setting
          :style="{float: 'right'}"
          :table-size.sync="tableSize"
          v-model="columns"
          :refresh-loading="loading"
          @refresh="getList" />
      </div>

      <!-- 数据展示 -->
      <a-table
        :loading="loading"
        :size="tableSize"
        rowKey="id"
        :columns="columns"
        :data-source="leaveTypeList"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :bordered="tableBordered">
        <span slot="isPaid" slot-scope="text, record">
          <dict-tag v-if="dict.type.yes_no && dict.type.yes_no.length > 0" :options="dict.type.yes_no" :value="record.isPaid" />
          <span v-else>{{ record.isPaid === 1 ? '是' : '否' }}</span>
        </span>
        <span slot="needProof" slot-scope="text, record">
          <dict-tag v-if="dict.type.yes_no && dict.type.yes_no.length > 0" :options="dict.type.yes_no" :value="record.needProof" />
          <span v-else>{{ record.needProof === 1 ? '是' : '否' }}</span>
        </span>
        <span slot="status" slot-scope="text, record">
          <a-switch
            checked-children="启用"
            un-checked-children="停用"
            :checked="record.status == '1'"
            @change="handleStatusChange(record)"
          />
        </span>
        <span slot="operation" slot-scope="text, record">
          <a @click="handleUpdate(record,null)" v-hasPermi="['system:leaveType:edit']">
            <a-icon type="edit" />
            修改
          </a>
          <a-divider type="vertical" v-hasPermi="['system:leaveType:remove']" />
          <a @click="handleDelete(record)" v-hasPermi="['system:leaveType:remove']">
            <a-icon type="delete" />
            删除
          </a>
        </span>
      </a-table>

      <!-- 分页 -->
      <a-pagination
        class="ant-table-pagination"
        show-size-changer
        show-quick-jumper
        :current="queryParams.pageNum"
        :total="total"
        :page-size="queryParams.pageSize"
        :showTotal="total => `共 ${total} 条`"
        @showSizeChange="onShowSizeChange"
        @change="changeSize"
      />
    </a-card>

    <!-- 添加或修改假期类型对话框 -->
    <a-modal
      :title="title"
      :visible="open"
      :width="600"
      @cancel="cancel"
      @ok="submitForm"
      :confirmLoading="confirmLoading"
    >
              <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
        <a-form-model-item label="假期名称" prop="typeName">
          <a-input v-model="form.typeName" placeholder="请输入假期名称" @blur="generateTypeCode" />
        </a-form-model-item>
        <a-form-model-item label="假期代码" prop="typeCode">
          <a-input v-model="form.typeCode" placeholder="系统自动生成" disabled />
        </a-form-model-item>
        <a-form-model-item label="最大天数" prop="maxDays">
          <a-input-number
            v-model="form.maxDays"
            placeholder="请输入最大天数"
            :min="0"
            :precision="1"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item label="是否带薪" prop="isPaid">
          <a-radio-group v-model="form.isPaid">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="需要证明" prop="needProof">
          <a-radio-group v-model="form.needProof">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="提前申请天数" prop="advanceDays">
          <a-input-number
            v-model="form.advanceDays"
            placeholder="请输入需提前申请天数"
            :min="0"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item label="状态" prop="status">
          <a-radio-group v-model="form.status">
            <a-radio :value="1">正常</a-radio>
            <a-radio :value="0">停用</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="备注" prop="remark">
          <a-textarea v-model="form.remark" placeholder="请输入备注" :rows="3" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </page-header-wrapper>
</template>

<script>
import {
  listLeaveType,
  getLeaveType,
  delLeaveType,
  addLeaveType,
  updateLeaveType,
  checkTypeCodeUnique
} from '@/api/system/leaveType'
import { tableMixin } from '@/store/table-mixin'

export default {
  name: 'LeaveType',
  dicts: ['yes_no', 'sys_normal_disable'],
  mixins: [tableMixin],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      selectedRowKeys: [],
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      ids: [],
      total: 0,
      // 假期类型表格数据
      leaveTypeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 确认加载中
      confirmLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeName: undefined,
        typeCode: undefined,
        status: undefined
      },
      // 分页
      pagination: {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `显示 ${range[0]} 到 ${range[1]} 条，共 ${total} 条`,
        current: 1,
        pageSize: 10,
        total: 0
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        typeName: [
          { required: true, message: '假期名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '假期名称长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '假期代码不能为空', trigger: 'blur' }
        ],
        isPaid: [
          { required: true, message: '是否带薪不能为空', trigger: 'change' }
        ],
        needProof: [
          { required: true, message: '是否需要证明不能为空', trigger: 'change' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ]
      },
      // 表格列定义
      columns: [
        {
          title: '假期名称',
          dataIndex: 'typeName',
          key: 'typeName',
          width: 120,
          ellipsis: true,
          align: 'center'
        },

        {
          title: '最大天数',
          dataIndex: 'maxDays',
          key: 'maxDays',
          width: 100,
          align: 'center'
        },
        {
          title: '是否带薪',
          dataIndex: 'isPaid',
          key: 'isPaid',
          width: 100,
          scopedSlots: { customRender: 'isPaid' },
          align: 'center'
        },
        {
          title: '需要证明',
          dataIndex: 'needProof',
          key: 'needProof',
          width: 100,
          scopedSlots: { customRender: 'needProof' },
          align: 'center'
        },
        {
          title: '提前申请天数',
          dataIndex: 'advanceDays',
          key: 'advanceDays',
          width: 120,
          align: 'center'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
          scopedSlots: { customRender: 'status' },
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 180,
          fixed: 'right',
          scopedSlots: { customRender: 'operation' },
          align: 'center'
        }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询假期类型列表 */
    getList() {
      this.loading = true
      listLeaveType(this.queryParams).then(response => {
        this.leaveTypeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        typeName: undefined,
        typeCode: undefined,
        status: undefined
      }
      this.handleQuery()
    },
    /** 多选框选中数据 */
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.id)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加假期类型'
    },
    /** 根据假期名称自动生成假期代码 */
    generateTypeCode() {
      if (this.form.typeName && !this.form.id) {
        // 将中文转换为拼音首字母
        const pinyinMap = {
          '年': 'N', '假': 'J', '病': 'B', '事': 'S', '婚': 'H', '丧': 'S', '产': 'C', '陪': 'P', '探': 'T', '调': 'D', '补': 'B', '休': 'X',
          '年假': 'NJ', '病假': 'BJ', '事假': 'SJ', '婚假': 'HJ', '丧假': 'SJ', '产假': 'CJ', '陪产假': 'PCJ', '探亲假': 'TQJ', '调休': 'DX', '补休': 'BX', '休息': 'XX'
        }

        let code = this.form.typeName
        // 先尝试完整匹配
        if (pinyinMap[this.form.typeName]) {
          code = pinyinMap[this.form.typeName]
        } else {
          // 逐字转换
          code = this.form.typeName.split('').map(char => pinyinMap[char] || char).join('')
        }

        // 如果转换后还是中文，取前两个字符的拼音首字母
        if (/[\u4e00-\u9fa5]/.test(code)) {
          code = this.form.typeName.substring(0, 2).split('').map(char => pinyinMap[char] || char).join('')
        }

        this.form.typeCode = code.toUpperCase()
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row, ids) {
      this.reset()
      const id = (row && row.id) || (ids && ids[0])
      if (!id) {
        this.$message.warning('请选择要修改的数据')
        return
      }
      getLeaveType(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改假期类型'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          if (this.form.id != null) {
            updateLeaveType(this.form).then(response => {
              this.$message.success('修改成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.confirmLoading = false
            })
          } else {
            addLeaveType(this.form).then(response => {
              this.$message.success('新增成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.confirmLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that = this
      const ids = row.id || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        content: '当前选中编号为' + ids + '的数据',
        onOk () {
          return delLeaveType(ids)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
            })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          that.download('system/leaveType/export', {
            ...that.queryParams
          }, `假期类型_${new Date().getTime()}.xlsx`)
        },
        onCancel () {}
      })
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    /** 状态切换 */
    handleStatusChange (record) {
      const status = record.status === '1' ? '0' : '1'
      const text = status === '1' ? '启用' : '停用'
      this.$confirm({
        title: '确认' + text + '吗?',
        content: '确认要' + text + record.typeName + '吗？',
        onOk: () => {
          updateLeaveType({ ...record, status: status }).then(() => {
            record.status = status
            this.$message.success(text + '成功')
          }).catch(() => {
            this.$message.error(text + '失败')
          })
        }
      })
    },
    /** 分页大小改变 */
    onShowSizeChange(current, pageSize) {
      this.queryParams.pageSize = pageSize
      this.getList()
    },
    /** 分页改变 */
    changeSize(current, pageSize) {
      this.queryParams.pageNum = current
      this.queryParams.pageSize = pageSize
      this.getList()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        typeName: null,
        typeCode: null,
        maxDays: null,
        isPaid: 1,
        needProof: 0,
        advanceDays: 0,
        status: 1,
        remark: null
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },

  }
}
</script>

<style scoped>
.query-form {
  margin-bottom: 16px;
}
</style>
