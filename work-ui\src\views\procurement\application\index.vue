<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <a-form :model="queryParams" layout="inline" v-show="showSearch">
      <a-form-item label="申请单号">
        <a-input
          v-model="queryParams.appNo"
          placeholder="请输入申请单号"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="申请标题">
        <a-input
          v-model="queryParams.title"
          placeholder="请输入申请标题"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="申请人">
        <a-input
          v-model="queryParams.applicantName"
          placeholder="请输入申请人姓名"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="采购类型">
        <a-select v-model="queryParams.procurementType" placeholder="请选择采购类型" allow-clear style="width: 120px">
          <a-select-option value="OFFICE">办公用品</a-select-option>
          <a-select-option value="IT">IT设备</a-select-option>
          <a-select-option value="MATERIAL">原材料</a-select-option>
          <a-select-option value="SERVICE">服务</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="状态">
        <a-select v-model="queryParams.status" placeholder="请选择状态" allow-clear style="width: 100px">
          <a-select-option value="1">草稿</a-select-option>
          <a-select-option value="2">待审批</a-select-option>
          <a-select-option value="3">审批中</a-select-option>
          <a-select-option value="4">已通过</a-select-option>
          <a-select-option value="5">已拒绝</a-select-option>
          <a-select-option value="6">已采购</a-select-option>
          <a-select-option value="7">已入库</a-select-option>
          <a-select-option value="8">已报销</a-select-option>
          <a-select-option value="9">已关闭</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" icon="search" @click="handleQuery">搜索</a-button>
        <a-button icon="reload" @click="resetQuery" style="margin-left: 8px">重置</a-button>
      </a-form-item>
    </a-form>

    <!-- 操作按钮 -->
    <div class="table-operations" style="margin-bottom: 16px;">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-hasPermi="['system:procurement:add']"
      >新增</a-button>
      <a-button
        icon="edit"
        :disabled="single"
        @click="handleUpdate"
        v-hasPermi="['system:procurement:edit']"
        style="margin-left: 8px"
      >修改</a-button>
      <a-button
        type="danger"
        icon="delete"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['system:procurement:remove']"
        style="margin-left: 8px"
      >删除</a-button>
      <a-button
        icon="download"
        @click="handleExport"
        v-hasPermi="['system:procurement:export']"
        style="margin-left: 8px"
      >导出</a-button>
      <div style="float: right;">
        <a-tooltip title="刷新">
          <a-button icon="reload" @click="getList" />
        </a-tooltip>
        <a-tooltip :title="showSearch ? '隐藏搜索' : '显示搜索'">
          <a-button :icon="showSearch ? 'up' : 'down'" @click="showSearch = !showSearch" style="margin-left: 8px" />
        </a-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :loading="loading"
      rowKey="id"
      :columns="columns"
      :data-source="procurementList"
      :row-selection="rowSelection"
      :pagination="false"
      :bordered="true">
      <span slot="procurementType" slot-scope="text">
        <a-tag :color="getProcurementTypeColor(text)">
          {{ getProcurementTypeText(text) }}
        </a-tag>
      </span>
      <span slot="urgencyLevel" slot-scope="text">
        <a-tag :color="getUrgencyLevelColor(text)">
          {{ getUrgencyLevelText(text) }}
        </a-tag>
      </span>
      <span slot="status" slot-scope="text">
        <a-tag :color="getStatusColor(text)">
          {{ getStatusText(text) }}
        </a-tag>
      </span>
      <span slot="action" slot-scope="text, record">
        <a @click="handleView(record)" v-hasPermi="['system:procurement:query']">查看</a>
        <a-divider type="vertical" v-if="record.status == 1" />
        <a @click="handleUpdate(record)" v-hasPermi="['system:procurement:edit']" v-if="record.status == 1">修改</a>
        <a-divider type="vertical" v-if="record.status == 1" />
        <a @click="handleSubmit(record)" v-hasPermi="['system:procurement:edit']" v-if="record.status == 1">提交</a>
        <a-divider type="vertical" v-if="record.status == 1" />
        <a @click="handleDelete(record)" v-hasPermi="['system:procurement:remove']" v-if="record.status == 1">删除</a>
      </span>
    </a-table>

    <!-- 分页 -->
    <a-pagination
      v-show="total > 0"
      :total="total"
      :current="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      show-size-changer
      show-quick-jumper
      :show-total="total => `共 ${total} 条`"
      @change="handlePageChange"
      @showSizeChange="handleSizeChange"
      style="margin-top: 16px; text-align: right;"
    />

    <!-- 新增/修改弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :width="1000"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
    >
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="申请单号" prop="appNo">
              <a-input v-model="form.appNo" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="申请标题" prop="title">
              <a-input v-model="form.title" placeholder="请输入申请标题" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="采购类型" prop="procurementType">
              <a-select v-model="form.procurementType" placeholder="请选择采购类型">
                <a-select-option value="OFFICE">办公用品</a-select-option>
                <a-select-option value="IT">IT设备</a-select-option>
                <a-select-option value="MATERIAL">原材料</a-select-option>
                <a-select-option value="SERVICE">服务</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="紧急程度" prop="urgencyLevel">
              <a-select v-model="form.urgencyLevel" placeholder="请选择紧急程度">
                <a-select-option :value="1">普通</a-select-option>
                <a-select-option :value="2">紧急</a-select-option>
                <a-select-option :value="3">特急</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="期望到货日期" prop="expectedDate">
              <a-date-picker
                v-model="form.expectedDate"
                placeholder="请选择期望到货日期"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="申请总金额" prop="totalAmount">
              <a-input v-model="form.totalAmount" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item label="申请理由" prop="reason">
          <a-textarea v-model="form.reason" placeholder="请输入申请理由" :rows="3" />
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-textarea v-model="form.remark" placeholder="请输入备注" :rows="2" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      title="采购申请详情"
      :visible="detailVisible"
      :width="1000"
      @cancel="detailVisible = false"
      :footer="null"
    >
      <a-descriptions :column="3" bordered>
        <a-descriptions-item label="申请单号">{{ detailData.appNo }}</a-descriptions-item>
        <a-descriptions-item label="申请标题">{{ detailData.title }}</a-descriptions-item>
        <a-descriptions-item label="申请人">{{ detailData.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="申请部门">{{ detailData.deptName }}</a-descriptions-item>
        <a-descriptions-item label="采购类型">
          <a-tag :color="getProcurementTypeColor(detailData.procurementType)">
            {{ getProcurementTypeText(detailData.procurementType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请总金额">{{ detailData.totalAmount }}</a-descriptions-item>
        <a-descriptions-item label="紧急程度">
          <a-tag :color="getUrgencyLevelColor(detailData.urgencyLevel)">
            {{ getUrgencyLevelText(detailData.urgencyLevel) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="期望到货日期">{{ detailData.expectedDate }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请理由" :span="3">{{ detailData.reason }}</a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">{{ detailData.remark }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>
<script
>
import { listProcurement, getProcurement, delProcurement, addProcurement, updateProcurement, generateAppNo, submitProcurement } from "@/api/system/procurement";

export default {
  name: "Procurement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购申请表格数据
      procurementList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appNo: null,
        title: null,
        applicantName: null,
        procurementType: null,
        status: null,
      },
      // 表格列定义
      columns: [
        {
          title: '申请单号',
          dataIndex: 'appNo',
          key: 'appNo',
          width: 150,
          align: 'center'
        },
        {
          title: '申请标题',
          dataIndex: 'title',
          key: 'title',
          width: 200,
          align: 'center'
        },
        {
          title: '申请人',
          dataIndex: 'applicantName',
          key: 'applicantName',
          width: 100,
          align: 'center'
        },
        {
          title: '部门',
          dataIndex: 'deptName',
          key: 'deptName',
          width: 120,
          align: 'center'
        },
        {
          title: '采购类型',
          dataIndex: 'procurementType',
          key: 'procurementType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'procurementType' }
        },
        {
          title: '申请总金额',
          dataIndex: 'totalAmount',
          key: 'totalAmount',
          width: 120,
          align: 'center'
        },
        {
          title: '紧急程度',
          dataIndex: 'urgencyLevel',
          key: 'urgencyLevel',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'urgencyLevel' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 弹窗相关
      modalVisible: false,
      modalTitle: '',
      modalLoading: false,
      detailVisible: false,
      // 表单数据
      form: {
        id: null,
        appNo: null,
        title: null,
        procurementType: null,
        urgencyLevel: 1,
        expectedDate: null,
        totalAmount: null,
        reason: null,
        remark: null
      },
      detailData: {},
      // 表单验证规则
      rules: {
        title: [
          { required: true, message: '请输入申请标题', trigger: 'blur' }
        ],
        procurementType: [
          { required: true, message: '请选择采购类型', trigger: 'change' }
        ],
        urgencyLevel: [
          { required: true, message: '请选择紧急程度', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入申请理由', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.ids,
        onChange: this.handleSelectionChange
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购申请列表 */
    getList() {
      this.loading = true;
      listProcurement(this.queryParams).then(response => {
        this.procurementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        appNo: null,
        title: null,
        applicantName: null,
        procurementType: null,
        status: null,
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selectedRowKeys) {
      this.ids = selectedRowKeys;
      this.single = selectedRowKeys.length !== 1;
      this.multiple = !selectedRowKeys.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.resetForm();
      generateAppNo().then(response => {
        this.form.appNo = response.data;
        this.modalTitle = '新增采购申请';
        this.modalVisible = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(record) {
      this.resetForm();
      const id = record ? record.id : this.ids[0];
      getProcurement(id).then(response => {
        this.form = { ...response.data };
        this.modalTitle = '修改采购申请';
        this.modalVisible = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(record) {
      const ids = record ? [record.id] : this.ids;
      this.$confirm({
        title: '确认删除',
        content: `是否确认删除选中的${ids.length}条采购申请？`,
        onOk: () => {
          delProcurement(ids).then(() => {
            this.$message.success('删除成功');
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info('导出功能开发中...');
    },
    /** 分页改变 */
    handlePageChange(page, pageSize) {
      this.queryParams.pageNum = page;
      this.queryParams.pageSize = pageSize;
      this.getList();
    },
    /** 分页大小改变 */
    handleSizeChange(current, size) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = size;
      this.getList();
    },
    /** 查看详情 */
    handleView(record) {
      getProcurement(record.id).then(response => {
        this.detailData = response.data;
        this.detailVisible = true;
      });
    },
    /** 提交申请 */
    handleSubmit(record) {
      this.$confirm({
        title: '确认提交',
        content: '是否确认提交该采购申请？',
        onOk: () => {
          submitProcurement(record).then(() => {
            this.$message.success('提交成功');
            this.getList();
          });
        }
      });
    },
    /** 弹窗确定 */
    handleModalOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.modalLoading = true;
          if (this.form.id) {
            updateProcurement(this.form).then(() => {
              this.$message.success('修改成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          } else {
            addProcurement(this.form).then(() => {
              this.$message.success('新增成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          }
        }
      });
    },
    /** 弹窗取消 */
    handleModalCancel() {
      this.modalVisible = false;
      this.resetForm();
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        appNo: null,
        title: null,
        procurementType: null,
        urgencyLevel: 1,
        expectedDate: null,
        totalAmount: null,
        reason: null,
        remark: null
      };
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
    /** 获取采购类型颜色 */
    getProcurementTypeColor(type) {
      const colorMap = {
        'OFFICE': 'blue',
        'IT': 'green',
        'MATERIAL': 'orange',
        'SERVICE': 'purple'
      };
      return colorMap[type] || 'default';
    },
    /** 获取采购类型文本 */
    getProcurementTypeText(type) {
      const textMap = {
        'OFFICE': '办公用品',
        'IT': 'IT设备',
        'MATERIAL': '原材料',
        'SERVICE': '服务'
      };
      return textMap[type] || type;
    },
    /** 获取紧急程度颜色 */
    getUrgencyLevelColor(level) {
      const colorMap = {
        1: 'default',
        2: 'orange',
        3: 'red'
      };
      return colorMap[level] || 'default';
    },
    /** 获取紧急程度文本 */
    getUrgencyLevelText(level) {
      const textMap = {
        1: '普通',
        2: '紧急',
        3: '特急'
      };
      return textMap[level] || level;
    },
    /** 获取状态颜色 */
    getStatusColor(status) {
      const colorMap = {
        1: 'default',    // 草稿
        2: 'processing', // 待审批
        3: 'processing', // 审批中
        4: 'success',    // 已通过
        5: 'error',      // 已拒绝
        6: 'cyan',       // 已采购
        7: 'blue',       // 已入库
        8: 'green',      // 已报销
        9: 'default'     // 已关闭
      };
      return colorMap[status] || 'default';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        1: '草稿',
        2: '待审批',
        3: '审批中',
        4: '已通过',
        5: '已拒绝',
        6: '已采购',
        7: '已入库',
        8: '已报销',
        9: '已关闭'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.table-operations {
  margin-bottom: 16px;
}
</style>