<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <a-form :model="queryParams" layout="inline" v-show="showSearch">
      <a-form-item label="供应商编码">
        <a-input
          v-model="queryParams.supplierCode"
          placeholder="请输入供应商编码"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="供应商名称">
        <a-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="供应商简称">
        <a-input
          v-model="queryParams.supplierShortName"
          placeholder="请输入供应商简称"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="供应商类型">
        <a-select v-model="queryParams.supplierType" placeholder="请选择供应商类型" allow-clear style="width: 120px">
          <a-select-option value="MANUFACTURER">生产商</a-select-option>
          <a-select-option value="DISTRIBUTOR">经销商</a-select-option>
          <a-select-option value="SERVICE">服务商</a-select-option>
          <a-select-option value="TRADER">贸易商</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="供应商等级">
        <a-select v-model="queryParams.supplierLevel" placeholder="请选择供应商等级" allow-clear style="width: 100px">
          <a-select-option value="A">A级</a-select-option>
          <a-select-option value="B">B级</a-select-option>
          <a-select-option value="C">C级</a-select-option>
          <a-select-option value="D">D级</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="信用等级">
        <a-select v-model="queryParams.creditRating" placeholder="请选择信用等级" allow-clear style="width: 100px">
          <a-select-option value="AAA">AAA级</a-select-option>
          <a-select-option value="AA">AA级</a-select-option>
          <a-select-option value="A">A级</a-select-option>
          <a-select-option value="BBB">BBB级</a-select-option>
          <a-select-option value="BB">BB级</a-select-option>
          <a-select-option value="B">B级</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="状态">
        <a-select v-model="queryParams.status" placeholder="请选择状态" allow-clear style="width: 100px">
          <a-select-option :value="1">正常</a-select-option>
          <a-select-option :value="2">暂停</a-select-option>
          <a-select-option :value="3">黑名单</a-select-option>
          <a-select-option :value="4">待审核</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" icon="search" @click="handleQuery">搜索</a-button>
        <a-button icon="reload" @click="resetQuery" style="margin-left: 8px">重置</a-button>
      </a-form-item>
    </a-form>

    <!-- 操作按钮 -->
    <div class="table-operations" style="margin-bottom: 16px;">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-hasPermi="['system:supplier:add']"
      >新增</a-button>
      <a-button
        icon="edit"
        :disabled="single"
        @click="handleUpdate"
        v-hasPermi="['system:supplier:edit']"
        style="margin-left: 8px"
      >修改</a-button>
      <a-button
        type="danger"
        icon="delete"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['system:supplier:remove']"
        style="margin-left: 8px"
      >删除</a-button>
      <a-button
        icon="download"
        @click="handleExport"
        v-hasPermi="['system:supplier:export']"
        style="margin-left: 8px"
      >导出</a-button>
      <div style="float: right;">
        <a-tooltip title="刷新">
          <a-button icon="reload" @click="getList" />
        </a-tooltip>
        <a-tooltip :title="showSearch ? '隐藏搜索' : '显示搜索'">
          <a-button :icon="showSearch ? 'up' : 'down'" @click="showSearch = !showSearch" style="margin-left: 8px" />
        </a-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :loading="loading"
      rowKey="id"
      :columns="columns"
      :data-source="supplierList"
      :row-selection="rowSelection"
      :pagination="false"
      :bordered="true">
      <span slot="supplierType" slot-scope="text">
        <a-tag :color="getSupplierTypeColor(text)">
          {{ getSupplierTypeText(text) }}
        </a-tag>
      </span>
      <span slot="creditRating" slot-scope="text">
        <a-tag :color="getCreditRatingColor(text)" v-if="text">
          {{ text }}
        </a-tag>
        <span v-else>-</span>
      </span>
      <span slot="status" slot-scope="text">
        <a-tag
          :color="getStatusColor(text)"
          :style="getStatusStyle(text)"
          class="status-tag"
        >
          {{ getStatusText(text) }}
        </a-tag>
      </span>
      <span slot="action" slot-scope="text, record">
        <a @click="handleView(record)" v-hasPermi="['system:supplier:query']">查看</a>
        <a-divider type="vertical" />
        <a @click="handleUpdate(record)" v-hasPermi="['system:supplier:edit']">修改</a>
        <a-divider type="vertical" />
        <a @click="handleDelete(record)" v-hasPermi="['system:supplier:remove']">删除</a>
      </span>
    </a-table>

    <!-- 分页 -->
    <a-pagination
      v-show="total > 0"
      :total="total"
      :current="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      show-size-changer
      show-quick-jumper
      :show-total="total => `共 ${total} 条`"
      @change="handlePageChange"
      @showSizeChange="handleSizeChange"
      style="margin-top: 16px; text-align: right;"
    />

    <!-- 新增/修改弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :width="1000"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
    >
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <a-divider orientation="left">基本信息</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="供应商编码" prop="supplierCode">
              <a-input v-model="form.supplierCode" placeholder="系统自动生成" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="供应商名称" prop="supplierName">
              <a-input v-model="form.supplierName" placeholder="请输入供应商名称" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="供应商简称">
              <a-input v-model="form.supplierShortName" placeholder="请输入供应商简称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="供应商类型" prop="supplierType">
              <a-select v-model="form.supplierType" placeholder="请选择供应商类型">
                <a-select-option value="MANUFACTURER">生产商</a-select-option>
                <a-select-option value="DISTRIBUTOR">经销商</a-select-option>
                <a-select-option value="SERVICE">服务商</a-select-option>
                <a-select-option value="TRADER">贸易商</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="供应商等级">
              <a-select v-model="form.supplierLevel" placeholder="请选择供应商等级">
                <a-select-option value="A">A级</a-select-option>
                <a-select-option value="B">B级</a-select-option>
                <a-select-option value="C">C级</a-select-option>
                <a-select-option value="D">D级</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态">
              <a-select v-model="form.status" placeholder="请选择状态">
                <a-select-option :value="1">正常</a-select-option>
                <a-select-option :value="2">暂停</a-select-option>
                <a-select-option :value="3">黑名单</a-select-option>
                <a-select-option :value="4">待审核</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item label="主营业务">
          <a-textarea v-model="form.mainBusiness" placeholder="请输入主营业务" :rows="2" />
        </a-form-model-item>

        <!-- 联系信息 -->
        <a-divider orientation="left">联系信息</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="联系人" prop="contactPerson">
              <a-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="手机号码">
              <a-input v-model="form.mobilePhone" placeholder="请输入手机号码" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="联系电话" prop="contactPhone">
              <a-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="传真号码">
              <a-input v-model="form.faxNumber" placeholder="请输入传真号码" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item label="联系邮箱" prop="contactEmail">
          <a-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
        </a-form-model-item>

        <!-- 地址信息 -->
        <a-divider orientation="left">地址信息</a-divider>
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="省份">
              <a-input v-model="form.province" placeholder="请输入省份" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="城市">
              <a-input v-model="form.city" placeholder="请输入城市" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="区县">
              <a-input v-model="form.district" placeholder="请输入区县" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="18">
            <a-form-model-item label="详细地址">
              <a-textarea v-model="form.address" placeholder="请输入详细地址" :rows="2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="邮政编码">
              <a-input v-model="form.postalCode" placeholder="邮政编码" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 企业信息 -->
        <a-divider orientation="left">企业信息</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="营业执照号">
              <a-input v-model="form.businessLicense" placeholder="请输入营业执照号" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="税号">
              <a-input v-model="form.taxNumber" placeholder="请输入税号" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="注册资本">
              <a-input-number v-model="form.registeredCapital" placeholder="注册资本" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="成立日期">
              <a-date-picker v-model="form.establishDate" placeholder="请选择成立日期" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 财务信息 -->
        <a-divider orientation="left">财务信息</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="银行账号">
              <a-input v-model="form.bankAccount" placeholder="请输入银行账号" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="开户银行">
              <a-input v-model="form.bankName" placeholder="请输入开户银行" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="信用等级">
              <a-select v-model="form.creditRating" placeholder="请选择信用等级" allow-clear>
                <a-select-option value="AAA">AAA级</a-select-option>
                <a-select-option value="AA">AA级</a-select-option>
                <a-select-option value="A">A级</a-select-option>
                <a-select-option value="BBB">BBB级</a-select-option>
                <a-select-option value="BB">BB级</a-select-option>
                <a-select-option value="B">B级</a-select-option>
                <a-select-option value="CCC">CCC级</a-select-option>
                <a-select-option value="CC">CC级</a-select-option>
                <a-select-option value="C">C级</a-select-option>
                <a-select-option value="D">D级</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="付款方式">
              <a-select v-model="form.paymentMethod" placeholder="请选择付款方式" allow-clear>
                <a-select-option value="CASH">现金</a-select-option>
                <a-select-option value="TRANSFER">转账</a-select-option>
                <a-select-option value="CHECK">支票</a-select-option>
                <a-select-option value="CREDIT">赊账</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 合作信息 -->
        <a-divider orientation="left">合作信息</a-divider>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="合作开始日期">
              <a-date-picker v-model="form.cooperationStartDate" placeholder="请选择合作开始日期" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="合作年限">
              <a-input-number v-model="form.cooperationYears" placeholder="合作年限" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="付款周期(天)">
              <a-input-number v-model="form.paymentCycle" placeholder="付款周期" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="发票类型">
              <a-select v-model="form.invoiceType" placeholder="请选择发票类型" allow-clear>
                <a-select-option value="NORMAL">普通发票</a-select-option>
                <a-select-option value="SPECIAL">专用发票</a-select-option>
                <a-select-option value="ELECTRONIC">电子发票</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="质量认证">
              <a-input v-model="form.qualityCertification" placeholder="请输入质量认证" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="环保认证">
              <a-input v-model="form.environmentCertification" placeholder="请输入环保认证" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item label="备注">
          <a-textarea v-model="form.remark" placeholder="请输入备注" :rows="3" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      title="供应商详情"
      :visible="detailVisible"
      :width="1000"
      @cancel="detailVisible = false"
      :footer="null"
    >
      <a-descriptions :column="3" bordered>
        <a-descriptions-item label="供应商编码">{{ detailData.supplierCode }}</a-descriptions-item>
        <a-descriptions-item label="供应商名称">{{ detailData.supplierName }}</a-descriptions-item>
        <a-descriptions-item label="供应商类型">
          <a-tag :color="getSupplierTypeColor(detailData.supplierType)">
            {{ getSupplierTypeText(detailData.supplierType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="联系人">{{ detailData.contactPerson || '-' }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ detailData.contactPhone || '-' }}</a-descriptions-item>
        <a-descriptions-item label="联系邮箱">{{ detailData.contactEmail || '-' }}</a-descriptions-item>
        <a-descriptions-item label="地址" :span="3">{{ detailData.address || '-' }}</a-descriptions-item>
        <a-descriptions-item label="营业执照号">{{ detailData.businessLicense || '-' }}</a-descriptions-item>
        <a-descriptions-item label="税号">{{ detailData.taxNumber || '-' }}</a-descriptions-item>
        <a-descriptions-item label="银行账号">{{ detailData.bankAccount || '-' }}</a-descriptions-item>
        <a-descriptions-item label="开户银行">{{ detailData.bankName || '-' }}</a-descriptions-item>
        <a-descriptions-item label="信用等级">
          <a-tag :color="getCreditRatingColor(detailData.creditRating)" v-if="detailData.creditRating">
            {{ detailData.creditRating }}
          </a-tag>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="合作年限">{{ detailData.cooperationYears || 0 }} 年</a-descriptions-item>
        <a-descriptions-item label="累计采购金额">{{ detailData.totalAmount || 0 }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag
            :color="getStatusColor(detailData.status)"
            :style="getStatusStyle(detailData.status)"
            class="status-tag"
          >
            {{ getStatusText(detailData.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ detailData.createTime }}</a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">{{ detailData.remark || '-' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>


  </div>
</template>

<style scoped>
/* 状态标签样式优化 */
.status-tag {
  min-width: 60px;
  text-align: center;
  font-size: 12px;
  font-weight: 600 !important;
  border-width: 1px !important;
  border-style: solid !important;
  border-radius: 4px !important;
  padding: 2px 8px !important;
  line-height: 1.5 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

/* 表格行悬停效果 */
::v-deep .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* 状态列特殊样式 */
::v-deep .ant-table-tbody > tr > td:nth-child(7) {
  padding: 8px 16px !important;
}
</style>

<script>
import { listSupplier, getSupplier, delSupplier, addSupplier, updateSupplier, generateSupplierCode } from "@/api/system/supplier";

export default {
  name: "SupplierInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商列表
      supplierList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierCode: null,
        supplierName: null,
        supplierShortName: null,
        supplierType: null,
        supplierLevel: null,
        creditRating: null,
        status: null,
      },
      // 表格列定义
      columns: [
        {
          title: '供应商编码',
          dataIndex: 'supplierCode',
          key: 'supplierCode',
          width: 120,
          align: 'center'
        },
        {
          title: '供应商名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          width: 180,
          align: 'center'
        },
        {
          title: '供应商简称',
          dataIndex: 'supplierShortName',
          key: 'supplierShortName',
          width: 120,
          align: 'center'
        },
        {
          title: '供应商类型',
          dataIndex: 'supplierType',
          key: 'supplierType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'supplierType' }
        },
        {
          title: '供应商等级',
          dataIndex: 'supplierLevel',
          key: 'supplierLevel',
          width: 100,
          align: 'center',
          customRender: (text) => text ? `${text}级` : '-'
        },
        {
          title: '联系人',
          dataIndex: 'contactPerson',
          key: 'contactPerson',
          width: 100,
          align: 'center'
        },
        {
          title: '联系电话',
          dataIndex: 'contactPhone',
          key: 'contactPhone',
          width: 120,
          align: 'center'
        },
        {
          title: '信用等级',
          dataIndex: 'creditRating',
          key: 'creditRating',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'creditRating' }
        },
        {
          title: '合作年限',
          dataIndex: 'cooperationYears',
          key: 'cooperationYears',
          width: 100,
          align: 'center',
          customRender: (text) => text ? `${text}年` : '-'
        },
        {
          title: '累计采购金额',
          dataIndex: 'totalAmount',
          key: 'totalAmount',
          width: 120,
          align: 'center'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],

      // 弹窗相关
      modalVisible: false,
      modalTitle: '',
      modalLoading: false,
      detailVisible: false,
      // 表单数据
      form: {
        id: null,
        supplierCode: null,
        supplierName: null,
        supplierShortName: null,
        supplierType: null,
        supplierLevel: null,
        mainBusiness: null,
        contactPerson: null,
        contactPhone: null,
        mobilePhone: null,
        contactEmail: null,
        faxNumber: null,
        address: null,
        province: null,
        city: null,
        district: null,
        postalCode: null,
        businessLicense: null,
        taxNumber: null,
        bankAccount: null,
        bankName: null,
        creditRating: null,
        registeredCapital: null,
        establishDate: null,
        cooperationStartDate: null,
        cooperationYears: 0,
        totalAmount: null,
        yearlyAmount: null,
        paymentMethod: null,
        paymentCycle: null,
        invoiceType: null,
        qualityCertification: null,
        environmentCertification: null,
        status: 1,
        remark: null
      },
      detailData: {},
      // 表单验证规则
      rules: {
        supplierName: [
          { required: true, message: '请输入供应商名称', trigger: 'blur' }
        ],
        supplierType: [
          { required: true, message: '请选择供应商类型', trigger: 'change' }
        ],
        contactPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.ids,
        onChange: this.handleSelectionChange
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询供应商列表 */
    getList() {
      this.loading = true;
      listSupplier(this.queryParams).then(response => {
        this.supplierList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        supplierCode: null,
        supplierName: null,
        supplierShortName: null,
        supplierType: null,
        supplierLevel: null,
        creditRating: null,
        status: null,
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selectedRowKeys) {
      this.ids = selectedRowKeys;
      this.single = selectedRowKeys.length !== 1;
      this.multiple = !selectedRowKeys.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.resetForm();
      generateSupplierCode().then(response => {
        this.form.supplierCode = response.data;
        this.modalTitle = '新增供应商';
        this.modalVisible = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(record) {
      this.resetForm();
      const id = record ? record.id : this.ids[0];
      getSupplier(id).then(response => {
        this.form = { ...response.data };
        this.modalTitle = '修改供应商';
        this.modalVisible = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(record) {
      const ids = record ? [record.id] : this.ids;
      this.$confirm({
        title: '确认删除',
        content: `是否确认删除选中的${ids.length}条供应商？`,
        onOk: () => {
          delSupplier(ids).then(() => {
            this.$message.success('删除成功');
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info('导出功能开发中...');
    },
    /** 分页改变 */
    handlePageChange(page, pageSize) {
      this.queryParams.pageNum = page;
      this.queryParams.pageSize = pageSize;
      this.getList();
    },
    /** 分页大小改变 */
    handleSizeChange(size) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = size;
      this.getList();
    },
    /** 查看详情 */
    handleView(record) {
      getSupplier(record.id).then(response => {
        this.detailData = response.data;
        this.detailVisible = true;
      });
    },

    /** 弹窗确定 */
    handleModalOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.modalLoading = true;
          if (this.form.id) {
            updateSupplier(this.form).then(() => {
              this.$message.success('修改成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          } else {
            addSupplier(this.form).then(() => {
              this.$message.success('新增成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          }
        }
      });
    },
    /** 弹窗取消 */
    handleModalCancel() {
      this.modalVisible = false;
      this.resetForm();
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        supplierCode: null,
        supplierName: null,
        supplierShortName: null,
        supplierType: null,
        supplierLevel: null,
        mainBusiness: null,
        contactPerson: null,
        contactPhone: null,
        mobilePhone: null,
        contactEmail: null,
        faxNumber: null,
        address: null,
        province: null,
        city: null,
        district: null,
        postalCode: null,
        businessLicense: null,
        taxNumber: null,
        bankAccount: null,
        bankName: null,
        creditRating: null,
        registeredCapital: null,
        establishDate: null,
        cooperationStartDate: null,
        cooperationYears: 0,
        totalAmount: null,
        yearlyAmount: null,
        paymentMethod: null,
        paymentCycle: null,
        invoiceType: null,
        qualityCertification: null,
        environmentCertification: null,
        status: 1,
        remark: null
      };
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
    /** 获取供应商类型颜色 */
    getSupplierTypeColor(type) {
      const colorMap = {
        'MANUFACTURER': 'blue',
        'DISTRIBUTOR': 'green',
        'SERVICE': 'orange',
        'TRADER': 'purple'
      };
      return colorMap[type] || 'default';
    },
    /** 获取供应商类型文本 */
    getSupplierTypeText(type) {
      const textMap = {
        'MANUFACTURER': '生产商',
        'DISTRIBUTOR': '经销商',
        'SERVICE': '服务商',
        'TRADER': '贸易商'
      };
      return textMap[type] || type;
    },
    /** 获取信用等级颜色 */
    getCreditRatingColor(rating) {
      const colorMap = {
        'AAA': 'green',
        'AA': 'green',
        'A': 'blue',
        'BBB': 'blue',
        'BB': 'orange',
        'B': 'orange',
        'CCC': 'red',
        'CC': 'red',
        'C': 'red',
        'D': 'volcano'
      };
      return colorMap[rating] || 'default';
    },
    /** 获取状态颜色 */
    getStatusColor(status) {
      const colorMap = {
        1: '#52c41a',    // 正常 - 绿色
        2: '#faad14',    // 暂停 - 橙色
        3: '#ff4d4f',    // 黑名单 - 红色
        4: '#1890ff'     // 待审核 - 蓝色
      };
      return colorMap[status] || '#d9d9d9';
    },
    /** 获取状态样式 */
    getStatusStyle(status) {
      const styleMap = {
        1: {
          backgroundColor: '#f6ffed',
          borderColor: '#52c41a',
          color: '#389e0d',
          fontWeight: '600'
        },
        2: {
          backgroundColor: '#fff7e6',
          borderColor: '#faad14',
          color: '#d46b08',
          fontWeight: '600'
        },
        3: {
          backgroundColor: '#fff2f0',
          borderColor: '#ff4d4f',
          color: '#cf1322',
          fontWeight: '600'
        },
        4: {
          backgroundColor: '#e6f7ff',
          borderColor: '#1890ff',
          color: '#0958d9',
          fontWeight: '600'
        }
      };
      return styleMap[status] || {
        backgroundColor: '#fafafa',
        borderColor: '#d9d9d9',
        color: '#595959',
        fontWeight: '600'
      };
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        1: '正常',
        2: '暂停',
        3: '黑名单',
        4: '待审核'
      };
      return textMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.table-operations {
  margin-bottom: 16px;
}
</style>