<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <a-form :model="queryParams" layout="inline" v-show="showSearch">
      <a-form-item label="模板名称">
        <a-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="模板代码">
        <a-input
          v-model="queryParams.templateCode"
          placeholder="请输入模板代码"
          allow-clear
          @pressEnter="handleQuery"
        />
      </a-form-item>
      <a-form-item label="业务类型">
        <a-select v-model="queryParams.businessType" placeholder="请选择业务类型" allow-clear style="width: 120px">
          <a-select-option value="PROCUREMENT">采购</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="是否激活">
        <a-select v-model="queryParams.isActive" placeholder="请选择状态" allow-clear style="width: 100px">
          <a-select-option :value="1">激活</a-select-option>
          <a-select-option :value="0">停用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" icon="search" @click="handleQuery">搜索</a-button>
        <a-button icon="reload" @click="resetQuery" style="margin-left: 8px">重置</a-button>
      </a-form-item>
    </a-form>

    <!-- 操作按钮 -->
    <div class="table-operations" style="margin-bottom: 16px;">
      <a-button
        type="primary"
        icon="plus"
        @click="handleAdd"
        v-hasPermi="['system:template:add']"
      >新增</a-button>
      <a-button
        icon="edit"
        :disabled="single"
        @click="handleUpdate"
        v-hasPermi="['system:template:edit']"
        style="margin-left: 8px"
      >修改</a-button>
      <a-button
        type="danger"
        icon="delete"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['system:template:remove']"
        style="margin-left: 8px"
      >删除</a-button>
      <a-button
        icon="download"
        @click="handleExport"
        v-hasPermi="['system:template:export']"
        style="margin-left: 8px"
      >导出</a-button>
      <div style="float: right;">
        <a-tooltip title="刷新">
          <a-button icon="reload" @click="getList" />
        </a-tooltip>
        <a-tooltip :title="showSearch ? '隐藏搜索' : '显示搜索'">
          <a-button :icon="showSearch ? 'up' : 'down'" @click="showSearch = !showSearch" style="margin-left: 8px" />
        </a-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :loading="loading"
      rowKey="id"
      :columns="columns"
      :data-source="templateList"
      :row-selection="rowSelection"
      :pagination="false"
      :bordered="true">
      <span slot="businessType" slot-scope="text">
        <a-tag color="blue">{{ getBusinessTypeText(text) }}</a-tag>
      </span>
      <span slot="procurementType" slot-scope="text">
        <a-tag :color="getProcurementTypeColor(text)" v-if="text">
          {{ getProcurementTypeText(text) }}
        </a-tag>
        <span v-else>-</span>
      </span>
      <span slot="isDefault" slot-scope="text">
        <a-tag :color="text ? 'green' : 'default'">
          {{ text ? '是' : '否' }}
        </a-tag>
      </span>
      <span slot="isActive" slot-scope="text">
        <a-tag :color="text ? 'success' : 'error'">
          {{ text ? '激活' : '停用' }}
        </a-tag>
      </span>
      <span slot="action" slot-scope="text, record">
        <a @click="handleView(record)" v-hasPermi="['system:template:query']">查看</a>
        <a-divider type="vertical" />
        <a @click="handleUpdate(record)" v-hasPermi="['system:template:edit']">修改</a>
        <a-divider type="vertical" />
        <a @click="handleSetDefault(record)" v-hasPermi="['system:template:edit']" v-if="!record.isDefault">设为默认</a>
        <a-divider type="vertical" v-if="!record.isDefault" />
        <a @click="handleChangeStatus(record)" v-hasPermi="['system:template:edit']">
          {{ record.isActive ? '停用' : '激活' }}
        </a>
        <a-divider type="vertical" />
        <a @click="handleDelete(record)" v-hasPermi="['system:template:remove']">删除</a>
      </span>
    </a-table>

    <!-- 分页 -->
    <a-pagination
      v-show="total > 0"
      :total="total"
      :current="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      show-size-changer
      show-quick-jumper
      :show-total="total => `共 ${total} 条`"
      @change="handlePageChange"
      @showSizeChange="handleSizeChange"
      style="margin-top: 16px; text-align: right;"
    />

    <!-- 新增/修改弹窗 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :width="1200"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
    >
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="模板名称" prop="templateName">
              <a-input v-model="form.templateName" placeholder="请输入模板名称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="模板代码" prop="templateCode">
              <a-input v-model="form.templateCode" placeholder="请输入模板代码" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="业务类型" prop="businessType">
              <a-select v-model="form.businessType" placeholder="请选择业务类型">
                <a-select-option value="PROCUREMENT">采购</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="采购类型">
              <a-select v-model="form.procurementType" placeholder="请选择采购类型" allow-clear>
                <a-select-option value="OFFICE">办公用品</a-select-option>
                <a-select-option value="IT">IT设备</a-select-option>
                <a-select-option value="MATERIAL">原材料</a-select-option>
                <a-select-option value="SERVICE">服务</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="最小金额">
              <a-input-number v-model="form.minAmount" placeholder="最小金额" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="最大金额">
              <a-input-number v-model="form.maxAmount" placeholder="最大金额" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="紧急程度">
              <a-select v-model="form.urgencyLevels" mode="multiple" placeholder="请选择紧急程度" allow-clear>
                <a-select-option value="1">普通</a-select-option>
                <a-select-option value="2">紧急</a-select-option>
                <a-select-option value="3">特急</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="排序">
              <a-input-number v-model="form.sortOrder" placeholder="排序" style="width: 100%" :min="0" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item label="模板描述">
          <a-textarea v-model="form.description" placeholder="请输入模板描述" :rows="3" />
        </a-form-model-item>
        <a-form-model-item label="备注">
          <a-textarea v-model="form.remark" placeholder="请输入备注" :rows="2" />
        </a-form-model-item>
        
        <!-- 审批步骤 -->
        <a-divider>审批步骤配置</a-divider>
        <div style="margin-bottom: 16px;">
          <a-button type="primary" icon="plus" @click="handleAddStep">添加步骤</a-button>
          <a-button type="danger" icon="delete" @click="handleDeleteStep" style="margin-left: 8px">删除步骤</a-button>
        </div>
        <a-table
          :data-source="stepList"
          :columns="stepColumns"
          :pagination="false"
          :row-selection="stepRowSelection"
          size="small"
          bordered
        >
          <template slot="stepNo" slot-scope="text, record, index">
            <a-input-number v-model="record.stepNo" :min="1" style="width: 80px" />
          </template>
          <template slot="stepName" slot-scope="text, record">
            <a-input v-model="record.stepName" placeholder="步骤名称" />
          </template>
          <template slot="stepType" slot-scope="text, record">
            <a-select v-model="record.stepType" style="width: 100px">
              <a-select-option value="APPROVAL">审批</a-select-option>
              <a-select-option value="NOTIFY">通知</a-select-option>
            </a-select>
          </template>
          <template slot="approverType" slot-scope="text, record">
            <a-select v-model="record.approverType" style="width: 100px">
              <a-select-option value="USER">用户</a-select-option>
              <a-select-option value="ROLE">角色</a-select-option>
              <a-select-option value="DEPT">部门</a-select-option>
              <a-select-option value="POLICY">策略</a-select-option>
            </a-select>
          </template>
          <template slot="approverValue" slot-scope="text, record">
            <a-input v-model="record.approverValue" placeholder="审批人值" />
          </template>
        </a-table>
      </a-form-model>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      title="审批模板详情"
      :visible="detailVisible"
      :width="1000"
      @cancel="detailVisible = false"
      :footer="null"
    >
      <a-descriptions :column="3" bordered>
        <a-descriptions-item label="模板名称">{{ detailData.templateName }}</a-descriptions-item>
        <a-descriptions-item label="模板代码">{{ detailData.templateCode }}</a-descriptions-item>
        <a-descriptions-item label="业务类型">
          <a-tag color="blue">{{ getBusinessTypeText(detailData.businessType) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="采购类型">
          <a-tag :color="getProcurementTypeColor(detailData.procurementType)" v-if="detailData.procurementType">
            {{ getProcurementTypeText(detailData.procurementType) }}
          </a-tag>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="最小金额">{{ detailData.minAmount || '-' }}</a-descriptions-item>
        <a-descriptions-item label="最大金额">{{ detailData.maxAmount || '-' }}</a-descriptions-item>
        <a-descriptions-item label="是否默认">
          <a-tag :color="detailData.isDefault ? 'green' : 'default'">
            {{ detailData.isDefault ? '是' : '否' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="是否激活">
          <a-tag :color="detailData.isActive ? 'success' : 'error'">
            {{ detailData.isActive ? '激活' : '停用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="排序">{{ detailData.sortOrder || 0 }}</a-descriptions-item>
        <a-descriptions-item label="模板描述" :span="3">{{ detailData.description || '-' }}</a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">{{ detailData.remark || '-' }}</a-descriptions-item>
      </a-descriptions>
      
      <a-divider>审批步骤</a-divider>
      <a-table
        :data-source="detailData.approvalStepTemplateList || []"
        :columns="detailStepColumns"
        :pagination="false"
        size="small"
        bordered
      />
    </a-modal>
  </div>
</template>

<script>
import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate, setDefaultTemplate, changeTemplateStatus } from "@/api/system/template";

export default {
  name: "ApprovalTemplate",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectedSteps: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板列表
      templateList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateCode: null,
        businessType: null,
        isActive: null,
      },
      // 表格列定义
      columns: [
        {
          title: '模板名称',
          dataIndex: 'templateName',
          key: 'templateName',
          width: 150,
          align: 'center'
        },
        {
          title: '模板代码',
          dataIndex: 'templateCode',
          key: 'templateCode',
          width: 120,
          align: 'center'
        },
        {
          title: '业务类型',
          dataIndex: 'businessType',
          key: 'businessType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'businessType' }
        },
        {
          title: '采购类型',
          dataIndex: 'procurementType',
          key: 'procurementType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'procurementType' }
        },
        {
          title: '金额范围',
          key: 'amountRange',
          width: 120,
          align: 'center',
          customRender: (text, record) => {
            const min = record.minAmount || 0;
            const max = record.maxAmount || '∞';
            return `${min} - ${max}`;
          }
        },
        {
          title: '是否默认',
          dataIndex: 'isDefault',
          key: 'isDefault',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'isDefault' }
        },
        {
          title: '是否激活',
          dataIndex: 'isActive',
          key: 'isActive',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'isActive' }
        },
        {
          title: '排序',
          dataIndex: 'sortOrder',
          key: 'sortOrder',
          width: 80,
          align: 'center'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 250,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 步骤表格列
      stepColumns: [
        {
          title: '步骤序号',
          dataIndex: 'stepNo',
          width: 100,
          scopedSlots: { customRender: 'stepNo' }
        },
        {
          title: '步骤名称',
          dataIndex: 'stepName',
          width: 150,
          scopedSlots: { customRender: 'stepName' }
        },
        {
          title: '步骤类型',
          dataIndex: 'stepType',
          width: 100,
          scopedSlots: { customRender: 'stepType' }
        },
        {
          title: '审批人类型',
          dataIndex: 'approverType',
          width: 100,
          scopedSlots: { customRender: 'approverType' }
        },
        {
          title: '审批人值',
          dataIndex: 'approverValue',
          width: 150,
          scopedSlots: { customRender: 'approverValue' }
        }
      ],
      // 详情步骤表格列
      detailStepColumns: [
        {
          title: '步骤序号',
          dataIndex: 'stepNo',
          width: 100,
          align: 'center'
        },
        {
          title: '步骤名称',
          dataIndex: 'stepName',
          width: 150,
          align: 'center'
        },
        {
          title: '步骤类型',
          dataIndex: 'stepType',
          width: 100,
          align: 'center',
          customRender: (text) => {
            const typeMap = {
              'APPROVAL': '审批',
              'NOTIFY': '通知'
            };
            return typeMap[text] || text;
          }
        },
        {
          title: '审批人类型',
          dataIndex: 'approverType',
          width: 100,
          align: 'center',
          customRender: (text) => {
            const typeMap = {
              'USER': '用户',
              'ROLE': '角色',
              'DEPT': '部门',
              'POLICY': '策略'
            };
            return typeMap[text] || text;
          }
        },
        {
          title: '审批人值',
          dataIndex: 'approverValue',
          width: 150,
          align: 'center'
        },
        {
          title: '审批策略',
          dataIndex: 'approverPolicy',
          width: 120,
          align: 'center'
        }
      ],
      // 弹窗相关
      modalVisible: false,
      modalTitle: '',
      modalLoading: false,
      detailVisible: false,
      // 表单数据
      form: {
        id: null,
        templateName: null,
        templateCode: null,
        businessType: 'PROCUREMENT',
        procurementType: null,
        minAmount: null,
        maxAmount: null,
        urgencyLevels: [],
        sortOrder: 0,
        description: null,
        remark: null
      },
      stepList: [],
      detailData: {},
      // 表单验证规则
      rules: {
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        templateCode: [
          { required: true, message: '请输入模板代码', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.ids,
        onChange: this.handleSelectionChange
      }
    },
    stepRowSelection() {
      return {
        selectedRowKeys: this.selectedSteps,
        onChange: this.handleStepSelectionChange
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询模板列表 */
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        this.templateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateCode: null,
        businessType: null,
        isActive: null,
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selectedRowKeys) {
      this.ids = selectedRowKeys;
      this.single = selectedRowKeys.length !== 1;
      this.multiple = !selectedRowKeys.length;
    },
    // 步骤选中数据
    handleStepSelectionChange(selectedRowKeys) {
      this.selectedSteps = selectedRowKeys;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.resetForm();
      this.modalTitle = '新增审批模板';
      this.modalVisible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(record) {
      this.resetForm();
      const id = record ? record.id : this.ids[0];
      getTemplate(id).then(response => {
        this.form = { ...response.data };
        this.stepList = response.data.approvalStepTemplateList || [];
        this.modalTitle = '修改审批模板';
        this.modalVisible = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(record) {
      const ids = record ? [record.id] : this.ids;
      this.$confirm({
        title: '确认删除',
        content: `是否确认删除选中的${ids.length}条审批模板？`,
        onOk: () => {
          delTemplate(ids).then(() => {
            this.$message.success('删除成功');
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info('导出功能开发中...');
    },
    /** 分页改变 */
    handlePageChange(page, pageSize) {
      this.queryParams.pageNum = page;
      this.queryParams.pageSize = pageSize;
      this.getList();
    },
    /** 分页大小改变 */
    handleSizeChange(current, size) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = size;
      this.getList();
    },
    /** 查看详情 */
    handleView(record) {
      getTemplate(record.id).then(response => {
        this.detailData = response.data;
        this.detailVisible = true;
      });
    },
    /** 设为默认 */
    handleSetDefault(record) {
      this.$confirm({
        title: '确认操作',
        content: '是否确认将该模板设为默认模板？',
        onOk: () => {
          setDefaultTemplate(record.id).then(() => {
            this.$message.success('设置成功');
            this.getList();
          });
        }
      });
    },
    /** 改变状态 */
    handleChangeStatus(record) {
      const status = record.isActive ? 0 : 1;
      const text = status ? '激活' : '停用';
      this.$confirm({
        title: '确认操作',
        content: `是否确认${text}该模板？`,
        onOk: () => {
          changeTemplateStatus(record.id, status).then(() => {
            this.$message.success(`${text}成功`);
            this.getList();
          });
        }
      });
    },
    /** 添加步骤 */
    handleAddStep() {
      this.stepList.push({
        stepNo: this.stepList.length + 1,
        stepName: '',
        stepType: 'APPROVAL',
        approverType: 'USER',
        approverValue: '',
        approverPolicy: '',
        sortOrder: this.stepList.length
      });
    },
    /** 删除步骤 */
    handleDeleteStep() {
      if (this.selectedSteps.length === 0) {
        this.$message.warning('请选择要删除的步骤');
        return;
      }
      this.stepList = this.stepList.filter((item, index) => !this.selectedSteps.includes(index));
      this.selectedSteps = [];
    },
    /** 弹窗确定 */
    handleModalOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.modalLoading = true;
          const formData = {
            ...this.form,
            urgencyLevels: this.form.urgencyLevels.join(','),
            approvalStepTemplateList: this.stepList
          };
          
          if (this.form.id) {
            updateTemplate(formData).then(() => {
              this.$message.success('修改成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          } else {
            addTemplate(formData).then(() => {
              this.$message.success('新增成功');
              this.modalVisible = false;
              this.getList();
            }).finally(() => {
              this.modalLoading = false;
            });
          }
        }
      });
    },
    /** 弹窗取消 */
    handleModalCancel() {
      this.modalVisible = false;
      this.resetForm();
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        templateName: null,
        templateCode: null,
        businessType: 'PROCUREMENT',
        procurementType: null,
        minAmount: null,
        maxAmount: null,
        urgencyLevels: [],
        sortOrder: 0,
        description: null,
        remark: null
      };
      this.stepList = [];
      this.selectedSteps = [];
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
    /** 获取业务类型文本 */
    getBusinessTypeText(type) {
      const textMap = {
        'PROCUREMENT': '采购'
      };
      return textMap[type] || type;
    },
    /** 获取采购类型颜色 */
    getProcurementTypeColor(type) {
      const colorMap = {
        'OFFICE': 'blue',
        'IT': 'green',
        'MATERIAL': 'orange',
        'SERVICE': 'purple'
      };
      return colorMap[type] || 'default';
    },
    /** 获取采购类型文本 */
    getProcurementTypeText(type) {
      const textMap = {
        'OFFICE': '办公用品',
        'IT': 'IT设备',
        'MATERIAL': '原材料',
        'SERVICE': '服务'
      };
      return textMap[type] || type;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.table-operations {
  margin-bottom: 16px;
}
</style>