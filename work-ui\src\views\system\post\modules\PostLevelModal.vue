<template>
  <a-modal
    :title="title"
    :width="1600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :maskClosable="false"
    :bodyStyle="{ padding: '24px' }"
    okText="保存"
    cancelText="取消"
  >
    <div class="level-manage-container">
      <!-- 岗位信息 -->
      <a-card class="post-info-card" :bordered="false">
        <div slot="title" class="card-title">
          <a-icon type="idcard" class="title-icon" />
          岗位信息
        </div>
        <a-descriptions :column="3" size="middle" bordered>
          <a-descriptions-item label="岗位名称">
            <a-tag color="blue" class="post-tag">{{ postInfo.postName }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="岗位编码">
            <span class="post-code">{{ postInfo.postCode }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="显示顺序">
            <a-badge :count="postInfo.postSort" :number-style="{ backgroundColor: '#52c41a' }" />
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 级别管理操作 -->
      <div class="level-operations">
        <div class="operations-left">
          <a-button type="primary" @click="handleAddLevel" icon="plus" size="large" class="add-btn">
            添加级别
          </a-button>
          <a-button @click="handleSortLevel" icon="sort-ascending" size="large" class="sort-btn">
            重新排序
          </a-button>
        </div>
        <div class="operations-right" v-if="levelList.length > 0">
          <a-tag color="geekblue">共 {{ levelList.length }} 个级别</a-tag>
        </div>
      </div>

      <!-- 级别列表 -->
      <a-card class="table-card" :bordered="false">
        <div slot="title" class="card-title">
          <a-icon type="team" class="title-icon" />
          级别配置
        </div>
        <a-table
          :columns="columns"
          :data-source="levelList"
          :pagination="false"
          size="middle"
          rowKey="tempId"
          class="level-table"
          :scroll="{ x: 1400 }"
          bordered
        >
        <span slot="levelOrder" slot-scope="text, record, index">
          <a-input-number
            v-model="record.levelOrder"
            :min="1"
            :max="99"
            size="small"
            style="width: 80px"
            @change="handleLevelOrderChange(record, index)"
            class="order-input"
          />
        </span>

        <span slot="levelCode" slot-scope="text, record">
          <a-input
            v-model="record.levelCode"
            placeholder="如：L1, L2"
            size="small"
            style="width: 120px"
            class="code-input"
          />
        </span>

        <span slot="levelName" slot-scope="text, record">
          <a-input
            v-model="record.levelName"
            placeholder="如：初级工程师"
            size="small"
            style="width: 150px"
            class="name-input"
          />
        </span>

        <span slot="baseSalary" slot-scope="text, record">
          <a-input-number
            v-model="record.baseSalary"
            :min="0"
            :precision="2"
            placeholder="0.00"
            size="small"
            style="width: 120px"
            @change="calculateTotalSalary(record)"
            class="salary-input"
            :formatter="value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/￥\s?|(,*)/g, '')"
          />
        </span>

        <span slot="performanceSalary" slot-scope="text, record">
          <a-input-number
            v-model="record.performanceSalary"
            :min="0"
            :precision="2"
            placeholder="0.00"
            size="small"
            style="width: 120px"
            @change="calculateTotalSalary(record)"
            class="salary-input"
            :formatter="value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/￥\s?|(,*)/g, '')"
          />
        </span>

        <span slot="allowance" slot-scope="text, record">
          <a-input-number
            v-model="record.allowance"
            :min="0"
            :precision="2"
            placeholder="0.00"
            size="small"
            style="width: 120px"
            @change="calculateTotalSalary(record)"
            class="salary-input"
            :formatter="value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/￥\s?|(,*)/g, '')"
          />
        </span>

        <span slot="totalSalary" slot-scope="text, record">
          <a-tag color="orange" class="total-salary-tag">
            <a-icon type="dollar" />
            {{ formatMoney(record.totalSalary) }}
          </a-tag>
        </span>

        <span slot="experience" slot-scope="text, record">
          <a-input-group compact class="experience-group">
            <a-input-number
              v-model="record.minExperience"
              :min="0"
              placeholder="最低"
              size="small"
              style="width: 60px"
              class="experience-input"
            />
            <a-input
              style="width: 30px; text-align: center; pointer-events: none; background: #f5f5f5"
              value="~"
              disabled
              size="small"
            />
            <a-input-number
              v-model="record.maxExperience"
              :min="record.minExperience || 0"
              placeholder="最高"
              size="small"
              style="width: 60px"
              class="experience-input"
            />
          </a-input-group>
          <div class="experience-unit">月</div>
        </span>

        <span slot="skillRequirements" slot-scope="text, record">
          <a-input
            v-model="record.skillRequirements"
            placeholder="如：Java、Spring、MySQL"
            size="small"
            style="width: 200px"
            class="skill-input"
          />
        </span>

        <span slot="status" slot-scope="text, record">
          <a-select v-model="record.status" size="small" style="width: 90px" class="status-select">
            <a-select-option value="0">
              <a-tag color="green" size="small">正常</a-tag>
            </a-select-option>
            <a-select-option value="1">
              <a-tag color="red" size="small">停用</a-tag>
            </a-select-option>
          </a-select>
        </span>

        <span slot="operation" slot-scope="text, record, index">
          <a-button 
            @click="handleDeleteLevel(index)" 
            type="danger" 
            size="small" 
            icon="delete"
            class="delete-btn"
          >
            删除
          </a-button>
        </span>
      </a-table>
      </a-card>

      <!-- 统计信息 -->
      <a-card class="level-summary-card" v-if="levelList.length > 0">
        <div slot="title" class="summary-title">
          <a-icon type="bar-chart" class="title-icon" />
          统计信息
        </div>
        <a-row :gutter="24" class="summary-stats">
          <a-col :span="6">
            <div class="stat-item">
              <a-statistic 
                title="级别总数" 
                :value="levelList.length" 
                suffix="个"
                :value-style="{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold' }"
              >
                <template #prefix>
                  <a-icon type="team" style="color: #1890ff" />
                </template>
              </a-statistic>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-item">
              <a-statistic 
                title="平均工资" 
                :value="averageSalary" 
                prefix="￥" 
                :precision="2"
                :value-style="{ color: '#722ed1', fontSize: '24px', fontWeight: 'bold' }"
              >
                <template #prefix>
                  <a-icon type="calculator" style="color: #722ed1" />
                </template>
              </a-statistic>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-item">
              <a-statistic 
                title="最低工资" 
                :value="minSalary" 
                prefix="￥" 
                :precision="2"
                :value-style="{ color: '#52c41a', fontSize: '24px', fontWeight: 'bold' }"
              >
                <template #prefix>
                  <a-icon type="arrow-down" style="color: #52c41a" />
                </template>
              </a-statistic>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-item">
              <a-statistic 
                title="最高工资" 
                :value="maxSalary" 
                prefix="￥" 
                :precision="2"
                :value-style="{ color: '#f5222d', fontSize: '24px', fontWeight: 'bold' }"
              >
                <template #prefix>
                  <a-icon type="arrow-up" style="color: #f5222d" />
                </template>
              </a-statistic>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { listPostLevelByPostId, batchSavePostLevels } from '@/api/system/postLevel'

export default {
  name: 'PostLevelModal',
  data() {
    return {
      title: '岗位级别管理',
      visible: false,
      confirmLoading: false,
      postInfo: {},
      levelList: [],
      tempIdCounter: 1,
      columns: [
        {
          title: '排序',
          dataIndex: 'levelOrder',
          width: 100,
          scopedSlots: { customRender: 'levelOrder' }
        },
        {
          title: '级别编码',
          dataIndex: 'levelCode',
          width: 140,
          scopedSlots: { customRender: 'levelCode' }
        },
        {
          title: '级别名称',
          dataIndex: 'levelName',
          width: 170,
          scopedSlots: { customRender: 'levelName' }
        },
        {
          title: '基础工资',
          dataIndex: 'baseSalary',
          width: 140,
          scopedSlots: { customRender: 'baseSalary' }
        },
        {
          title: '绩效工资',
          dataIndex: 'performanceSalary',
          width: 140,
          scopedSlots: { customRender: 'performanceSalary' }
        },
        {
          title: '津贴补助',
          dataIndex: 'allowance',
          width: 140,
          scopedSlots: { customRender: 'allowance' }
        },
        {
          title: '总工资',
          dataIndex: 'totalSalary',
          width: 120,
          scopedSlots: { customRender: 'totalSalary' }
        },
        {
          title: '经验要求(月)',
          dataIndex: 'minExperience',
          width: 160,
          scopedSlots: { customRender: 'experience' }
        },
        {
          title: '技能要求',
          dataIndex: 'skillRequirements',
          width: 220,
          scopedSlots: { customRender: 'skillRequirements' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          width: 80,
          scopedSlots: { customRender: 'operation' }
        }
      ]
    }
  },
  computed: {
    averageSalary() {
      if (this.levelList.length === 0) return 0
      const total = this.levelList.reduce((sum, item) => sum + (parseFloat(item.totalSalary) || 0), 0)
      return total / this.levelList.length
    },
    maxSalary() {
      if (this.levelList.length === 0) return 0
      return Math.max(...this.levelList.map(item => parseFloat(item.totalSalary) || 0))
    },
    minSalary() {
      if (this.levelList.length === 0) return 0
      return Math.min(...this.levelList.map(item => parseFloat(item.totalSalary) || 0))
    }
  },
  methods: {
    // 显示弹窗
    show(postInfo) {
      this.visible = true
      this.postInfo = { ...postInfo }
      this.title = `${postInfo.postName} - 级别管理`
      this.loadLevelList()
    },

    // 加载级别列表
    loadLevelList() {
      listPostLevelByPostId(this.postInfo.postId).then(response => {
        this.levelList = response.data.map(item => ({
          ...item,
          tempId: this.tempIdCounter++,
          baseSalary: parseFloat(item.baseSalary) || 0,
          performanceSalary: parseFloat(item.performanceSalary) || 0,
          allowance: parseFloat(item.allowance) || 0,
          totalSalary: parseFloat(item.totalSalary) || 0,
          minExperience: parseInt(item.minExperience) || 0,
          maxExperience: item.maxExperience ? parseInt(item.maxExperience) : null
        }))
      })
    },

    // 添加级别
    handleAddLevel() {
      const newLevel = {
        tempId: this.tempIdCounter++,
        postId: this.postInfo.postId,
        levelCode: '',
        levelName: '',
        levelOrder: this.levelList.length + 1,
        baseSalary: 0,
        performanceSalary: 0,
        allowance: 0,
        totalSalary: 0,
        minExperience: 0,
        maxExperience: null,
        skillRequirements: '',
        status: '0'
      }
      this.levelList.push(newLevel)
    },

    // 删除级别
    handleDeleteLevel(index) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这个级别吗？',
        onOk: () => {
          this.levelList.splice(index, 1)
          // 重新排序
          this.levelList.forEach((item, idx) => {
            item.levelOrder = idx + 1
          })
        }
      })
    },

    // 排序级别
    handleSortLevel() {
      this.levelList.sort((a, b) => a.levelOrder - b.levelOrder)
    },

    // 级别排序变化
    handleLevelOrderChange(record, index) {
      // 可以添加排序逻辑
    },

    // 计算总工资
    calculateTotalSalary(record) {
      const base = parseFloat(record.baseSalary) || 0
      const performance = parseFloat(record.performanceSalary) || 0
      const allowance = parseFloat(record.allowance) || 0
      record.totalSalary = base + performance + allowance
    },

    // 格式化金额
    formatMoney(amount) {
      return parseFloat(amount || 0).toLocaleString('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      })
    },

    // 验证数据
    validateData() {
      for (let i = 0; i < this.levelList.length; i++) {
        const item = this.levelList[i]
        if (!item.levelCode) {
          this.$message.error(`第${i + 1}行级别编码不能为空`)
          return false
        }
        if (!item.levelName) {
          this.$message.error(`第${i + 1}行级别名称不能为空`)
          return false
        }
        if (!item.levelOrder) {
          this.$message.error(`第${i + 1}行级别排序不能为空`)
          return false
        }
        if (item.baseSalary === null || item.baseSalary === undefined || item.baseSalary < 0) {
          this.$message.error(`第${i + 1}行基础工资不能为空且不能小于0`)
          return false
        }
        if (item.minExperience !== null && item.maxExperience !== null && item.minExperience > item.maxExperience) {
          this.$message.error(`第${i + 1}行最低经验要求不能大于最高经验要求`)
          return false
        }
      }

      // 检查级别编码是否重复
      const codes = this.levelList.map(item => item.levelCode)
      const uniqueCodes = [...new Set(codes)]
      if (codes.length !== uniqueCodes.length) {
        this.$message.error('级别编码不能重复')
        return false
      }

      return true
    },

    // 提交保存
    handleSubmit() {
      if (!this.validateData()) {
        return
      }

      this.confirmLoading = true
      
      // 准备保存数据
      const saveData = this.levelList.map(item => ({
        levelId: item.levelId || null,
        postId: this.postInfo.postId,
        levelCode: item.levelCode,
        levelName: item.levelName,
        levelOrder: item.levelOrder,
        baseSalary: item.baseSalary,
        performanceSalary: item.performanceSalary,
        allowance: item.allowance,
        totalSalary: item.totalSalary,
        minExperience: item.minExperience,
        maxExperience: item.maxExperience,
        skillRequirements: item.skillRequirements,
        status: item.status
      }))

      batchSavePostLevels(this.postInfo.postId, saveData).then(response => {
        this.$message.success('保存成功')
        this.confirmLoading = false
        this.visible = false
        this.$emit('ok')
      }).catch(() => {
        this.confirmLoading = false
      })
    },

    // 取消
    handleCancel() {
      this.visible = false
      this.levelList = []
      this.postInfo = {}
    }
  }
}
</script>

<style scoped>
.level-manage-container {
  max-height: 600px;
  overflow-y: auto;
  padding: 20px;
}

.post-info-card {
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.post-info-card .ant-descriptions-item-label {
  font-weight: 600;
  color: #262626;
}

.level-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.level-table {
  margin-bottom: 20px;
}

.total-salary {
  font-weight: bold;
  color: #1890ff;
  font-size: 14px;
}

.level-summary-card {
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
}

.summary-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #262626;
  font-size: 16px;
}

.summary-stats {
  padding: 16px 0;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

/* 新增样式 */
.order-input {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.order-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.code-input {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.code-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.name-input {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.name-input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.salary-input {
  border-radius: 4px;
  transition: all 0.3s;
}

.salary-input:focus {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.total-salary-tag {
  font-weight: 600;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
}

.experience-group {
  display: flex;
  align-items: center;
}

.experience-input {
  border-radius: 4px;
  transition: all 0.3s;
}

.experience-input:focus {
  border-color: #722ed1;
  box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);
}

.experience-unit {
  margin-left: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

.skill-input {
  border-radius: 4px;
  transition: all 0.3s;
}

.skill-input:focus {
  border-color: #13c2c2;
  box-shadow: 0 0 0 2px rgba(19, 194, 194, 0.2);
}

.status-select {
  border-radius: 4px;
}

.delete-btn {
  border-radius: 4px;
  transition: all 0.3s;
}

.delete-btn:hover {
  background-color: #ff7875;
  border-color: #ff7875;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #262626;
}

.title-icon {
  margin-right: 8px;
  color: #1890ff;
}

.post-tag {
  font-weight: 500;
}

.post-code {
  font-family: 'Courier New', monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.operations-left {
  display: flex;
  gap: 12px;
}

.add-btn {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.add-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.sort-btn {
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.sort-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.table-card {
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   border-radius: 8px;
 }

 .level-summary-card {
   margin-top: 20px;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   border-radius: 8px;
   background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
 }

 .summary-title {
   display: flex;
   align-items: center;
   font-weight: 600;
   color: #262626;
   font-size: 16px;
 }

 .summary-stats {
   padding: 16px 0;
 }

 .stat-item {
   text-align: center;
   padding: 16px;
   background: #ffffff;
   border-radius: 8px;
   box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
   transition: all 0.3s ease;
   border: 1px solid #f0f0f0;
 }

 .stat-item:hover {
   transform: translateY(-2px);
   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
   border-color: #d9d9d9;
 }
</style>